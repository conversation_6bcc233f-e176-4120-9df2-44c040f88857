body > div.document > div.sphinxsidebar > div > form > table > tbody > tr:nth-child(2) > td > select {
  width: 100%!important;
}

#python27 > a {
  color: white;
}

/* Carbon by BuySellAds */
#carbonads {
  display: block;
  overflow: hidden;
  margin: 1.5em 0 2em;
  padding: 1em;
  border: solid 1px #cccccc;
  border-radius: 2px;
  background-color: #eeeeee;
  text-align: center;
  line-height: 1.5;
}

#carbonads a {
  border-bottom: 0;
}

#carbonads span {
  display: block;
  overflow: hidden;
}

.carbon-img {
  display: block;
  margin: 0 auto 1em;
  text-align: center;
}

.carbon-text {
  display: block;
  margin-bottom: 1em;
}

.carbon-poweredby {
  display: block;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 10px;
  line-height: 1;
}


/* Native CPC by BuySellAds */

#native-ribbon #_custom_ {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  box-shadow: 0 -1px 4px 1px hsla(0, 0%, 0%, .15);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu,
    Cantarell, "Helvetica Neue", Helvetica, Arial, sans-serif;
  transition: all .25s ease-in-out;
  transform: translateY(calc(100% - 35px));

  flex-flow: column nowrap;
}

#native-ribbon #_custom_:hover {
  transform: translateY(0);
}

.native-img {
  margin-right: 20px;
  max-height: 50px;
  border-radius: 3px;
}

.native-sponsor {
  margin: 10px 20px;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: .5px;
  font-size: 12px;
  transition: all .3s ease-in-out;
  transform-origin: left;
}

#native-ribbon #_custom_:hover .native-sponsor {
  margin: 0 20px;
  opacity: 0;
  transform: scaleY(0);
}

.native-flex {
  display: flex;
  padding: 10px 20px 25px;
  text-decoration: none;

  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
}

.native-main {
  display: flex;

  flex-flow: row nowrap;
  align-items: center;
}

.native-details {
  display: flex;
  margin-right: 30px;

  flex-flow: column nowrap;
}

.native-company {
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 10px;
}

.native-desc {
  letter-spacing: 1px;
  font-weight: 300;
  font-size: 14px;
  line-height: 1.4;
}

.native-cta {
  padding: 10px 14px;
  border-radius: 3px;
  box-shadow: 0 6px 13px 0 hsla(0, 0%, 0%, .15);
  text-transform: uppercase;
  white-space: nowrap;
  letter-spacing: 1px;
  font-weight: 400;
  font-size: 12px;
  transition: all .3s ease-in-out;
  transform: translateY(-1px);
}

.native-cta:hover {
  box-shadow: none;
  transform: translateY(1px);
}

@media only screen and (min-width: 320px) and (max-width: 759px) {
  .native-flex {
    padding: 5px 5px 15px;
    flex-direction: column;

    flex-wrap: wrap;
  }

  .native-img {
    margin: 0;
    display: none;
  }

  .native-details {
    margin: 0;
  }

  .native-main {
    flex-direction: column;
    text-align: left;

    flex-wrap: wrap;
    align-content: center;
  }

  .native-cta {
    display: none;
  }
}
