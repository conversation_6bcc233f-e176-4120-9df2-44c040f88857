<?xml version="1.0" encoding="windows-1255"?>
<!--
Source: http://www.whatsup.org.il/backend.php
Expect: windows-1255
-->
<!DOCTYPE rss PUBLIC "-//Netscape Communications//DTD RSS 0.91//EN"
 "http://my.netscape.com/publish/formats/rss-0.91.dtd">

<rss version="0.91">

<channel>
<title>Whatsup - לינוקס, תוכנה חופשית וקוד פתוח בעברית</title>
<link>http://www.whatsup.co.il/</link>
<description>Whatsup - פורטל הלינוקס העברי שלך - בעברית !</description>
<language>he-il</language>
<image>
 <title>Whatsup - לינוקס, תוכנה חופשית וקוד פתוח בעברית</title>
 <url>http://www.whatsup.co.il/images/</url>
 <link>http://www.whatsup.co.il/</link>
</image>
<webMaster>admi&#110;&#064;&#119;hatsup.org.il</webMaster>
<item>
<title>הטמעת פייתון ביישומי C/C++</title>
<link>http://www.whatsup.co.il/modules.php?op=modload&amp;name=News&amp;file=article&amp;sid=4982</link>
<description>
פעמים רבות, כאשר כותבים יישומים גדולים (במיוחד בסביבת *nix) שפות היישום הן C ו-C++. לאחר שהיישום נבנה, הוא די סטטי. פעמים רבות נדרשות יכולות הרחבה מצד משתמשי קצה/צד שלישי, כגון: כתיבת תוספים ע&quot;י המשתמשים, הפעלה קוד מותאם עבור הלקוח באתר (בין אם נכתב ע&quot;י הלקוח או התמיכה) עם התרחשות אירועים שונים וכדו'.&lt;br /&gt;
&lt;br /&gt;
אם ניקח לדוגמא את מקרה פיירפוקס, תחום התוספים עבורו פורח גם הודות לקלות היחסית של יצירתם (&lt;a href=&quot;http://xulplanet.com&quot;&gt;XUL עם ג'אווהסקריפט&lt;/a&gt;). יצירת תוספים בשפות C/C++ יכולה להוות בעיה. על המשתמש להכיר את שפת הפיתוח (די מורכבת, במיוחד למשתמשים מהשורה), להתגבר על בעיות linking ושאר ירקות. מצד שני, בניית שפת תסריטים משלך אינו דבר פשוט ומועד לשגיאות.</description>
</item>
<item>
<title>אופן-אופיס בדרכים</title>
<link>http://www.whatsup.co.il/modules.php?op=modload&amp;name=News&amp;file=article&amp;sid=4981</link>
<description>
בהמשך למגמת יישומי חלונות הניתנים להפעלה מדיסק usb או התקנים שליפים אחרים (כגון: &lt;a href=&quot;article/3786&quot;&gt;Firefox&lt;/a&gt; ו-&lt;a href=&quot;article/3853&quot;&gt;Thunderbird&lt;/a&gt;) הוכרז  &lt;a href=&quot;http://portableapps.com/news/2006-01-03_-_portable_openoffice_2.0.1_released&quot;&gt;עדכון ל-Portable OpenOffice.org&lt;/a&gt;.&lt;br /&gt;
&lt;br /&gt;
העדכון כולל:&lt;br /&gt;
&lt;br /&gt;
- גרסה 2.0.1&lt;br /&gt;
- תמיכה מלאה ב-windows 2000&lt;br /&gt;
- תמיכה להגדרות תחת windows 98 ו-ME&lt;br /&gt;
- משגרים ליישומים הנפרדים (כגון writer, calc, impress וכו')</description>
</item>
<item>
<title>שוחרר קרנל 2.6.15</title>
<link>http://www.whatsup.co.il/modules.php?op=modload&amp;name=News&amp;file=article&amp;sid=4980</link>
<description>
יצאה גירסאת הקרנל הבאה 2.6.15, בדיוק 15 שנה לאחר הפעם הראשונה שלינוס הביא את המכונה שהריצה לינוקס ממש. &lt;br /&gt;
 &lt;a href=&quot;http://www.kernel.org/pub/linux/kernel/v2.6/ChangeLog-2.6.15&quot;&gt;רשימת השינויים&lt;/a&gt; כרגיל ארוכה וכוללת בעיקר תיקוני באגים ועידכון דרייברים.&lt;br /&gt;
&lt;br /&gt;
קישורים נוספים:&lt;br /&gt;
&lt;br /&gt;
ווטסאפ, &lt;a href=&quot;http://www.whatsup.co.il/modules.php?op=modload&amp;name=News&amp;file=article&amp;sid=4854&quot;&gt;ויקי כתחליף ל-changelog&lt;/a&gt;, הערה, האתר לא עובד כעת. כנראה בגלל עומס.</description>
</item>
<item>
<title>hocr 0.7.0 שוחרר</title>
<link>http://www.whatsup.co.il/modules.php?op=modload&amp;name=News&amp;file=article&amp;sid=4979</link>
<description>
שוחררה גרסה 0.7.0 של hocr.&lt;br /&gt;
&lt;br /&gt;
hocr היא ספרית C/C++ לצורך זיהוי אותיות אופטי עברי. זו הגירסה הראשונה המשוחררת מאד הודיע איגוד האינטרנט הישראלי על רצונו להעניק סיוע לפרוייקט. תכונות המערכת:&lt;br /&gt;
&lt;br /&gt;
1. קריאת טקסטים סרוקים בעברית&lt;br /&gt;
2. קריאה והבנת ניקוד&lt;br /&gt;
3. קישורים לשפות C++ פיטון ופרל&lt;br /&gt;
4. מתמשקת בקלות עם מבנה התמונה המשמשים ב QT ו GTK&lt;br /&gt;
5. מנשקים ל GTK , QT ולשורת הפקודה.&lt;br /&gt;
&lt;br /&gt;
רשימת השינויים כוללת:&lt;br /&gt;
&lt;br /&gt;
- קישור לשפות C++ פייטון ופרל&lt;br /&gt;
- שיפורים במנשק הגרפי של GTK&lt;br /&gt;
- מדריכים בעברית&lt;br /&gt;
&lt;br /&gt;
&lt;b&gt;קישורים&lt;/b&gt;:&lt;br /&gt;
&lt;br /&gt;
&lt;a href=&quot;http://hocr.berlios.de/screenshots.html&quot;&gt;תמונות מסך&lt;/a&gt;,&lt;br /&gt;
&lt;a href=&quot;http://hocr.berlios.de/index.html&quot;&gt;דף הבית של הפרוייקט&lt;/a&gt;,&lt;br /&gt;
&lt;a href=&quot;http://hocr.berlios.de/wiki/doku.php&quot;&gt;מדריכים בעברית&lt;/a&gt;,</description>
</item>
<item>
<title>מה עשית בשנת 2005 עבור הקוד הפתוח?</title>
<link>http://www.whatsup.co.il/modules.php?op=modload&amp;name=News&amp;file=article&amp;sid=4978</link>
<description>
הקוד הפתוח בישראל נשען כמעט אך ורק על מתנדבים, ובזה כוחנו כקהילה. אם הרגשת שבשנה היוצאת לא עשית מספיק עבור הקוד הפתוח בישראל, תוכל/י לתקן זאת ב- 2006.&lt;br /&gt;
&lt;br /&gt;
להלן רשימה של פרויקטים שצריכים את עזרתך, היום ועכשיו. רובם לא מצריכים יכולת תכנות או השקעת זמן יוצאת דופן – כלומר הם יכולים להיעשות על ידך.&lt;br /&gt;
&lt;br /&gt;
</description>
</item>
<item>
<title>פתיחת העונה בתלוקס: הרצאה על vim ב-8 בינואר</title>
<link>http://www.whatsup.co.il/modules.php?op=modload&amp;name=News&amp;file=article&amp;sid=4977</link>
<description>
&lt;p&gt;
&lt;a href=&quot;http://www.cs.tau.ac.il/telux/&quot;&gt;מועדון הלינוקס התל-אביבי (תלוקס)&lt;/a&gt; 
פותח את עונת ההרצאות הרגילות עם הרצאה של שגיב ברהום על 
&lt;a href=&quot;http://www.vim.org/&quot;&gt;עורך הטקסט Vim&lt;/a&gt;.
ההרצאה תערך ביום ראשון, 8 בינואר 2006, בשעה 18:30, בחדר 008 בבניין שרייבר באוניברסיטת תל-אביב. פרטים נוספים ניתן למצוא 
&lt;a href=&quot;http://www.cs.tau.ac.il/telux/&quot;&gt;באתר&lt;/a&gt;.
&lt;/p&gt;
</description>
</item>
<item>
<title>OpenOffice 2.0 בעברית</title>
<link>http://www.whatsup.co.il/modules.php?op=modload&amp;name=News&amp;file=article&amp;sid=4976</link>
<description>
לאחר המתנה ארוכה, תשוחרר עם תחילת השנה האזרחית החדשה, גירסת אופן אופיס 2 בעיברית. הגירסה הבאה אשר תבוסס על מהדורה 2.02 אשר בה תיקוני בגים, תשוחרר לקראת חודש מרץ.&lt;br /&gt;
&lt;br /&gt;
פרטים נוספים לגבי מועדי השיחרור ניתן למצוא באתרי אופן אופיס:&lt;br /&gt;
&lt;a href=&quot;http://www.openoffice.org.il/&quot;&gt;אתר אופן אופיס הישראלי&lt;/a&gt;&lt;br /&gt;
&lt;a href=&quot;http://www.openoffice.org/&quot;&gt;אתר אופן אופיס הראשי&lt;/a&gt;&lt;br /&gt;
</description>
</item>
<item>
<title>וואלה! מתרחבת עם &quot;וואלה! פדיה&quot;</title>
<link>http://www.whatsup.co.il/modules.php?op=modload&amp;name=News&amp;file=article&amp;sid=4975</link>
<description>
אתר האינטרנט &quot;וואלה!&quot; פותח היום את &quot;וואלה! פדיה&quot; - אתר שמהווה שיתוף פעולה עם אתר &quot;ויקיפדיה&quot; הישראלי.&lt;br /&gt;
וואלה! יציגו ערכים רלוונטים מתוך &quot;וואלה! פדיה&quot; בתוך תוצאות החיפוש וכן יאפשרו לגולשים לקרוא תוכן מאתר &quot;ויקיפדיה&quot; בתוך שער הכניסה שלהם.&lt;br /&gt;
במסגרת השיתוף פעולה, &quot;וואלה!&quot; יעבירו חלק מנתחי ההכנסות שלהם לויקיפדיה וכן יתרמו תוכן לאתר.&lt;br /&gt;
&lt;br /&gt;
באמצעות אתר זה, מקווים ב&quot;וואלה!&quot; להתחרות בשרות האינצקלופדיה של אתר Ynet ובאתר גוגל שלאחרונה פתח סניף בישראל.&lt;br /&gt;
&lt;br /&gt;
מקור: &lt;a href=&quot;http://www.haaretz.co.il/captain/pages/ShArtCaptain.jhtml?contrassID=11&amp;subContrassID=0&amp;itemNo=663887&quot;&gt;וואלה מצטרפת לויקיפדיה בוואלה פדיה&lt;/a&gt;, הארץ.&lt;br /&gt;
לאתר &lt;a href=&quot;http://pedia.walla.co.il/&quot;&gt;וואלה-פדיה&lt;/a&gt;</description>
</item>
<item>
<title>הזיקית בישראל</title>
<link>http://www.whatsup.co.il/modules.php?op=modload&amp;name=News&amp;file=article&amp;sid=4974</link>
<description>
&lt;a href=&quot;http://www.chameleon.org.il/index.html&quot;&gt;Chameleon&lt;/a&gt; (זיקית) הוא האתר המרכז את הפעילות הקהילתית של נובל וסוזה לינוקס בישראל.&lt;br /&gt;
&lt;br /&gt;
תוכלו למצוא שם מדריכים, פורום תמיכה וחנות מוצרים. עוד באתר קישורים להורדות, קורסים, מצב וניתוח פיננסי של נובל.&lt;br /&gt;
&lt;br /&gt;
מפריע מעט השימוש ביישומוני ג'אווה שהופך חלקים מהאתר לא נגישים (לאלה שאין להם את התוסף) או גורמים לאיטיות ועומס של 100% על המעבד (לפחות אצלי על dapper ופיירפוקס  1.5) במעבר לחלק מהעמודים.</description>
</item>
<item>
<title>חברת DELL החלה לצרף את פיירפוקס  לצד האקספלורר בבריטניה ואוסטרליה</title>
<link>http://www.whatsup.co.il/modules.php?op=modload&amp;name=News&amp;file=article&amp;sid=4973</link>
<description>
NRG מדווחים כי &lt;a href=&quot;http://www.nrg.co.il/online/10/ART1/026/334.html&quot;&gt;דל החלה להתקין בצורה שקטה את פיירפוקס&lt;/a&gt; במקביל ל-ל-IE (אשר נשאר דפדפן ברירת המחדל):&lt;br /&gt;
&lt;br /&gt;
&quot;...הפצת  פיירפוקס במחשבי החברה לא לוותה בהודעה רשמית. לקוחות  החברה הופתעו לגלות את האייקון של פיירפוקס על שולחן העבודה של מחשביהם החדשים, בסמוך לזה של אקספלורר. בלייק רוס, איש קרן מוזילה ואחד מיוצרי פיירפוקס, היה הראשון לאשר  את הידיעה בבלוגו. בהמשך הודיעו גורמים ב-DELL שהחבילה המכילה את פיירפוקס תופץ כרגע רק בבריטניה, ולאחר מכן באוסטרליה. לדברי אנשי החברה, אין הכוונה להרחיב את הפצת החבילה לאזורים אחרים בעולם...&quot;</description>
</item>
</channel>
</rss>
