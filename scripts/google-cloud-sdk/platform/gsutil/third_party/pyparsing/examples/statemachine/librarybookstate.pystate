#
# librarybookstate.pystate
#
# This state machine models the state of books in a library.
#

statemachine BookState:
    New -(shelve)-> Available
    Available -(reserve)-> OnHold
    OnHold -(release)-> Available
    Available -(checkout)-> CheckedOut
    CheckedOut -(checkin)-> Available

    # add states for restricted books
    New -(restrict)-> Restricted
    Available -(restrict)-> Restricted
    Restricted -(release)-> Available
    Restricted -(checkout)-> CheckedOutRestricted
    CheckedOutRestricted -(checkin)-> Restricted
