release_tracks: [GA, BETA, ALPHA]
help_text:
  brief: Add IAM policy binding to a subscription.
  description: |
    Add an IAM policy binding to a Cloud Pub/Sub Subscription.

  examples: |
    To add an IAM policy binding with the role of 'roles/editor' for the user '<EMAIL>'
    on the subscription 'my-subscription', run:

      $ {command} my-subscription --member='user:<EMAIL>' --role='roles/editor'

    To add an IAM policy binding with the role of 'roles/editor' for the service account
    '<EMAIL>' on the subscription 'my-subscription', run:

      $ {command} my-subscription \
          --member='serviceAccount:<EMAIL>' \
          --role='roles/editor'

    See https://cloud.google.com/iam/docs/managing-policies for details of
    policy role and member types.

request:
  collection: pubsub.projects.subscriptions

arguments:
  resource:
    help_text: The subscription to add the IAM policy binding.
    spec: !REF googlecloudsdk.command_lib.pubsub.resources:subscription

ALPHA:
  help_text:
    brief: Add IAM policy binding to a subscription.
    description: |
      Add an IAM policy binding to the IAM policy of a Cloud Pub/Sub Subscription. One binding consists of a member,
      a role, and an optional condition.

    examples: |
      To add an IAM policy binding with the role of 'roles/editor' for the user
      '<EMAIL>' on the subscription 'my-subscription', run:

        $ {command} my-subscription --member='user:<EMAIL>' --role='roles/editor'

      To add an IAM policy binding with the role of 'roles/editor' for the service account
      '<EMAIL>' on the subscription 'my-subscription', run:

        $ {command} my-subscription \
            --member='serviceAccount:<EMAIL>' \
            --role='roles/editor'

      To add an IAM policy binding which expires at the end of the year 2018 for the role of
      'roles/pubsub.subscriber' and the user '<EMAIL>' with subscription 'my-subscription', run:

        $ {command} my-subscription --member='user:<EMAIL>' --role='roles/pubsub.subscriber' --condition='expression=request.time < timestamp("2019-01-01T00:00:00Z"),title=expires_end_of_2018,description=Expires at midnight on 2018-12-31'

      See https://cloud.google.com/iam/docs/managing-policies for details of
      policy role and member types.

  iam:
    enable_condition: true
