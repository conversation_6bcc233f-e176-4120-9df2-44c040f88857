release_tracks: [ALPHA, BETA, GA]
help_text:
  brief: List Pub/Sub Lite reservations.
  description: List Pub/Sub Lite reservations.
  examples: |
    To list Pub/Sub Lite reservations, run:

        $ {command} --location=us-central1 \
            --limit=5

request:
  collection: pubsublite.admin.projects.locations.reservations
  method: list
  modify_request_hooks:
  - googlecloudsdk.command_lib.pubsub.lite_util:UpdateAdminRequest

arguments:
  resource:
    help_text: Location to list reservations for.
    spec: !REF googlecloudsdk.command_lib.pubsub.resources:location
