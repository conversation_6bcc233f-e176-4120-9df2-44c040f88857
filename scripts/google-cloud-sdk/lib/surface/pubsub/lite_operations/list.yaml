release_tracks: [ALPHA, BETA, GA]
help_text:
  brief: List Pub/Sub Lite operations.
  description: |
    List Pub/Sub Lite operations.

    The optional `--subscription` flag can be used to filter operations for a Pub/Sub Lite
    subscription. The optional `--done` flag can be used to filter by operation completion status.
    These flags are ignored if `--filter` is set.

    To describe a listed operation, run:

        $ {command} operation-id --location=us-central1-a
  examples: |
    To list Pub/Sub Lite operations, run:

        $ {command} --location=us-central1-a --limit=50

    To list incomplete Pub/Sub Lite operations, run:

        $ {command} --location=us-central1-a --done=false

    To list Pub/Sub Lite operations for a subscription, run:

        $ {command} --location=us-central1-a --subscription=my-subscription --limit=50

    To list incomplete Pub/Sub Lite operations for a subscription, run:

        $ {command} --location=us-central1-a --subscription=my-subscription --done=false

request:
  collection: pubsublite.admin.projects.locations.operations
  method: list
  modify_request_hooks:
  - googlecloudsdk.command_lib.pubsub.lite_util:UpdateAdminRequest
  - googlecloudsdk.command_lib.pubsub.lite_util:UpdateListOperationsFilter

arguments:
  resource:
    help_text: Location to list operations for.
    spec: !REF googlecloudsdk.command_lib.pubsub.resources:location
  params:
  - _REF_: googlecloudsdk.command_lib.pubsub.flags:operation-done
  - _REF_: googlecloudsdk.command_lib.pubsub.flags:operation-subscription

output:
  format: |
    table(
        name.scope("operations"):label=OPERATION_ID,
        metadata.target:label=TARGET,
        metadata.createTime:label=CREATE_TIME,
        done:label=DONE,
        error.code:label=ERROR_CODE,
        error.message:label=MESSAGE
    )
