- release_tracks: [ALPHA]

  help_text:
    brief: Get the IAM policy for a project.
    description: |
      *{command}* displays the IAM policy associated with a project.
      If formatted as JSON, the output can be edited and used as
      a policy file for *set-iam-policy*. The output includes an "etag"
      field identifying the version emitted and allowing detection of
      concurrent policy updates; see
      $ {parent} set-iam-policy for additional details.
    examples: |
      To print the IAM policy for a given project, run:

        $ {command} my-project

  request:
    collection: cloudresourcemanager.projects
    use_relative_name: false

  arguments:
    resource:
      help_text: The project for which to display the IAM policy.
      spec: !REF googlecloudsdk.command_lib.projects.resources:project

  iam:
    policy_version: 3
    get_iam_policy_version_path: getIamPolicyRequest.options.requestedPolicyVersion
