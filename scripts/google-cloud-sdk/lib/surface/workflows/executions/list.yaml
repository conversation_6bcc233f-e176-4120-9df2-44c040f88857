- release_tracks: [ALPHA, BETA, GA]
  help_text:
    brief: |
      List workflow executions
    description: |
      List workflow executions for a given workflow.
    examples: |
      To list executions of a workflow named 'my-workflow', run:

          $ {command} my-workflow

  arguments:
    resource:
      spec: !REF googlecloudsdk.command_lib.workflows.resources:workflow_for_executions
      is_positional: true
      help_text: |
        The workflow name.

  request:
    ALPHA:
      api_version: v1alpha1
    BETA:
      api_version: v1beta
    GA:
      api_version: v1
    collection: workflowexecutions.projects.locations.workflows.executions
    modify_request_hooks:
    - googlecloudsdk.command_lib.workflows.hooks:print_default_location_warning

  response:
    id_field: name

  output:
    format: |
      table(
        name,
        state,
        startTime,
        endTime
      )
