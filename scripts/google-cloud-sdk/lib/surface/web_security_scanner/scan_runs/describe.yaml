# Copyright 2019 Google LLC. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

- release_tracks: [ALPHA]

  help_text:
    brief: Describe a scan run.
    description: Describe a scan run.
    examples: |
      The following commands describe the given scan run:

        $ {command} SCAN_RUN --scan-config=SCAN_CONFIG

        $ {command} SCAN_RUN

  request:
    collection: websecurityscanner.projects.scanConfigs.scanRuns
    api_version: v1beta

  arguments:
    resource:
      help_text: Scan run to describe.
      spec: !REF googlecloudsdk.command_lib.web_security_scanner.resources:scan_run
      is_positional: true
