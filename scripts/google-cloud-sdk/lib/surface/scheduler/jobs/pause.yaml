- help_text:
    brief: Pause the execution of a job.
    description: Pause the execution of a job.
    examples: |
      The following command pauses the execution of a job:

        $ {command} my-job

  request:
    collection: cloudscheduler.projects.locations.jobs
    method: pause
    static_fields:
      pauseJobRequest: {}

  arguments:
    resource:
      help_text: The job to pause.
      spec: !REF googlecloudsdk.command_lib.scheduler.resources:job

  response:
    modify_response_hooks:
    - googlecloudsdk.command_lib.scheduler.util:LogPauseSuccess

  output:
    format: none
