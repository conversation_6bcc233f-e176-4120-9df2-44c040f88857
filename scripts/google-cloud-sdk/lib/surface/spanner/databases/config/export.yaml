release_tracks: [ALPHA]
command_type: CONFIG_EXPORT
help_text:
  brief: Export the configuration for a Spanner database.
  description: |
    *{command}* exports the configuration for a Spanner database.

    Database configurations can be exported in
    Kubernetes Resource Model (krm) or Terraform HCL formats. The
    default format is `krm`.

    Specifying `--all` allows you to export the configurations for all
    databases within the project.

    Specifying `--path` allows you to export the configuration(s) to
    a local directory.
  examples: |
    To export the configuration for a database, run:

      $ {command} my-database

    To export the configuration for a database to a file, run:

      $ {command} my-database --path=/path/to/dir/

    To export the configuration for a database in Terraform
    HCL format, run:

      $ {command} my-database --resource-format=terraform

    To export the configurations for all databases within a
    project, run:

      $ {command} --all
arguments:
  resource:
    help_text: Database to export the configuration for.
    spec: !REF googlecloudsdk.command_lib.spanner.resources:database
