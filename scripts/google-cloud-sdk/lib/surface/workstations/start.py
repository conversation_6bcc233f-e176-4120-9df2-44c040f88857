# -*- coding: utf-8 -*- #
# Copyright 2022 Google LLC. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Command for starting Workstations."""

from __future__ import absolute_import
from __future__ import division
from __future__ import unicode_literals

from googlecloudsdk.api_lib.workstations import workstations
from googlecloudsdk.calliope import base
from googlecloudsdk.command_lib.workstations import flags as workstations_flags


@base.ReleaseTracks(
    base.ReleaseTrack.GA, base.ReleaseTrack.BETA, base.ReleaseTrack.ALPHA
)
class Start(base.Command):
  """Start a workstation.

  Start a workstation.

  ## EXAMPLES

    To start a workstation, run

      $ {command} WORKSTATION
  """

  @staticmethod
  def Args(parser):
    workstations_flags.AddAsyncFlag(parser)
    workstations_flags.AddWorkstationResourceArg(parser)

  def Collection(self):
    return 'workstations.projects.locations.workstationClusters.workstationConfigs.workstations'

  def Run(self, args):
    client = workstations.Workstations(self.ReleaseTrack())
    response = client.Start(args)
    return response
