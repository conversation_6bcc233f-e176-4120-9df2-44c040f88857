- release_tracks: [ALPHA]
  command_type: UPDATE
  help_text:
    brief: |
      Sets the feature policy for an instance.
    description: |
      Sets the feature policy for a Remote Build Execution instance, which will control which RBE
      execution features can be used with commands run against that instance.
    examples: |
      The following sets a simple feature policy for an instance called 'default_instance':

        $ {command} default_instance --linux-isolation=gvisor --docker-privileged=forbidden --docker-runtime=restricted --docker-runtime-allowlist=runc,runsc

  request:
    collection: remotebuildexecution.projects.instances

  async:
    collection: remotebuildexecution.projects.operations

  arguments:
    resource:
      spec: !REF googlecloudsdk.command_lib.remote_build_execution.resources:instance
      help_text: |
        Arguments describing the feature policy to set.
    params:
    - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerPrivileged.policy
      arg_name: docker-privileged
      required: false
      choices:
      - arg_value: allowed
        enum_value: allowed
        help_text: |
          dockerPrivileged can be used.
      - arg_value: forbidden
        enum_value: forbidden
        help_text: |
          dockerPrivileged cannot be used.
      help_text: |
        Whether dockerPrivileged can be used. If unspecified, the default is equivalent to
        "forbidden".
    - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerRunAsRoot.policy
      arg_name: docker-run-as-root
      required: false
      choices:
      - arg_value: allowed
        enum_value: allowed
        help_text: |
          dockerRunAsRoot can be used.
      - arg_value: forbidden
        enum_value: forbidden
        help_text: |
          dockerRunAsRoot cannot be used.
      help_text: |
        Whether dockerRunAsRoot can be used. If unspecified, the default is equivalent to
        "forbidden".
    - group:
        required: false
        help_text: |
          Flags for container image sources - either only container-image-sources or both flags may
          be specified.
        params:
        - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.containerImageSources.policy
          arg_name: container-image-sources
          required: true
          choices:
          - arg_value: allowed
            enum_value: allowed
            help_text: |
              Images from any container image sources can be used.
          - arg_value: forbidden
            enum_value: forbidden
            help_text: |
              No images from any container image sources can be used.
          - arg_value: restricted
            enum_value: restricted
            help_text: |
              Container images can be used, if and only if, they are stored in one of the allowed
              container image sources.
          help_text: |
            Whether container image sources can be used. Note that all RBE actions require a
            container image so if this is set to "forbidden", all tasks will fail. If unspecified,
            the default is equivalent to "allowed".
        - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.containerImageSources.allowedValues
          arg_name: container-image-sources-allowlist
          required: false
          help_text: |
            The list of allowed container image sources. Note: this will only be used if the
            corresponding policy is set to "restricted".
    - group:
        required: false
        help_text: |
          Flags for dockerAddCapabilities - either only docker-add-capabilities or both flags may be
          specified.
        params:
        - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerAddCapabilities.policy
          arg_name: docker-add-capabilities
          required: true
          choices:
          - arg_value: allowed
            enum_value: allowed
            help_text: |
              The feature can be used.
          - arg_value: forbidden
            enum_value: forbidden
            help_text: |
              The feature cannot be used.
          - arg_value: restricted
            enum_value: restricted
            help_text: |
              The feature can be used, if and only if, it is set to one of the allowed values.
          help_text: |
            Whether dockerAddCapabilities can be used. If unspecified, the default is equivalent to
            "forbidden".
        - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerAddCapabilities.allowedValues
          arg_name: docker-add-capabilities-allowlist
          required: false
          help_text: |
            The list of allowed dockerAddCapabilities values. Note: this will only be used if the
            corresponding policy is set to "restricted".
    - group:
        required: false
        help_text: |
          Flags for dockerChrootPath - either only docker-chroot-path or both flags may be
          specified.
        params:
        - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerChrootPath.policy
          arg_name: docker-chroot-path
          required: true
          choices:
          - arg_value: allowed
            enum_value: allowed
            help_text: |
              The feature can be used.
          - arg_value: forbidden
            enum_value: forbidden
            help_text: |
              The feature cannot be used.
          - arg_value: restricted
            enum_value: restricted
            help_text: |
              The feature can be used, if and only if, it is set to one of the allowed values.
          help_text: |
            Whether dockerChrootPath can be used. If unspecified, the default is equivalent to
            "forbidden".
        - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerChrootPath.allowedValues
          arg_name: docker-chroot-path-allowlist
          required: false
          help_text: |
           The list of allowed dockerChrootPath values. Note: this will only be used if the
           corresponding policy is set to "restricted".
    - group:
        required: false
        help_text: |
          Flags for dockerNetwork - either only docker-network or both flags may be specified.
        params:
        - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerNetwork.policy
          arg_name: docker-network
          required: true
          choices:
          - arg_value: allowed
            enum_value: allowed
            help_text: |
              The feature can be used.
          - arg_value: forbidden
            enum_value: forbidden
            help_text: |
              The feature cannot be used.
          - arg_value: restricted
            enum_value: restricted
            help_text: |
              The feature can be used, if and only if, it is set to one of the allowed values.
          help_text: |
            Whether dockerNetwork can be used. If unspecified, the default is equivalent to
            "forbidden".
        - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerNetwork.allowedValues
          arg_name: docker-network-allowlist
          required: false
          help_text: |
            The list of allowed dockerNetwork values. Note: this will only be used if the
            corresponding policy is set to "restricted".
    - group:
        required: false
        help_text: |
          Flags for dockerRunAsContainerProvidedUser - either only docker-run-as-container-provided-user
          or both flags may be specified.
        params:
        - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerRunAsContainerProvidedUser.policy
          arg_name: docker-run-as-container-provided-user
          required: true
          choices:
          - arg_value: allowed
            enum_value: allowed
            help_text: |
              The feature can be used.
          - arg_value: forbidden
            enum_value: forbidden
            help_text: |
              The feature cannot be used.
          - arg_value: restricted
            enum_value: restricted
            help_text: |
              The feature can be used, if and only if, it is set to one of the allowed values.
          help_text: |
            Whether dockerRunAsContainerProvidedUser can be used. If unspecified, the default is
            equivalent to "forbidden".
        - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerRunAsContainerProvidedUser.allowedValues
          arg_name: docker-run-as-container-provided-user-allowlist
          required: false
          help_text: |
            The list of allowed dockerRunAsContainerProvidedUser values. Note: this will only be
            used if the corresponding policy is set to "restricted".
    - group:
        required: false
        help_text: |
          Flags for dockerRuntime - either only docker-runtime or both flags may be specified.
        params:
        - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerRuntime.policy
          arg_name: docker-runtime
          required: true
          choices:
          - arg_value: allowed
            enum_value: allowed
            help_text: |
              The feature can be used.
          - arg_value: forbidden
            enum_value: forbidden
            help_text: |
              The feature cannot be used.
          - arg_value: restricted
            enum_value: restricted
            help_text: |
              The feature can be used, if and only if, it is set to one of the allowed values.
          help_text: |
            Whether dockerRuntime can be used. If unspecified, the default is equivalent to
            "forbidden".
        - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerRuntime.allowedValues
          arg_name: docker-runtime-allowlist
          required: false
          help_text: |
            The list of allowed dockerRuntime values. Note: this will only be used if the
            corresponding policy is set to "restricted".
    - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.dockerSiblingContainers.policy
      arg_name: docker-sibling-containers
      required: false
      choices:
      - arg_value: allowed
        enum_value: allowed
        help_text: |
          The feature can be used.
      - arg_value: forbidden
        enum_value: forbidden
        help_text: |
          The feature cannot be used.
      help_text: |
        Whether dockerSiblingSontainers can be used. If unspecified, the default is equivalent to
        "forbidden".
    - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.linuxIsolation
      arg_name: linux-isolation
      required: false
      choices:
      - arg_value: gvisor
        enum_value: gvisor
        help_text: |
          gVisor will be used as the isolation mechanism for all linux execution.
      - arg_value: 'off'
        enum_value: 'off'
        help_text: |
          No additional isolation mechanisms will be used beyond the default linux runtime.
      help_text: |
        Which Linux isolation mechanism should be used for execution. If unspecified, the default
        Linux runtime will be used.
    - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.linuxExecution
      arg_name: linux-execution
      required: false
      choices:
      - arg_value: forbidden
        enum_value: LINUX_EXECUTION_FORBIDDEN
        help_text: |
          Forbid Linux actions and worker pools.
      - arg_value: unrestricted
        enum_value: LINUX_EXECUTION_UNRESTRICTED
        help_text: |
          No additional restrictions imposed on Linux actions or worker pools by this policy.
      - arg_value: hardened-gvisor
        enum_value: LINUX_EXECUTION_HARDENED_GVISOR
        help_text: |
          Linux actions will be hardened with gVisor. Actions incompatible with gVisor hardening
          will be rejected.
      - arg_value: hardened-gvisor-or-terminal
        enum_value: LINUX_EXECUTION_HARDENED_GVISOR_OR_TERMINAL
        help_text: |
          Linux actions will be hardened with gVisor. Actions incompatible with gVisor hardening
          will be made terminal, i.e., the worker that ran the action will be terminated after the
          action completes.
      help_text: |
        Defines whether Linux actions and worker pools are allowed and how they can be configured
        to support various levels of isolation.
    - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.windowsExecution
      arg_name: windows-execution
      required: false
      choices:
      - arg_value: forbidden
        enum_value: WINDOWS_EXECUTION_FORBIDDEN
        help_text: |
          Forbid Windows actions and worker pools.
      - arg_value: unrestricted
        enum_value: WINDOWS_EXECUTION_UNRESTRICTED
        help_text: |
          No additional restrictions imposed on Windows actions or worker pools by this policy.
      - arg_value: terminal
        enum_value: WINDOWS_EXECUTION_TERMINAL
        help_text: |
          Windows workers will be terminated after they finish running an action.
      help_text: |
        Defines whether Windows actions and worker pools are allowed and how they can be configured
        to support various levels of isolation.
    - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.actionIsolation
      arg_name: action-isolation
      required: false
      choices:
      - arg_value: enforced
        enum_value: ACTION_ISOLATION_ENFORCED
        help_text: |
          Isolation of actions is enforced.
      - arg_value: 'off'
        enum_value: ACTION_ISOLATION_OFF
        help_text: |
          No enforcement of isolation for actions.
      help_text: |
        Defines levels of isolation of actions executed on this instance by requiring other
        isolation related feature policies like linux-execution, windows-execution, etc to be set
        a certain way.
    - api_field: googleDevtoolsRemotebuildexecutionAdminV1alphaInstance.featurePolicy.actionHermeticity
      arg_name: action-hermeticity
      required: false
      choices:
      - arg_value: enforced
        enum_value: ACTION_HERMETICITY_ENFORCED
        help_text: |
          Hermeticity of actions is enforced.
      - arg_value: best-effort
        enum_value: ACTION_HERMETICITY_BEST_EFFORT
        help_text: |
          Hermeticity of actions is best effort.
      - arg_value: 'off'
        enum_value: ACTION_HERMETICITY_OFF
        help_text: |
          No Hermeticity restrictions for actions.
      help_text: |
        Defines levels of hermeticity for actions executed on this instance by requiring other
        isolation and hermeticity related feature policies like linux-execution, windows-execution,
        etc to be set a certain way.
