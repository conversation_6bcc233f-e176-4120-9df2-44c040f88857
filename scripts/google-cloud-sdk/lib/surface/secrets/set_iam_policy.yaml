- release_tracks: [BETA, GA]
  help_text:
    brief: Set the IAM policy binding for a secret.
    description: |
      Sets the IAM policy for the given secret as defined in a JSON or YAML file.

      See https://cloud.google.com/iam/docs/managing-policies for details of
      the policy file format and contents.

    examples: |
      The following command will read an IAM policy defined in a JSON file
      'policy.json' and set it for the secret 'my-secret':

        $ {command} my-secret policy.json

  request:
    api_version: v1
    collection: secretmanager.projects.secrets

  arguments:
    resource:
      help_text: Name of the secret for which to set the IAM policy.
      spec: !REF googlecloudsdk.command_lib.secrets.resources:secret

  iam:
    enable_condition: true
    policy_version: 3
    get_iam_policy_version_path: options.requestedPolicyVersion
