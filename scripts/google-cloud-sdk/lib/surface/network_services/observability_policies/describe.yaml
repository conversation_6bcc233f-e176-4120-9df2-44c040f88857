release_tracks: [ALPHA]

help_text:
  brief: |
    Describe an observability policy.
  description: Show details of an observability policy.

  examples: |
    Show details about an observability policy named 'my-observability-policy'.

      $ {command} my-observability-policy --location=global

arguments:
  resource:
    spec: !REF googlecloudsdk.command_lib.network_services.resources:observabilityPolicy
    help_text: Name of the observability policy to be described.

request: &request
  api_version: v1alpha1
  collection: networkservices.projects.locations.observabilityPolicies
