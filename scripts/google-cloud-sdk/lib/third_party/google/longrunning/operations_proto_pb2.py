# -*- coding: utf-8 -*-

# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/longrunning/operations.proto
"""Generated protocol buffer code."""
from cloudsdk.google.protobuf import descriptor as _descriptor
from cloudsdk.google.protobuf import message as _message
from cloudsdk.google.protobuf import reflection as _reflection
from cloudsdk.google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.api import client_pb2 as google_dot_api_dot_client__pb2
from cloudsdk.google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from cloudsdk.google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from cloudsdk.google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.rpc import status_pb2 as google_dot_rpc_dot_status__pb2
from cloudsdk.google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
    name="google/longrunning/operations.proto",
    package="google.longrunning",
    syntax="proto3",
    serialized_options=b"\n\026com.google.longrunningB\017OperationsProtoP\001Z=google.golang.org/genproto/googleapis/longrunning;longrunning\370\001\001\252\002\022Google.LongRunning\312\002\022Google\\LongRunning",
    create_key=_descriptor._internal_create_key,
    serialized_pb=b'\n#google/longrunning/operations.proto\x12\x12google.longrunning\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x19google/protobuf/any.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x17google/rpc/status.proto\x1a google/protobuf/descriptor.proto"\xa8\x01\n\tOperation\x12\x0c\n\x04name\x18\x01 \x01(\t\x12&\n\x08metadata\x18\x02 \x01(\x0b\x32\x14.google.protobuf.Any\x12\x0c\n\x04\x64one\x18\x03 \x01(\x08\x12#\n\x05\x65rror\x18\x04 \x01(\x0b\x32\x12.google.rpc.StatusH\x00\x12(\n\x08response\x18\x05 \x01(\x0b\x32\x14.google.protobuf.AnyH\x00\x42\x08\n\x06result"#\n\x13GetOperationRequest\x12\x0c\n\x04name\x18\x01 \x01(\t"\\\n\x15ListOperationsRequest\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0e\n\x06\x66ilter\x18\x01 \x01(\t\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\x12\n\npage_token\x18\x03 \x01(\t"d\n\x16ListOperationsResponse\x12\x31\n\noperations\x18\x01 \x03(\x0b\x32\x1d.google.longrunning.Operation\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t"&\n\x16\x43\x61ncelOperationRequest\x12\x0c\n\x04name\x18\x01 \x01(\t"&\n\x16\x44\x65leteOperationRequest\x12\x0c\n\x04name\x18\x01 \x01(\t"P\n\x14WaitOperationRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12*\n\x07timeout\x18\x02 \x01(\x0b\x32\x19.google.protobuf.Duration"=\n\rOperationInfo\x12\x15\n\rresponse_type\x18\x01 \x01(\t\x12\x15\n\rmetadata_type\x18\x02 \x01(\t2\xaa\x05\n\nOperations\x12\x94\x01\n\x0eListOperations\x12).google.longrunning.ListOperationsRequest\x1a*.google.longrunning.ListOperationsResponse"+\x82\xd3\xe4\x93\x02\x17\x12\x15/v1/{name=operations}\xda\x41\x0bname,filter\x12\x7f\n\x0cGetOperation\x12\'.google.longrunning.GetOperationRequest\x1a\x1d.google.longrunning.Operation"\'\x82\xd3\xe4\x93\x02\x1a\x12\x18/v1/{name=operations/**}\xda\x41\x04name\x12~\n\x0f\x44\x65leteOperation\x12*.google.longrunning.DeleteOperationRequest\x1a\x16.google.protobuf.Empty"\'\x82\xd3\xe4\x93\x02\x1a*\x18/v1/{name=operations/**}\xda\x41\x04name\x12\x88\x01\n\x0f\x43\x61ncelOperation\x12*.google.longrunning.CancelOperationRequest\x1a\x16.google.protobuf.Empty"1\x82\xd3\xe4\x93\x02$"\x1f/v1/{name=operations/**}:cancel:\x01*\xda\x41\x04name\x12Z\n\rWaitOperation\x12(.google.longrunning.WaitOperationRequest\x1a\x1d.google.longrunning.Operation"\x00\x1a\x1d\xca\x41\x1alongrunning.googleapis.com:Z\n\x0eoperation_info\x12\x1e.google.protobuf.MethodOptions\x18\x99\x08 \x01(\x0b\x32!.google.longrunning.OperationInfoB\x97\x01\n\x16\x63om.google.longrunningB\x0fOperationsProtoP\x01Z=google.golang.org/genproto/googleapis/longrunning;longrunning\xf8\x01\x01\xaa\x02\x12Google.LongRunning\xca\x02\x12Google\\LongRunningb\x06proto3',
    dependencies=[
        google_dot_api_dot_annotations__pb2.DESCRIPTOR,
        google_dot_api_dot_client__pb2.DESCRIPTOR,
        google_dot_protobuf_dot_any__pb2.DESCRIPTOR,
        google_dot_protobuf_dot_duration__pb2.DESCRIPTOR,
        google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,
        google_dot_rpc_dot_status__pb2.DESCRIPTOR,
        google_dot_protobuf_dot_descriptor__pb2.DESCRIPTOR,
    ],
)


OPERATION_INFO_FIELD_NUMBER = 1049
operation_info = _descriptor.FieldDescriptor(
    name="operation_info",
    full_name="google.longrunning.operation_info",
    index=0,
    number=1049,
    type=11,
    cpp_type=10,
    label=1,
    has_default_value=False,
    default_value=None,
    message_type=None,
    enum_type=None,
    containing_type=None,
    is_extension=True,
    extension_scope=None,
    serialized_options=None,
    file=DESCRIPTOR,
    create_key=_descriptor._internal_create_key,
)


_OPERATION = _descriptor.Descriptor(
    name="Operation",
    full_name="google.longrunning.Operation",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="name",
            full_name="google.longrunning.Operation.name",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="metadata",
            full_name="google.longrunning.Operation.metadata",
            index=1,
            number=2,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="done",
            full_name="google.longrunning.Operation.done",
            index=2,
            number=3,
            type=8,
            cpp_type=7,
            label=1,
            has_default_value=False,
            default_value=False,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="error",
            full_name="google.longrunning.Operation.error",
            index=3,
            number=4,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="response",
            full_name="google.longrunning.Operation.response",
            index=4,
            number=5,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[
        _descriptor.OneofDescriptor(
            name="result",
            full_name="google.longrunning.Operation.result",
            index=0,
            containing_type=None,
            create_key=_descriptor._internal_create_key,
            fields=[],
        )
    ],
    serialized_start=262,
    serialized_end=430,
)


_GETOPERATIONREQUEST = _descriptor.Descriptor(
    name="GetOperationRequest",
    full_name="google.longrunning.GetOperationRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="name",
            full_name="google.longrunning.GetOperationRequest.name",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        )
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=432,
    serialized_end=467,
)


_LISTOPERATIONSREQUEST = _descriptor.Descriptor(
    name="ListOperationsRequest",
    full_name="google.longrunning.ListOperationsRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="name",
            full_name="google.longrunning.ListOperationsRequest.name",
            index=0,
            number=4,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="filter",
            full_name="google.longrunning.ListOperationsRequest.filter",
            index=1,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="page_size",
            full_name="google.longrunning.ListOperationsRequest.page_size",
            index=2,
            number=2,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="page_token",
            full_name="google.longrunning.ListOperationsRequest.page_token",
            index=3,
            number=3,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=469,
    serialized_end=561,
)


_LISTOPERATIONSRESPONSE = _descriptor.Descriptor(
    name="ListOperationsResponse",
    full_name="google.longrunning.ListOperationsResponse",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="operations",
            full_name="google.longrunning.ListOperationsResponse.operations",
            index=0,
            number=1,
            type=11,
            cpp_type=10,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="next_page_token",
            full_name="google.longrunning.ListOperationsResponse.next_page_token",
            index=1,
            number=2,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=563,
    serialized_end=663,
)


_CANCELOPERATIONREQUEST = _descriptor.Descriptor(
    name="CancelOperationRequest",
    full_name="google.longrunning.CancelOperationRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="name",
            full_name="google.longrunning.CancelOperationRequest.name",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        )
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=665,
    serialized_end=703,
)


_DELETEOPERATIONREQUEST = _descriptor.Descriptor(
    name="DeleteOperationRequest",
    full_name="google.longrunning.DeleteOperationRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="name",
            full_name="google.longrunning.DeleteOperationRequest.name",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        )
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=705,
    serialized_end=743,
)


_WAITOPERATIONREQUEST = _descriptor.Descriptor(
    name="WaitOperationRequest",
    full_name="google.longrunning.WaitOperationRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="name",
            full_name="google.longrunning.WaitOperationRequest.name",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="timeout",
            full_name="google.longrunning.WaitOperationRequest.timeout",
            index=1,
            number=2,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=745,
    serialized_end=825,
)


_OPERATIONINFO = _descriptor.Descriptor(
    name="OperationInfo",
    full_name="google.longrunning.OperationInfo",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="response_type",
            full_name="google.longrunning.OperationInfo.response_type",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="metadata_type",
            full_name="google.longrunning.OperationInfo.metadata_type",
            index=1,
            number=2,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=827,
    serialized_end=888,
)

_OPERATION.fields_by_name[
    "metadata"
].message_type = google_dot_protobuf_dot_any__pb2._ANY
_OPERATION.fields_by_name["error"].message_type = google_dot_rpc_dot_status__pb2._STATUS
_OPERATION.fields_by_name[
    "response"
].message_type = google_dot_protobuf_dot_any__pb2._ANY
_OPERATION.oneofs_by_name["result"].fields.append(_OPERATION.fields_by_name["error"])
_OPERATION.fields_by_name["error"].containing_oneof = _OPERATION.oneofs_by_name[
    "result"
]
_OPERATION.oneofs_by_name["result"].fields.append(_OPERATION.fields_by_name["response"])
_OPERATION.fields_by_name["response"].containing_oneof = _OPERATION.oneofs_by_name[
    "result"
]
_LISTOPERATIONSRESPONSE.fields_by_name["operations"].message_type = _OPERATION
_WAITOPERATIONREQUEST.fields_by_name[
    "timeout"
].message_type = google_dot_protobuf_dot_duration__pb2._DURATION
DESCRIPTOR.message_types_by_name["Operation"] = _OPERATION
DESCRIPTOR.message_types_by_name["GetOperationRequest"] = _GETOPERATIONREQUEST
DESCRIPTOR.message_types_by_name["ListOperationsRequest"] = _LISTOPERATIONSREQUEST
DESCRIPTOR.message_types_by_name["ListOperationsResponse"] = _LISTOPERATIONSRESPONSE
DESCRIPTOR.message_types_by_name["CancelOperationRequest"] = _CANCELOPERATIONREQUEST
DESCRIPTOR.message_types_by_name["DeleteOperationRequest"] = _DELETEOPERATIONREQUEST
DESCRIPTOR.message_types_by_name["WaitOperationRequest"] = _WAITOPERATIONREQUEST
DESCRIPTOR.message_types_by_name["OperationInfo"] = _OPERATIONINFO
DESCRIPTOR.extensions_by_name["operation_info"] = operation_info
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Operation = _reflection.GeneratedProtocolMessageType(
    "Operation",
    (_message.Message,),
    {
        "DESCRIPTOR": _OPERATION,
        "__module__": "google.longrunning.operations_pb2"
        # @@protoc_insertion_point(class_scope:google.longrunning.Operation)
    },
)
_sym_db.RegisterMessage(Operation)

GetOperationRequest = _reflection.GeneratedProtocolMessageType(
    "GetOperationRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _GETOPERATIONREQUEST,
        "__module__": "google.longrunning.operations_pb2"
        # @@protoc_insertion_point(class_scope:google.longrunning.GetOperationRequest)
    },
)
_sym_db.RegisterMessage(GetOperationRequest)

ListOperationsRequest = _reflection.GeneratedProtocolMessageType(
    "ListOperationsRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _LISTOPERATIONSREQUEST,
        "__module__": "google.longrunning.operations_pb2"
        # @@protoc_insertion_point(class_scope:google.longrunning.ListOperationsRequest)
    },
)
_sym_db.RegisterMessage(ListOperationsRequest)

ListOperationsResponse = _reflection.GeneratedProtocolMessageType(
    "ListOperationsResponse",
    (_message.Message,),
    {
        "DESCRIPTOR": _LISTOPERATIONSRESPONSE,
        "__module__": "google.longrunning.operations_pb2"
        # @@protoc_insertion_point(class_scope:google.longrunning.ListOperationsResponse)
    },
)
_sym_db.RegisterMessage(ListOperationsResponse)

CancelOperationRequest = _reflection.GeneratedProtocolMessageType(
    "CancelOperationRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _CANCELOPERATIONREQUEST,
        "__module__": "google.longrunning.operations_pb2"
        # @@protoc_insertion_point(class_scope:google.longrunning.CancelOperationRequest)
    },
)
_sym_db.RegisterMessage(CancelOperationRequest)

DeleteOperationRequest = _reflection.GeneratedProtocolMessageType(
    "DeleteOperationRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _DELETEOPERATIONREQUEST,
        "__module__": "google.longrunning.operations_pb2"
        # @@protoc_insertion_point(class_scope:google.longrunning.DeleteOperationRequest)
    },
)
_sym_db.RegisterMessage(DeleteOperationRequest)

WaitOperationRequest = _reflection.GeneratedProtocolMessageType(
    "WaitOperationRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _WAITOPERATIONREQUEST,
        "__module__": "google.longrunning.operations_pb2"
        # @@protoc_insertion_point(class_scope:google.longrunning.WaitOperationRequest)
    },
)
_sym_db.RegisterMessage(WaitOperationRequest)

OperationInfo = _reflection.GeneratedProtocolMessageType(
    "OperationInfo",
    (_message.Message,),
    {
        "DESCRIPTOR": _OPERATIONINFO,
        "__module__": "google.longrunning.operations_pb2"
        # @@protoc_insertion_point(class_scope:google.longrunning.OperationInfo)
    },
)
_sym_db.RegisterMessage(OperationInfo)

operation_info.message_type = _OPERATIONINFO
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(operation_info)

DESCRIPTOR._options = None

_OPERATIONS = _descriptor.ServiceDescriptor(
    name="Operations",
    full_name="google.longrunning.Operations",
    file=DESCRIPTOR,
    index=0,
    serialized_options=b"\312A\032longrunning.googleapis.com",
    create_key=_descriptor._internal_create_key,
    serialized_start=891,
    serialized_end=1573,
    methods=[
        _descriptor.MethodDescriptor(
            name="ListOperations",
            full_name="google.longrunning.Operations.ListOperations",
            index=0,
            containing_service=None,
            input_type=_LISTOPERATIONSREQUEST,
            output_type=_LISTOPERATIONSRESPONSE,
            serialized_options=b"\202\323\344\223\002\027\022\025/v1/{name=operations}\332A\013name,filter",
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.MethodDescriptor(
            name="GetOperation",
            full_name="google.longrunning.Operations.GetOperation",
            index=1,
            containing_service=None,
            input_type=_GETOPERATIONREQUEST,
            output_type=_OPERATION,
            serialized_options=b"\202\323\344\223\002\032\022\030/v1/{name=operations/**}\332A\004name",
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.MethodDescriptor(
            name="DeleteOperation",
            full_name="google.longrunning.Operations.DeleteOperation",
            index=2,
            containing_service=None,
            input_type=_DELETEOPERATIONREQUEST,
            output_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
            serialized_options=b"\202\323\344\223\002\032*\030/v1/{name=operations/**}\332A\004name",
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.MethodDescriptor(
            name="CancelOperation",
            full_name="google.longrunning.Operations.CancelOperation",
            index=3,
            containing_service=None,
            input_type=_CANCELOPERATIONREQUEST,
            output_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
            serialized_options=b'\202\323\344\223\002$"\037/v1/{name=operations/**}:cancel:\001*\332A\004name',
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.MethodDescriptor(
            name="WaitOperation",
            full_name="google.longrunning.Operations.WaitOperation",
            index=4,
            containing_service=None,
            input_type=_WAITOPERATIONREQUEST,
            output_type=_OPERATION,
            serialized_options=None,
            create_key=_descriptor._internal_create_key,
        ),
    ],
)
_sym_db.RegisterServiceDescriptor(_OPERATIONS)

DESCRIPTOR.services_by_name["Operations"] = _OPERATIONS

# @@protoc_insertion_point(module_scope)
