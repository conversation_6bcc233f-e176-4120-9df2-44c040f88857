{"version": "2.0", "metadata": {"apiVersion": "2015-03-31", "endpointPrefix": "lambda", "protocol": "rest-json", "serviceFullName": "AWS Lambda", "serviceId": "Lambda", "signatureVersion": "v4", "uid": "lambda-2015-03-31"}, "operations": {"AddLayerVersionPermission": {"name": "AddLayerVersionPermission", "http": {"method": "POST", "requestUri": "/2018-10-31/layers/{LayerName}/versions/{VersionNumber}/policy", "responseCode": 201}, "input": {"shape": "AddLayerVersionPermissionRequest"}, "output": {"shape": "AddLayerVersionPermissionResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceConflictException"}, {"shape": "TooManyRequestsException"}, {"shape": "InvalidParameterValueException"}, {"shape": "PolicyLengthExceededException"}, {"shape": "PreconditionFailedException"}], "documentation": "<p>Adds permissions to the resource-based policy of a version of an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a>. Use this action to grant layer usage permission to other accounts. You can grant permission to a single account, all AWS accounts, or all accounts in an organization.</p> <p>To revoke permission, call <a>RemoveLayerVersionPermission</a> with the statement ID that you specified when you added it.</p>"}, "AddPermission": {"name": "AddPermission", "http": {"method": "POST", "requestUri": "/2015-03-31/functions/{FunctionName}/policy", "responseCode": 201}, "input": {"shape": "AddPermissionRequest"}, "output": {"shape": "AddPermissionResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceConflictException"}, {"shape": "InvalidParameterValueException"}, {"shape": "PolicyLengthExceededException"}, {"shape": "TooManyRequestsException"}, {"shape": "PreconditionFailedException"}], "documentation": "<p>Grants an AWS service or another account permission to use a function. You can apply the policy at the function level, or specify a qualifier to restrict access to a single version or alias. If you use a qualifier, the invoker must use the full Amazon Resource Name (ARN) of that version or alias to invoke the function.</p> <p>To grant permission to another account, specify the account ID as the <code>Principal</code>. For AWS services, the principal is a domain-style identifier defined by the service, like <code>s3.amazonaws.com</code> or <code>sns.amazonaws.com</code>. For AWS services, you can also specify the ARN of the associated resource as the <code>SourceArn</code>. If you grant permission to a service principal without specifying the source, other accounts could potentially configure resources in their account to invoke your Lambda function.</p> <p>This action adds a statement to a resource-based permissions policy for the function. For more information about function policies, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/access-control-resource-based.html\">Lambda Function Policies</a>. </p>"}, "CreateAlias": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/2015-03-31/functions/{FunctionName}/aliases", "responseCode": 201}, "input": {"shape": "CreateAliasRequest"}, "output": {"shape": "AliasConfiguration"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceConflictException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Creates an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/versioning-aliases.html\">alias</a> for a Lambda function version. Use aliases to provide clients with a function identifier that you can update to invoke a different version.</p> <p>You can also map an alias to split invocation requests between two versions. Use the <code>RoutingConfig</code> parameter to specify a second version and the percentage of invocation requests that it receives.</p>"}, "CreateEventSourceMapping": {"name": "CreateEventSourceMapping", "http": {"method": "POST", "requestUri": "/2015-03-31/event-source-mappings/", "responseCode": 202}, "input": {"shape": "CreateEventSourceMappingRequest"}, "output": {"shape": "EventSourceMappingConfiguration"}, "errors": [{"shape": "ServiceException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceConflictException"}, {"shape": "TooManyRequestsException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates a mapping between an event source and an AWS Lambda function. Lambda reads items from the event source and triggers the function.</p> <p>For details about each event source type, see the following topics.</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/with-ddb.html\">Using AWS Lambda with Amazon DynamoDB</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/with-kinesis.html\">Using AWS Lambda with Amazon Kinesis</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/with-sqs.html\">Using AWS Lambda with Amazon SQS</a> </p> </li> </ul> <p>The following error handling options are only available for stream sources (DynamoDB and Kinesis):</p> <ul> <li> <p> <code>BisectBatchOnFunctionError</code> - If the function returns an error, split the batch in two and retry.</p> </li> <li> <p> <code>DestinationConfig</code> - Send discarded records to an Amazon SQS queue or Amazon SNS topic.</p> </li> <li> <p> <code>MaximumRecordAgeInSeconds</code> - Discard records older than the specified age.</p> </li> <li> <p> <code>MaximumRetryAttempts</code> - Discard records after the specified number of retries.</p> </li> <li> <p> <code>ParallelizationFactor</code> - Process multiple batches from each shard concurrently.</p> </li> </ul>"}, "CreateFunction": {"name": "CreateFunction", "http": {"method": "POST", "requestUri": "/2015-03-31/functions", "responseCode": 201}, "input": {"shape": "CreateFunctionRequest"}, "output": {"shape": "FunctionConfiguration"}, "errors": [{"shape": "ServiceException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceConflictException"}, {"shape": "TooManyRequestsException"}, {"shape": "CodeStorageExceededException"}], "documentation": "<p>Creates a Lambda function. To create a function, you need a <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/deployment-package-v2.html\">deployment package</a> and an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/intro-permission-model.html#lambda-intro-execution-role\">execution role</a>. The deployment package contains your function code. The execution role grants the function permission to use AWS services, such as Amazon CloudWatch Logs for log streaming and AWS X-Ray for request tracing.</p> <p>When you create a function, Lambda provisions an instance of the function and its supporting resources. If your function connects to a VPC, this process can take a minute or so. During this time, you can't invoke or modify the function. The <code>State</code>, <code>StateReason</code>, and <code>StateReasonCode</code> fields in the response from <a>GetFunctionConfiguration</a> indicate when the function is ready to invoke. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/functions-states.html\">Function States</a>.</p> <p>A function has an unpublished version, and can have published versions and aliases. The unpublished version changes when you update your function's code and configuration. A published version is a snapshot of your function code and configuration that can't be changed. An alias is a named resource that maps to a version, and can be changed to map to a different version. Use the <code>Publish</code> parameter to create version <code>1</code> of your function from its initial configuration.</p> <p>The other parameters let you configure version-specific and function-level settings. You can modify version-specific settings later with <a>UpdateFunctionConfiguration</a>. Function-level settings apply to both the unpublished and published versions of the function, and include tags (<a>TagResource</a>) and per-function concurrency limits (<a>PutFunctionConcurrency</a>).</p> <p>If another account or an AWS service invokes your function, use <a>AddPermission</a> to grant permission by creating a resource-based IAM policy. You can grant permissions at the function level, on a version, or on an alias.</p> <p>To invoke your function directly, use <a>Invoke</a>. To invoke your function in response to events in other AWS services, create an event source mapping (<a>CreateEventSourceMapping</a>), or configure a function trigger in the other service. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/lambda-invocation.html\">Invoking Functions</a>.</p>"}, "DeleteAlias": {"name": "DeleteAlias", "http": {"method": "DELETE", "requestUri": "/2015-03-31/functions/{FunctionName}/aliases/{Name}", "responseCode": 204}, "input": {"shape": "DeleteAliasRequest"}, "errors": [{"shape": "ServiceException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceConflictException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Deletes a Lambda function <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/versioning-aliases.html\">alias</a>.</p>"}, "DeleteEventSourceMapping": {"name": "DeleteEventSourceMapping", "http": {"method": "DELETE", "requestUri": "/2015-03-31/event-source-mappings/{UUID}", "responseCode": 202}, "input": {"shape": "DeleteEventSourceMappingRequest"}, "output": {"shape": "EventSourceMappingConfiguration"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/intro-invocation-modes.html\">event source mapping</a>. You can get the identifier of a mapping from the output of <a>ListEventSourceMappings</a>.</p> <p>When you delete an event source mapping, it enters a <code>Deleting</code> state and might not be completely deleted for several seconds.</p>"}, "DeleteFunction": {"name": "DeleteFunction", "http": {"method": "DELETE", "requestUri": "/2015-03-31/functions/{FunctionName}", "responseCode": 204}, "input": {"shape": "DeleteFunctionRequest"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceConflictException"}], "documentation": "<p>Deletes a Lambda function. To delete a specific function version, use the <code>Qualifier</code> parameter. Otherwise, all versions and aliases are deleted.</p> <p>To delete Lambda event source mappings that invoke a function, use <a>DeleteEventSourceMapping</a>. For AWS services and resources that invoke your function directly, delete the trigger in the service where you originally configured it.</p>"}, "DeleteFunctionConcurrency": {"name": "DeleteFunctionConcurrency", "http": {"method": "DELETE", "requestUri": "/2017-10-31/functions/{FunctionName}/concurrency", "responseCode": 204}, "input": {"shape": "DeleteFunctionConcurrencyRequest"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceConflictException"}], "documentation": "<p>Removes a concurrent execution limit from a function.</p>"}, "DeleteFunctionEventInvokeConfig": {"name": "DeleteFunctionEventInvokeConfig", "http": {"method": "DELETE", "requestUri": "/2019-09-25/functions/{FunctionName}/event-invoke-config", "responseCode": 204}, "input": {"shape": "DeleteFunctionEventInvokeConfigRequest"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Deletes the configuration for asynchronous invocation for a function, version, or alias.</p> <p>To configure options for asynchronous invocation, use <a>PutFunctionEventInvokeConfig</a>.</p>"}, "DeleteLayerVersion": {"name": "DeleteLayerVersion", "http": {"method": "DELETE", "requestUri": "/2018-10-31/layers/{LayerName}/versions/{VersionNumber}", "responseCode": 204}, "input": {"shape": "DeleteLayerVersionRequest"}, "errors": [{"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Deletes a version of an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a>. Deleted versions can no longer be viewed or added to functions. To avoid breaking functions, a copy of the version remains in Lambda until no functions refer to it.</p>"}, "DeleteProvisionedConcurrencyConfig": {"name": "DeleteProvisionedConcurrencyConfig", "http": {"method": "DELETE", "requestUri": "/2019-09-30/functions/{FunctionName}/provisioned-concurrency", "responseCode": 204}, "input": {"shape": "DeleteProvisionedConcurrencyConfigRequest"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ResourceConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "ServiceException"}], "documentation": "<p>Deletes the provisioned concurrency configuration for a function.</p>"}, "GetAccountSettings": {"name": "GetAccountSettings", "http": {"method": "GET", "requestUri": "/2016-08-19/account-settings/", "responseCode": 200}, "input": {"shape": "GetAccountSettingsRequest"}, "output": {"shape": "GetAccountSettingsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "ServiceException"}], "documentation": "<p>Retrieves details about your account's <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/limits.html\">limits</a> and usage in an AWS Region.</p>"}, "GetAlias": {"name": "GetAlias", "http": {"method": "GET", "requestUri": "/2015-03-31/functions/{FunctionName}/aliases/{Name}", "responseCode": 200}, "input": {"shape": "GetAliasRequest"}, "output": {"shape": "AliasConfiguration"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Returns details about a Lambda function <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/versioning-aliases.html\">alias</a>.</p>"}, "GetEventSourceMapping": {"name": "GetEventSourceMapping", "http": {"method": "GET", "requestUri": "/2015-03-31/event-source-mappings/{UUID}", "responseCode": 200}, "input": {"shape": "GetEventSourceMappingRequest"}, "output": {"shape": "EventSourceMappingConfiguration"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Returns details about an event source mapping. You can get the identifier of a mapping from the output of <a>ListEventSourceMappings</a>.</p>"}, "GetFunction": {"name": "GetFunction", "http": {"method": "GET", "requestUri": "/2015-03-31/functions/{FunctionName}", "responseCode": 200}, "input": {"shape": "GetFunctionRequest"}, "output": {"shape": "GetFunctionResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "InvalidParameterValueException"}], "documentation": "<p>Returns information about the function or function version, with a link to download the deployment package that's valid for 10 minutes. If you specify a function version, only details that are specific to that version are returned.</p>"}, "GetFunctionConcurrency": {"name": "GetFunctionConcurrency", "http": {"method": "GET", "requestUri": "/2019-09-30/functions/{FunctionName}/concurrency", "responseCode": 200}, "input": {"shape": "GetFunctionConcurrencyRequest"}, "output": {"shape": "GetFunctionConcurrencyResponse"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "ServiceException"}], "documentation": "<p>Returns details about the reserved concurrency configuration for a function. To set a concurrency limit for a function, use <a>PutFunctionConcurrency</a>.</p>"}, "GetFunctionConfiguration": {"name": "GetFunctionConfiguration", "http": {"method": "GET", "requestUri": "/2015-03-31/functions/{FunctionName}/configuration", "responseCode": 200}, "input": {"shape": "GetFunctionConfigurationRequest"}, "output": {"shape": "FunctionConfiguration"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "InvalidParameterValueException"}], "documentation": "<p>Returns the version-specific settings of a Lambda function or version. The output includes only options that can vary between versions of a function. To modify these settings, use <a>UpdateFunctionConfiguration</a>.</p> <p>To get all of a function's details, including function-level settings, use <a>GetFunction</a>.</p>"}, "GetFunctionEventInvokeConfig": {"name": "GetFunctionEventInvokeConfig", "http": {"method": "GET", "requestUri": "/2019-09-25/functions/{FunctionName}/event-invoke-config", "responseCode": 200}, "input": {"shape": "GetFunctionEventInvokeConfigRequest"}, "output": {"shape": "FunctionEventInvokeConfig"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves the configuration for asynchronous invocation for a function, version, or alias.</p> <p>To configure options for asynchronous invocation, use <a>PutFunctionEventInvokeConfig</a>.</p>"}, "GetLayerVersion": {"name": "GetLayerVersion", "http": {"method": "GET", "requestUri": "/2018-10-31/layers/{LayerName}/versions/{VersionNumber}", "responseCode": 200}, "input": {"shape": "GetLayerVersionRequest"}, "output": {"shape": "GetLayerVersionResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns information about a version of an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a>, with a link to download the layer archive that's valid for 10 minutes.</p>"}, "GetLayerVersionByArn": {"name": "GetLayerVersionByArn", "http": {"method": "GET", "requestUri": "/2018-10-31/layers?find=LayerVersion", "responseCode": 200}, "input": {"shape": "GetLayerVersionByArnRequest"}, "output": {"shape": "GetLayerVersionResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns information about a version of an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a>, with a link to download the layer archive that's valid for 10 minutes.</p>"}, "GetLayerVersionPolicy": {"name": "GetLayerVersionPolicy", "http": {"method": "GET", "requestUri": "/2018-10-31/layers/{LayerName}/versions/{VersionNumber}/policy", "responseCode": 200}, "input": {"shape": "GetLayerVersionPolicyRequest"}, "output": {"shape": "GetLayerVersionPolicyResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "InvalidParameterValueException"}], "documentation": "<p>Returns the permission policy for a version of an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a>. For more information, see <a>AddLayerVersionPermission</a>.</p>"}, "GetPolicy": {"name": "GetPolicy", "http": {"method": "GET", "requestUri": "/2015-03-31/functions/{FunctionName}/policy", "responseCode": 200}, "input": {"shape": "GetPolicyRequest"}, "output": {"shape": "GetPolicyResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "InvalidParameterValueException"}], "documentation": "<p>Returns the <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/access-control-resource-based.html\">resource-based IAM policy</a> for a function, version, or alias.</p>"}, "GetProvisionedConcurrencyConfig": {"name": "GetProvisionedConcurrencyConfig", "http": {"method": "GET", "requestUri": "/2019-09-30/functions/{FunctionName}/provisioned-concurrency", "responseCode": 200}, "input": {"shape": "GetProvisionedConcurrencyConfigRequest"}, "output": {"shape": "GetProvisionedConcurrencyConfigResponse"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "ServiceException"}, {"shape": "ProvisionedConcurrencyConfigNotFoundException"}], "documentation": "<p>Retrieves the provisioned concurrency configuration for a function's alias or version.</p>"}, "Invoke": {"name": "Invoke", "http": {"method": "POST", "requestUri": "/2015-03-31/functions/{FunctionName}/invocations"}, "input": {"shape": "InvocationRequest"}, "output": {"shape": "InvocationResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestContentException"}, {"shape": "RequestTooLargeException"}, {"shape": "UnsupportedMediaTypeException"}, {"shape": "TooManyRequestsException"}, {"shape": "InvalidParameterValueException"}, {"shape": "EC2UnexpectedException"}, {"shape": "SubnetIPAddressLimitReachedException"}, {"shape": "ENILimitReachedException"}, {"shape": "EFSMountConnectivityException"}, {"shape": "EFSMountFailureException"}, {"shape": "EFSMountTimeoutException"}, {"shape": "EFSIOException"}, {"shape": "EC2ThrottledException"}, {"shape": "EC2AccessDeniedException"}, {"shape": "InvalidSubnetIDException"}, {"shape": "InvalidSecurityGroupIDException"}, {"shape": "InvalidZipFileException"}, {"shape": "KMSDisabledException"}, {"shape": "KMSInvalidStateException"}, {"shape": "KMSAccessDeniedException"}, {"shape": "KMSNotFoundException"}, {"shape": "InvalidRuntimeException"}, {"shape": "ResourceConflictException"}, {"shape": "ResourceNotReadyException"}], "documentation": "<p>Invokes a Lambda function. You can invoke a function synchronously (and wait for the response), or asynchronously. To invoke a function asynchronously, set <code>InvocationType</code> to <code>Event</code>.</p> <p>For <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/invocation-sync.html\">synchronous invocation</a>, details about the function response, including errors, are included in the response body and headers. For either invocation type, you can find more information in the <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/monitoring-functions.html\">execution log</a> and <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/lambda-x-ray.html\">trace</a>.</p> <p>When an error occurs, your function may be invoked multiple times. Retry behavior varies by error type, client, event source, and invocation type. For example, if you invoke a function asynchronously and it returns an error, <PERSON><PERSON> executes the function up to two more times. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/retries-on-errors.html\">Retry Behavior</a>.</p> <p>For <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/invocation-async.html\">asynchronous invocation</a>, Lambda adds events to a queue before sending them to your function. If your function does not have enough capacity to keep up with the queue, events may be lost. Occasionally, your function may receive the same event multiple times, even if no error occurs. To retain events that were not processed, configure your function with a <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/invocation-async.html#dlq\">dead-letter queue</a>.</p> <p>The status code in the API response doesn't reflect function errors. Error codes are reserved for errors that prevent your function from executing, such as permissions errors, <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/limits.html\">limit errors</a>, or issues with your function's code and configuration. For example, Lambda returns <code>TooManyRequestsException</code> if executing the function would cause you to exceed a concurrency limit at either the account level (<code>ConcurrentInvocationLimitExceeded</code>) or function level (<code>ReservedFunctionConcurrentInvocationLimitExceeded</code>).</p> <p>For functions with a long timeout, your client might be disconnected during synchronous invocation while it waits for a response. Configure your HTTP client, SDK, firewall, proxy, or operating system to allow for long connections with timeout or keep-alive settings.</p> <p>This operation requires permission for the <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/list_awslambda.html\">lambda:InvokeFunction</a> action.</p>"}, "InvokeAsync": {"name": "InvokeAsync", "http": {"method": "POST", "requestUri": "/2014-11-13/functions/{FunctionName}/invoke-async/", "responseCode": 202}, "input": {"shape": "InvokeAsyncRequest"}, "output": {"shape": "InvokeAsyncResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestContentException"}, {"shape": "InvalidRuntimeException"}, {"shape": "ResourceConflictException"}], "documentation": "<important> <p>For asynchronous function invocation, use <a>Invoke</a>.</p> </important> <p>Invokes a function asynchronously.</p>", "deprecated": true}, "ListAliases": {"name": "ListAliases", "http": {"method": "GET", "requestUri": "/2015-03-31/functions/{FunctionName}/aliases", "responseCode": 200}, "input": {"shape": "ListAliasesRequest"}, "output": {"shape": "ListAliasesResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Returns a list of <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/versioning-aliases.html\">aliases</a> for a Lambda function.</p>"}, "ListEventSourceMappings": {"name": "ListEventSourceMappings", "http": {"method": "GET", "requestUri": "/2015-03-31/event-source-mappings/", "responseCode": 200}, "input": {"shape": "ListEventSourceMappingsRequest"}, "output": {"shape": "ListEventSourceMappingsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists event source mappings. Specify an <code>EventSourceArn</code> to only show event source mappings for a single event source.</p>"}, "ListFunctionEventInvokeConfigs": {"name": "ListFunctionEventInvokeConfigs", "http": {"method": "GET", "requestUri": "/2019-09-25/functions/{FunctionName}/event-invoke-config/list", "responseCode": 200}, "input": {"shape": "ListFunctionEventInvokeConfigsRequest"}, "output": {"shape": "ListFunctionEventInvokeConfigsResponse"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "ServiceException"}], "documentation": "<p>Retrieves a list of configurations for asynchronous invocation for a function.</p> <p>To configure options for asynchronous invocation, use <a>PutFunctionEventInvokeConfig</a>.</p>"}, "ListFunctions": {"name": "ListFunctions", "http": {"method": "GET", "requestUri": "/2015-03-31/functions/", "responseCode": 200}, "input": {"shape": "ListFunctionsRequest"}, "output": {"shape": "ListFunctionsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "InvalidParameterValueException"}], "documentation": "<p>Returns a list of Lambda functions, with the version-specific configuration of each. Lambda returns up to 50 functions per call.</p> <p>Set <code>FunctionVersion</code> to <code>ALL</code> to include all published versions of each function in addition to the unpublished version. To get more information about a function or version, use <a>GetFunction</a>.</p>"}, "ListLayerVersions": {"name": "ListLayerVersions", "http": {"method": "GET", "requestUri": "/2018-10-31/layers/{LayerName}/versions", "responseCode": 200}, "input": {"shape": "ListLayerVersionsRequest"}, "output": {"shape": "ListLayerVersionsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists the versions of an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a>. Versions that have been deleted aren't listed. Specify a <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/lambda-runtimes.html\">runtime identifier</a> to list only versions that indicate that they're compatible with that runtime.</p>"}, "ListLayers": {"name": "ListLayers", "http": {"method": "GET", "requestUri": "/2018-10-31/layers", "responseCode": 200}, "input": {"shape": "ListLayersRequest"}, "output": {"shape": "ListLayersResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layers</a> and shows information about the latest version of each. Specify a <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/lambda-runtimes.html\">runtime identifier</a> to list only layers that indicate that they're compatible with that runtime.</p>"}, "ListProvisionedConcurrencyConfigs": {"name": "ListProvisionedConcurrencyConfigs", "http": {"method": "GET", "requestUri": "/2019-09-30/functions/{FunctionName}/provisioned-concurrency?List=ALL", "responseCode": 200}, "input": {"shape": "ListProvisionedConcurrencyConfigsRequest"}, "output": {"shape": "ListProvisionedConcurrencyConfigsResponse"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "ServiceException"}], "documentation": "<p>Retrieves a list of provisioned concurrency configurations for a function.</p>"}, "ListTags": {"name": "ListTags", "http": {"method": "GET", "requestUri": "/2017-03-31/tags/{ARN}"}, "input": {"shape": "ListTagsRequest"}, "output": {"shape": "ListTagsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Returns a function's <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/tagging.html\">tags</a>. You can also view tags with <a>GetFunction</a>.</p>"}, "ListVersionsByFunction": {"name": "ListVersionsByFunction", "http": {"method": "GET", "requestUri": "/2015-03-31/functions/{FunctionName}/versions", "responseCode": 200}, "input": {"shape": "ListVersionsByFunctionRequest"}, "output": {"shape": "ListVersionsByFunctionResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Returns a list of <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/versioning-aliases.html\">versions</a>, with the version-specific configuration of each. Lambda returns up to 50 versions per call.</p>"}, "PublishLayerVersion": {"name": "PublishLayerVersion", "http": {"method": "POST", "requestUri": "/2018-10-31/layers/{LayerName}/versions", "responseCode": 201}, "input": {"shape": "PublishLayerVersionRequest"}, "output": {"shape": "PublishLayerVersionResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "InvalidParameterValueException"}, {"shape": "CodeStorageExceededException"}], "documentation": "<p>Creates an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a> from a ZIP archive. Each time you call <code>PublishLayerVersion</code> with the same layer name, a new version is created.</p> <p>Add layers to your function with <a>CreateFunction</a> or <a>UpdateFunctionConfiguration</a>.</p>"}, "PublishVersion": {"name": "PublishVersion", "http": {"method": "POST", "requestUri": "/2015-03-31/functions/{FunctionName}/versions", "responseCode": 201}, "input": {"shape": "PublishVersionRequest"}, "output": {"shape": "FunctionConfiguration"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}, {"shape": "CodeStorageExceededException"}, {"shape": "PreconditionFailedException"}, {"shape": "ResourceConflictException"}], "documentation": "<p>Creates a <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/versioning-aliases.html\">version</a> from the current code and configuration of a function. Use versions to create a snapshot of your function code and configuration that doesn't change.</p> <p>AWS Lambda doesn't publish a version if the function's configuration and code haven't changed since the last version. Use <a>UpdateFunctionCode</a> or <a>UpdateFunctionConfiguration</a> to update the function before publishing a version.</p> <p>Clients can invoke versions directly or with an alias. To create an alias, use <a>CreateAlias</a>.</p>"}, "PutFunctionConcurrency": {"name": "PutFunctionConcurrency", "http": {"method": "PUT", "requestUri": "/2017-10-31/functions/{FunctionName}/concurrency", "responseCode": 200}, "input": {"shape": "PutFunctionConcurrencyRequest"}, "output": {"shape": "Concurrency"}, "errors": [{"shape": "ServiceException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "ResourceConflictException"}], "documentation": "<p>Sets the maximum number of simultaneous executions for a function, and reserves capacity for that concurrency level.</p> <p>Concurrency settings apply to the function as a whole, including all published versions and the unpublished version. Reserving concurrency both ensures that your function has capacity to process the specified number of events simultaneously, and prevents it from scaling beyond that level. Use <a>GetFunction</a> to see the current setting for a function.</p> <p>Use <a>GetAccountSettings</a> to see your Regional concurrency limit. You can reserve concurrency for as many functions as you like, as long as you leave at least 100 simultaneous executions unreserved for functions that aren't configured with a per-function limit. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/concurrent-executions.html\">Managing Concurrency</a>.</p>"}, "PutFunctionEventInvokeConfig": {"name": "PutFunctionEventInvokeConfig", "http": {"method": "PUT", "requestUri": "/2019-09-25/functions/{FunctionName}/event-invoke-config", "responseCode": 200}, "input": {"shape": "PutFunctionEventInvokeConfigRequest"}, "output": {"shape": "FunctionEventInvokeConfig"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Configures options for <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/invocation-async.html\">asynchronous invocation</a> on a function, version, or alias. If a configuration already exists for a function, version, or alias, this operation overwrites it. If you exclude any settings, they are removed. To set one option without affecting existing settings for other options, use <a>UpdateFunctionEventInvokeConfig</a>.</p> <p>By default, Lambda retries an asynchronous invocation twice if the function returns an error. It retains events in a queue for up to six hours. When an event fails all processing attempts or stays in the asynchronous invocation queue for too long, Lambda discards it. To retain discarded events, configure a dead-letter queue with <a>UpdateFunctionConfiguration</a>.</p> <p>To send an invocation record to a queue, topic, function, or event bus, specify a <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/invocation-async.html#invocation-async-destinations\">destination</a>. You can configure separate destinations for successful invocations (on-success) and events that fail all processing attempts (on-failure). You can configure destinations in addition to or instead of a dead-letter queue.</p>"}, "PutProvisionedConcurrencyConfig": {"name": "PutProvisionedConcurrencyConfig", "http": {"method": "PUT", "requestUri": "/2019-09-30/functions/{FunctionName}/provisioned-concurrency", "responseCode": 202}, "input": {"shape": "PutProvisionedConcurrencyConfigRequest"}, "output": {"shape": "PutProvisionedConcurrencyConfigResponse"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceConflictException"}, {"shape": "TooManyRequestsException"}, {"shape": "ServiceException"}], "documentation": "<p>Adds a provisioned concurrency configuration to a function's alias or version.</p>"}, "RemoveLayerVersionPermission": {"name": "RemoveLayerVersionPermission", "http": {"method": "DELETE", "requestUri": "/2018-10-31/layers/{LayerName}/versions/{VersionNumber}/policy/{StatementId}", "responseCode": 204}, "input": {"shape": "RemoveLayerVersionPermissionRequest"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}, {"shape": "PreconditionFailedException"}], "documentation": "<p>Removes a statement from the permissions policy for a version of an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a>. For more information, see <a>AddLayerVersionPermission</a>.</p>"}, "RemovePermission": {"name": "RemovePermission", "http": {"method": "DELETE", "requestUri": "/2015-03-31/functions/{FunctionName}/policy/{StatementId}", "responseCode": 204}, "input": {"shape": "RemovePermissionRequest"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}, {"shape": "PreconditionFailedException"}], "documentation": "<p>Revokes function-use permission from an AWS service or another account. You can get the ID of the statement from the output of <a>GetPolicy</a>.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/2017-03-31/tags/{ARN}", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}, {"shape": "ResourceConflictException"}], "documentation": "<p>Adds <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/tagging.html\">tags</a> to a function.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/2017-03-31/tags/{ARN}", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}, {"shape": "ResourceConflictException"}], "documentation": "<p>Removes <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/tagging.html\">tags</a> from a function.</p>"}, "UpdateAlias": {"name": "Update<PERSON><PERSON><PERSON>", "http": {"method": "PUT", "requestUri": "/2015-03-31/functions/{FunctionName}/aliases/{Name}", "responseCode": 200}, "input": {"shape": "UpdateAliasRequest"}, "output": {"shape": "AliasConfiguration"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}, {"shape": "PreconditionFailedException"}, {"shape": "ResourceConflictException"}], "documentation": "<p>Updates the configuration of a Lambda function <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/versioning-aliases.html\">alias</a>.</p>"}, "UpdateEventSourceMapping": {"name": "UpdateEventSourceMapping", "http": {"method": "PUT", "requestUri": "/2015-03-31/event-source-mappings/{UUID}", "responseCode": 202}, "input": {"shape": "UpdateEventSourceMappingRequest"}, "output": {"shape": "EventSourceMappingConfiguration"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}, {"shape": "ResourceConflictException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Updates an event source mapping. You can change the function that AWS Lambda invokes, or pause invocation and resume later from the same location.</p> <p>The following error handling options are only available for stream sources (DynamoDB and Kinesis):</p> <ul> <li> <p> <code>BisectBatchOnFunctionError</code> - If the function returns an error, split the batch in two and retry.</p> </li> <li> <p> <code>DestinationConfig</code> - Send discarded records to an Amazon SQS queue or Amazon SNS topic.</p> </li> <li> <p> <code>MaximumRecordAgeInSeconds</code> - Discard records older than the specified age.</p> </li> <li> <p> <code>MaximumRetryAttempts</code> - Discard records after the specified number of retries.</p> </li> <li> <p> <code>ParallelizationFactor</code> - Process multiple batches from each shard concurrently.</p> </li> </ul>"}, "UpdateFunctionCode": {"name": "UpdateFunctionCode", "http": {"method": "PUT", "requestUri": "/2015-03-31/functions/{FunctionName}/code", "responseCode": 200}, "input": {"shape": "UpdateFunctionCodeRequest"}, "output": {"shape": "FunctionConfiguration"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}, {"shape": "CodeStorageExceededException"}, {"shape": "PreconditionFailedException"}, {"shape": "ResourceConflictException"}], "documentation": "<p>Updates a Lambda function's code.</p> <p>The function's code is locked when you publish a version. You can't modify the code of a published version, only the unpublished version.</p>"}, "UpdateFunctionConfiguration": {"name": "UpdateFunctionConfiguration", "http": {"method": "PUT", "requestUri": "/2015-03-31/functions/{FunctionName}/configuration", "responseCode": 200}, "input": {"shape": "UpdateFunctionConfigurationRequest"}, "output": {"shape": "FunctionConfiguration"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}, {"shape": "ResourceConflictException"}, {"shape": "PreconditionFailedException"}], "documentation": "<p>Modify the version-specific settings of a Lambda function.</p> <p>When you update a function, Lambda provisions an instance of the function and its supporting resources. If your function connects to a VPC, this process can take a minute. During this time, you can't modify the function, but you can still invoke it. The <code>LastUpdateStatus</code>, <code>LastUpdateStatusReason</code>, and <code>LastUpdateStatusReasonCode</code> fields in the response from <a>GetFunctionConfiguration</a> indicate when the update is complete and the function is processing events with the new configuration. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/functions-states.html\">Function States</a>.</p> <p>These settings can vary between versions of a function and are locked when you publish a version. You can't modify the configuration of a published version, only the unpublished version.</p> <p>To configure function concurrency, use <a>PutFunctionConcurrency</a>. To grant invoke permissions to an account or AWS service, use <a>AddPermission</a>.</p>"}, "UpdateFunctionEventInvokeConfig": {"name": "UpdateFunctionEventInvokeConfig", "http": {"method": "POST", "requestUri": "/2019-09-25/functions/{FunctionName}/event-invoke-config", "responseCode": 200}, "input": {"shape": "UpdateFunctionEventInvokeConfigRequest"}, "output": {"shape": "FunctionEventInvokeConfig"}, "errors": [{"shape": "ServiceException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Updates the configuration for asynchronous invocation for a function, version, or alias.</p> <p>To configure options for asynchronous invocation, use <a>PutFunctionEventInvokeConfig</a>.</p>"}}, "shapes": {"AccountLimit": {"type": "structure", "members": {"TotalCodeSize": {"shape": "<PERSON>", "documentation": "<p>The amount of storage space that you can use for all deployment packages and layer archives.</p>"}, "CodeSizeUnzipped": {"shape": "<PERSON>", "documentation": "<p>The maximum size of a function's deployment package and layers when they're extracted.</p>"}, "CodeSizeZipped": {"shape": "<PERSON>", "documentation": "<p>The maximum size of a deployment package when it's uploaded directly to AWS Lambda. Use Amazon S3 for larger files.</p>"}, "ConcurrentExecutions": {"shape": "Integer", "documentation": "<p>The maximum number of simultaneous function executions.</p>"}, "UnreservedConcurrentExecutions": {"shape": "UnreservedConcurrentExecutions", "documentation": "<p>The maximum number of simultaneous function executions, minus the capacity that's reserved for individual functions with <a>PutFunctionConcurrency</a>.</p>"}}, "documentation": "<p>Limits that are related to concurrency and storage. All file and storage sizes are in bytes.</p>"}, "AccountUsage": {"type": "structure", "members": {"TotalCodeSize": {"shape": "<PERSON>", "documentation": "<p>The amount of storage space, in bytes, that's being used by deployment packages and layer archives.</p>"}, "FunctionCount": {"shape": "<PERSON>", "documentation": "<p>The number of Lambda functions.</p>"}}, "documentation": "<p>The number of functions and amount of storage in use.</p>"}, "Action": {"type": "string", "pattern": "(lambda:[*]|lambda:[a-zA-Z]+|[*])"}, "AddLayerVersionPermissionRequest": {"type": "structure", "required": ["Layer<PERSON>ame", "VersionNumber", "StatementId", "Action", "Principal"], "members": {"LayerName": {"shape": "Layer<PERSON>ame", "documentation": "<p>The name or Amazon Resource Name (ARN) of the layer.</p>", "location": "uri", "locationName": "Layer<PERSON>ame"}, "VersionNumber": {"shape": "LayerVersionNumber", "documentation": "<p>The version number.</p>", "location": "uri", "locationName": "VersionNumber"}, "StatementId": {"shape": "StatementId", "documentation": "<p>An identifier that distinguishes the policy from others on the same layer version.</p>"}, "Action": {"shape": "LayerPermissionAllowedAction", "documentation": "<p>The API action that grants access to the layer. For example, <code>lambda:GetLayerVersion</code>.</p>"}, "Principal": {"shape": "LayerPermissionAllowedPrincipal", "documentation": "<p>An account ID, or <code>*</code> to grant permission to all AWS accounts.</p>"}, "OrganizationId": {"shape": "OrganizationId", "documentation": "<p>With the principal set to <code>*</code>, grant permission to all accounts in the specified organization.</p>"}, "RevisionId": {"shape": "String", "documentation": "<p>Only update the policy if the revision ID matches the ID specified. Use this option to avoid modifying a policy that has changed since you last read it.</p>", "location": "querystring", "locationName": "RevisionId"}}}, "AddLayerVersionPermissionResponse": {"type": "structure", "members": {"Statement": {"shape": "String", "documentation": "<p>The permission statement.</p>"}, "RevisionId": {"shape": "String", "documentation": "<p>A unique identifier for the current revision of the policy.</p>"}}}, "AddPermissionRequest": {"type": "structure", "required": ["FunctionName", "StatementId", "Action", "Principal"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function, version, or alias.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code> (name-only), <code>my-function:v1</code> (with alias).</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>You can append a version number or alias to any of the formats. The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "StatementId": {"shape": "StatementId", "documentation": "<p>A statement identifier that differentiates the statement from others in the same policy.</p>"}, "Action": {"shape": "Action", "documentation": "<p>The action that the principal can use on the function. For example, <code>lambda:InvokeFunction</code> or <code>lambda:GetFunction</code>.</p>"}, "Principal": {"shape": "Principal", "documentation": "<p>The AWS service or account that invokes the function. If you specify a service, use <code>SourceArn</code> or <code>SourceAccount</code> to limit who can invoke the function through that service.</p>"}, "SourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>For AWS services, the ARN of the AWS resource that invokes the function. For example, an Amazon S3 bucket or Amazon SNS topic.</p>"}, "SourceAccount": {"shape": "SourceOwner", "documentation": "<p>For Amazon S3, the ID of the account that owns the resource. Use this together with <code>SourceArn</code> to ensure that the resource is owned by the specified account. It is possible for an Amazon S3 bucket to be deleted by its owner and recreated by another account.</p>"}, "EventSourceToken": {"shape": "EventSourceToken", "documentation": "<p>For Alexa Smart Home functions, a token that must be supplied by the invoker.</p>"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>Specify a version or alias to add permissions to a published version of the function.</p>", "location": "querystring", "locationName": "Qualifier"}, "RevisionId": {"shape": "String", "documentation": "<p>Only update the policy if the revision ID matches the ID that's specified. Use this option to avoid modifying a policy that has changed since you last read it.</p>"}}}, "AddPermissionResponse": {"type": "structure", "members": {"Statement": {"shape": "String", "documentation": "<p>The permission statement that's added to the function policy.</p>"}}}, "AdditionalVersion": {"type": "string", "max": 1024, "min": 1, "pattern": "[0-9]+"}, "AdditionalVersionWeights": {"type": "map", "key": {"shape": "AdditionalVersion"}, "value": {"shape": "Weight"}}, "Alias": {"type": "string", "max": 128, "min": 1, "pattern": "(?!^[0-9]+$)([a-zA-Z0-9-_]+)"}, "AliasConfiguration": {"type": "structure", "members": {"AliasArn": {"shape": "FunctionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the alias.</p>"}, "Name": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name of the alias.</p>"}, "FunctionVersion": {"shape": "Version", "documentation": "<p>The function version that the alias invokes.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the alias.</p>"}, "RoutingConfig": {"shape": "AliasRoutingConfiguration", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/lambda-traffic-shifting-using-aliases.html\">routing configuration</a> of the alias.</p>"}, "RevisionId": {"shape": "String", "documentation": "<p>A unique identifier that changes when you update the alias.</p>"}}, "documentation": "<p>Provides configuration information about a Lambda function <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/versioning-aliases.html\">alias</a>.</p>"}, "AliasList": {"type": "list", "member": {"shape": "AliasConfiguration"}}, "AliasRoutingConfiguration": {"type": "structure", "members": {"AdditionalVersionWeights": {"shape": "AdditionalVersionWeights", "documentation": "<p>The second version, and the percentage of traffic that's routed to it.</p>"}}, "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/lambda-traffic-shifting-using-aliases.html\">traffic-shifting</a> configuration of a Lambda function alias.</p>"}, "Arn": {"type": "string", "pattern": "arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-])+:([a-z]{2}(-gov)?-[a-z]+-\\d{1})?:(\\d{12})?:(.*)"}, "BatchSize": {"type": "integer", "max": 10000, "min": 1}, "BisectBatchOnFunctionError": {"type": "boolean"}, "Blob": {"type": "blob", "sensitive": true}, "BlobStream": {"type": "blob", "streaming": true}, "Boolean": {"type": "boolean"}, "CodeStorageExceededException": {"type": "structure", "members": {"Type": {"shape": "String", "documentation": "<p>The exception type.</p>"}, "message": {"shape": "String"}}, "documentation": "<p>You have exceeded your maximum total code size per account. <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/limits.html\">Learn more</a> </p>", "error": {"httpStatusCode": 400}, "exception": true}, "CompatibleRuntimes": {"type": "list", "member": {"shape": "Runtime"}, "max": 5}, "Concurrency": {"type": "structure", "members": {"ReservedConcurrentExecutions": {"shape": "ReservedConcurrentExecutions", "documentation": "<p>The number of concurrent executions that are reserved for this function. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/concurrent-executions.html\">Managing Concurrency</a>.</p>"}}}, "CreateAliasRequest": {"type": "structure", "required": ["FunctionName", "Name", "FunctionVersion"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>MyFunction</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:MyFunction</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Name": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name of the alias.</p>"}, "FunctionVersion": {"shape": "Version", "documentation": "<p>The function version that the alias invokes.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the alias.</p>"}, "RoutingConfig": {"shape": "AliasRoutingConfiguration", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-aliases.html#configuring-alias-routing\">routing configuration</a> of the alias.</p>"}}}, "CreateEventSourceMappingRequest": {"type": "structure", "required": ["EventSourceArn", "FunctionName"], "members": {"EventSourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the event source.</p> <ul> <li> <p> <b>Amazon Kinesis</b> - The ARN of the data stream or a stream consumer.</p> </li> <li> <p> <b>Amazon DynamoDB Streams</b> - The ARN of the stream.</p> </li> <li> <p> <b>Amazon Simple Queue Service</b> - The ARN of the queue.</p> </li> </ul>"}, "FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>MyFunction</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction</code>.</p> </li> <li> <p> <b>Version or Alias ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction:PROD</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:MyFunction</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it's limited to 64 characters in length.</p>"}, "Enabled": {"shape": "Enabled", "documentation": "<p>Disables the event source mapping to pause polling and invocation.</p>"}, "BatchSize": {"shape": "BatchSize", "documentation": "<p>The maximum number of items to retrieve in a single batch.</p> <ul> <li> <p> <b>Amazon Kinesis</b> - Default 100. Max 10,000.</p> </li> <li> <p> <b>Amazon DynamoDB Streams</b> - Default 100. Max 1,000.</p> </li> <li> <p> <b>Amazon Simple Queue Service</b> - Default 10. Max 10.</p> </li> </ul>"}, "MaximumBatchingWindowInSeconds": {"shape": "MaximumBatchingWindowInSeconds", "documentation": "<p>(Streams) The maximum amount of time to gather records before invoking the function, in seconds.</p>"}, "ParallelizationFactor": {"shape": "ParallelizationFactor", "documentation": "<p>(Streams) The number of batches to process from each shard concurrently.</p>"}, "StartingPosition": {"shape": "EventSourcePosition", "documentation": "<p>The position in a stream from which to start reading. Required for Amazon Kinesis and Amazon DynamoDB Streams sources. <code>AT_TIMESTAMP</code> is only supported for Amazon Kinesis streams.</p>"}, "StartingPositionTimestamp": {"shape": "Date", "documentation": "<p>With <code>StartingPosition</code> set to <code>AT_TIMESTAMP</code>, the time from which to start reading.</p>"}, "DestinationConfig": {"shape": "DestinationConfig", "documentation": "<p>(Streams) An Amazon SQS queue or Amazon SNS topic destination for discarded records.</p>"}, "MaximumRecordAgeInSeconds": {"shape": "MaximumRecordAgeInSeconds", "documentation": "<p>(Streams) The maximum age of a record that Lambda sends to a function for processing.</p>"}, "BisectBatchOnFunctionError": {"shape": "BisectBatchOnFunctionError", "documentation": "<p>(Streams) If the function returns an error, split the batch in two and retry.</p>"}, "MaximumRetryAttempts": {"shape": "MaximumRetryAttemptsEventSourceMapping", "documentation": "<p>(Streams) The maximum number of times to retry when the function returns an error.</p>"}}}, "CreateFunctionRequest": {"type": "structure", "required": ["FunctionName", "Runtime", "Role", "Handler", "Code"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>"}, "Runtime": {"shape": "Runtime", "documentation": "<p>The identifier of the function's <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/lambda-runtimes.html\">runtime</a>.</p>"}, "Role": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the function's execution role.</p>"}, "Handler": {"shape": "Handler", "documentation": "<p>The name of the method within your code that Lambda calls to execute your function. The format includes the file name. It can also include namespaces and other qualifiers, depending on the runtime. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/programming-model-v2.html\">Programming Model</a>.</p>"}, "Code": {"shape": "FunctionCode", "documentation": "<p>The code for the function.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the function.</p>"}, "Timeout": {"shape": "Timeout", "documentation": "<p>The amount of time that Lambda allows a function to run before stopping it. The default is 3 seconds. The maximum allowed value is 900 seconds.</p>"}, "MemorySize": {"shape": "MemorySize", "documentation": "<p>The amount of memory that your function has access to. Increasing the function's memory also increases its CPU allocation. The default value is 128 MB. The value must be a multiple of 64 MB.</p>"}, "Publish": {"shape": "Boolean", "documentation": "<p>Set to true to publish the first version of the function during creation.</p>"}, "VpcConfig": {"shape": "VpcConfig", "documentation": "<p>For network connectivity to AWS resources in a VPC, specify a list of security groups and subnets in the VPC. When you connect a function to a VPC, it can only access resources and the internet through that VPC. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-vpc.html\">VPC Settings</a>.</p>"}, "DeadLetterConfig": {"shape": "DeadLetterConfig", "documentation": "<p>A dead letter queue configuration that specifies the queue or topic where Lambda sends asynchronous events when they fail processing. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/invocation-async.html#dlq\">Dead Letter Queues</a>.</p>"}, "Environment": {"shape": "Environment", "documentation": "<p>Environment variables that are accessible from function code during execution.</p>"}, "KMSKeyArn": {"shape": "KMSKeyArn", "documentation": "<p>The ARN of the AWS Key Management Service (AWS KMS) key that's used to encrypt your function's environment variables. If it's not provided, AWS Lambda uses a default service key.</p>"}, "TracingConfig": {"shape": "TracingConfig", "documentation": "<p>Set <code>Mode</code> to <code>Active</code> to sample and trace a subset of incoming requests with AWS X-Ray.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/tagging.html\">tags</a> to apply to the function.</p>"}, "Layers": {"shape": "LayerList", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">function layers</a> to add to the function's execution environment. Specify each layer by its ARN, including the version.</p>"}, "FileSystemConfigs": {"shape": "FileSystemConfigList", "documentation": "<p>Connection settings for an Amazon EFS file system.</p>"}}}, "Date": {"type": "timestamp"}, "DeadLetterConfig": {"type": "structure", "members": {"TargetArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Amazon SQS queue or Amazon SNS topic.</p>"}}, "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/invocation-async.html#dlq\">dead-letter queue</a> for failed asynchronous invocations.</p>"}, "DeleteAliasRequest": {"type": "structure", "required": ["FunctionName", "Name"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>MyFunction</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:MyFunction</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Name": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name of the alias.</p>", "location": "uri", "locationName": "Name"}}}, "DeleteEventSourceMappingRequest": {"type": "structure", "required": ["UUID"], "members": {"UUID": {"shape": "String", "documentation": "<p>The identifier of the event source mapping.</p>", "location": "uri", "locationName": "UUID"}}}, "DeleteFunctionConcurrencyRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}}}, "DeleteFunctionEventInvokeConfigRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function, version, or alias.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code> (name-only), <code>my-function:v1</code> (with alias).</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>You can append a version number or alias to any of the formats. The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>A version number or alias name.</p>", "location": "querystring", "locationName": "Qualifier"}}}, "DeleteFunctionRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function or version.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code> (name-only), <code>my-function:1</code> (with version).</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>You can append a version number or alias to any of the formats. The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>Specify a version to delete. You can't delete a version that's referenced by an alias.</p>", "location": "querystring", "locationName": "Qualifier"}}}, "DeleteLayerVersionRequest": {"type": "structure", "required": ["Layer<PERSON>ame", "VersionNumber"], "members": {"LayerName": {"shape": "Layer<PERSON>ame", "documentation": "<p>The name or Amazon Resource Name (ARN) of the layer.</p>", "location": "uri", "locationName": "Layer<PERSON>ame"}, "VersionNumber": {"shape": "LayerVersionNumber", "documentation": "<p>The version number.</p>", "location": "uri", "locationName": "VersionNumber"}}}, "DeleteProvisionedConcurrencyConfigRequest": {"type": "structure", "required": ["FunctionName", "Qualifier"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>The version number or alias name.</p>", "location": "querystring", "locationName": "Qualifier"}}}, "Description": {"type": "string", "max": 256, "min": 0}, "DestinationArn": {"type": "string", "max": 350, "min": 0, "pattern": "^$|arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-])+:([a-z]{2}(-gov)?-[a-z]+-\\d{1})?:(\\d{12})?:(.*)"}, "DestinationConfig": {"type": "structure", "members": {"OnSuccess": {"shape": "OnSuccess", "documentation": "<p>The destination configuration for successful invocations.</p>"}, "OnFailure": {"shape": "OnFailure", "documentation": "<p>The destination configuration for failed invocations.</p>"}}, "documentation": "<p>A configuration object that specifies the destination of an event after Lambda processes it.</p>"}, "EC2AccessDeniedException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>Need additional permissions to configure VPC settings.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "EC2ThrottledException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>AWS Lambda was throttled by Amazon EC2 during Lambda function initialization using the execution role provided for the Lambda function.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "EC2UnexpectedException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}, "EC2ErrorCode": {"shape": "String"}}, "documentation": "<p>AWS Lambda received an unexpected EC2 client exception while setting up for the Lambda function.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "EFSIOException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>An error occured when reading from or writing to a connected file system.</p>", "error": {"httpStatusCode": 410}, "exception": true}, "EFSMountConnectivityException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>The function couldn't make a network connection to the configured file system.</p>", "error": {"httpStatusCode": 408}, "exception": true}, "EFSMountFailureException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>The function couldn't mount the configured file system due to a permission or configuration issue.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "EFSMountTimeoutException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>The function was able to make a network connection to the configured file system, but the mount operation timed out.</p>", "error": {"httpStatusCode": 408}, "exception": true}, "ENILimitReachedException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>AWS Lambda was not able to create an elastic network interface in the VPC, specified as part of Lambda function configuration, because the limit for network interfaces has been reached.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "Enabled": {"type": "boolean"}, "Environment": {"type": "structure", "members": {"Variables": {"shape": "EnvironmentVariables", "documentation": "<p>Environment variable key-value pairs.</p>"}}, "documentation": "<p>A function's environment variable settings.</p>"}, "EnvironmentError": {"type": "structure", "members": {"ErrorCode": {"shape": "String", "documentation": "<p>The error code.</p>"}, "Message": {"shape": "SensitiveString", "documentation": "<p>The error message.</p>"}}, "documentation": "<p>Error messages for environment variables that couldn't be applied.</p>"}, "EnvironmentResponse": {"type": "structure", "members": {"Variables": {"shape": "EnvironmentVariables", "documentation": "<p>Environment variable key-value pairs.</p>"}, "Error": {"shape": "EnvironmentError", "documentation": "<p>Error messages for environment variables that couldn't be applied.</p>"}}, "documentation": "<p>The results of an operation to update or read environment variables. If the operation is successful, the response contains the environment variables. If it failed, the response contains details about the error.</p>"}, "EnvironmentVariableName": {"type": "string", "pattern": "[a-zA-Z]([a-zA-Z0-9_])+", "sensitive": true}, "EnvironmentVariableValue": {"type": "string", "sensitive": true}, "EnvironmentVariables": {"type": "map", "key": {"shape": "EnvironmentVariableName"}, "value": {"shape": "EnvironmentVariableValue"}, "sensitive": true}, "EventSourceMappingConfiguration": {"type": "structure", "members": {"UUID": {"shape": "String", "documentation": "<p>The identifier of the event source mapping.</p>"}, "BatchSize": {"shape": "BatchSize", "documentation": "<p>The maximum number of items to retrieve in a single batch.</p>"}, "MaximumBatchingWindowInSeconds": {"shape": "MaximumBatchingWindowInSeconds", "documentation": "<p>(Streams) The maximum amount of time to gather records before invoking the function, in seconds.</p>"}, "ParallelizationFactor": {"shape": "ParallelizationFactor", "documentation": "<p>(Streams) The number of batches to process from each shard concurrently.</p>"}, "EventSourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the event source.</p>"}, "FunctionArn": {"shape": "FunctionArn", "documentation": "<p>The ARN of the Lambda function.</p>"}, "LastModified": {"shape": "Date", "documentation": "<p>The date that the event source mapping was last updated, or its state changed.</p>"}, "LastProcessingResult": {"shape": "String", "documentation": "<p>The result of the last AWS Lambda invocation of your Lambda function.</p>"}, "State": {"shape": "String", "documentation": "<p>The state of the event source mapping. It can be one of the following: <code>Creating</code>, <code>Enabling</code>, <code>Enabled</code>, <code>Disabling</code>, <code>Disabled</code>, <code>Updating</code>, or <code>Deleting</code>.</p>"}, "StateTransitionReason": {"shape": "String", "documentation": "<p>Indicates whether the last change to the event source mapping was made by a user, or by the Lambda service.</p>"}, "DestinationConfig": {"shape": "DestinationConfig", "documentation": "<p>(Streams) An Amazon SQS queue or Amazon SNS topic destination for discarded records.</p>"}, "MaximumRecordAgeInSeconds": {"shape": "MaximumRecordAgeInSeconds", "documentation": "<p>(Streams) The maximum age of a record that Lambda sends to a function for processing.</p>"}, "BisectBatchOnFunctionError": {"shape": "BisectBatchOnFunctionError", "documentation": "<p>(Streams) If the function returns an error, split the batch in two and retry.</p>"}, "MaximumRetryAttempts": {"shape": "MaximumRetryAttemptsEventSourceMapping", "documentation": "<p>(Streams) The maximum number of times to retry when the function returns an error.</p>"}}, "documentation": "<p>A mapping between an AWS resource and an AWS Lambda function. See <a>CreateEventSourceMapping</a> for details.</p>"}, "EventSourceMappingsList": {"type": "list", "member": {"shape": "EventSourceMappingConfiguration"}}, "EventSourcePosition": {"type": "string", "enum": ["TRIM_HORIZON", "LATEST", "AT_TIMESTAMP"]}, "EventSourceToken": {"type": "string", "max": 256, "min": 0, "pattern": "[a-zA-Z0-9._\\-]+"}, "FileSystemArn": {"type": "string", "max": 200, "pattern": "arn:aws[a-zA-Z-]*:elasticfilesystem:[a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:\\d{12}:access-point/fsap-[a-f0-9]{17}"}, "FileSystemConfig": {"type": "structure", "required": ["<PERSON><PERSON>", "LocalMountPath"], "members": {"Arn": {"shape": "FileSystemArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon EFS access point that provides access to the file system.</p>"}, "LocalMountPath": {"shape": "LocalMountPath", "documentation": "<p>The path where the function can access the file system, starting with <code>/mnt/</code>.</p>"}}, "documentation": "<p>Details about the connection between a Lambda function and an Amazon EFS file system.</p>"}, "FileSystemConfigList": {"type": "list", "member": {"shape": "FileSystemConfig"}, "max": 1}, "FunctionArn": {"type": "string", "pattern": "arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?"}, "FunctionCode": {"type": "structure", "members": {"ZipFile": {"shape": "Blob", "documentation": "<p>The base64-encoded contents of the deployment package. AWS SDK and AWS CLI clients handle the encoding for you.</p>"}, "S3Bucket": {"shape": "S3Bucket", "documentation": "<p>An Amazon S3 bucket in the same AWS Region as your function. The bucket can be in a different AWS account.</p>"}, "S3Key": {"shape": "S3Key", "documentation": "<p>The Amazon S3 key of the deployment package.</p>"}, "S3ObjectVersion": {"shape": "S3ObjectVersion", "documentation": "<p>For versioned objects, the version of the deployment package object to use.</p>"}}, "documentation": "<p>The code for the Lambda function. You can specify either an object in Amazon S3, or upload a deployment package directly.</p>"}, "FunctionCodeLocation": {"type": "structure", "members": {"RepositoryType": {"shape": "String", "documentation": "<p>The service that's hosting the file.</p>"}, "Location": {"shape": "String", "documentation": "<p>A presigned URL that you can use to download the deployment package.</p>"}}, "documentation": "<p>Details about a function's deployment package.</p>"}, "FunctionConfiguration": {"type": "structure", "members": {"FunctionName": {"shape": "NamespacedFunctionName", "documentation": "<p>The name of the function.</p>"}, "FunctionArn": {"shape": "NameSpacedFunctionArn", "documentation": "<p>The function's Amazon Resource Name (ARN).</p>"}, "Runtime": {"shape": "Runtime", "documentation": "<p>The runtime environment for the Lambda function.</p>"}, "Role": {"shape": "RoleArn", "documentation": "<p>The function's execution role.</p>"}, "Handler": {"shape": "Handler", "documentation": "<p>The function that Lambda calls to begin executing your function.</p>"}, "CodeSize": {"shape": "<PERSON>", "documentation": "<p>The size of the function's deployment package, in bytes.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The function's description.</p>"}, "Timeout": {"shape": "Timeout", "documentation": "<p>The amount of time in seconds that Lambda allows a function to run before stopping it.</p>"}, "MemorySize": {"shape": "MemorySize", "documentation": "<p>The memory that's allocated to the function.</p>"}, "LastModified": {"shape": "Timestamp", "documentation": "<p>The date and time that the function was last updated, in <a href=\"https://www.w3.org/TR/NOTE-datetime\">ISO-8601 format</a> (YYYY-MM-DDThh:mm:ss.sTZD).</p>"}, "CodeSha256": {"shape": "String", "documentation": "<p>The SHA256 hash of the function's deployment package.</p>"}, "Version": {"shape": "Version", "documentation": "<p>The version of the Lambda function.</p>"}, "VpcConfig": {"shape": "VpcConfigResponse", "documentation": "<p>The function's networking configuration.</p>"}, "DeadLetterConfig": {"shape": "DeadLetterConfig", "documentation": "<p>The function's dead letter queue.</p>"}, "Environment": {"shape": "EnvironmentResponse", "documentation": "<p>The function's environment variables.</p>"}, "KMSKeyArn": {"shape": "KMSKeyArn", "documentation": "<p>The KMS key that's used to encrypt the function's environment variables. This key is only returned if you've configured a customer managed CMK.</p>"}, "TracingConfig": {"shape": "TracingConfigResponse", "documentation": "<p>The function's AWS X-Ray tracing configuration.</p>"}, "MasterArn": {"shape": "FunctionArn", "documentation": "<p>For Lambda@Edge functions, the ARN of the master function.</p>"}, "RevisionId": {"shape": "String", "documentation": "<p>The latest updated revision of the function or alias.</p>"}, "Layers": {"shape": "LayersReferenceList", "documentation": "<p>The function's <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\"> layers</a>.</p>"}, "State": {"shape": "State", "documentation": "<p>The current state of the function. When the state is <code>Inactive</code>, you can reactivate the function by invoking it.</p>"}, "StateReason": {"shape": "StateReason", "documentation": "<p>The reason for the function's current state.</p>"}, "StateReasonCode": {"shape": "StateReasonCode", "documentation": "<p>The reason code for the function's current state. When the code is <code>Creating</code>, you can't invoke or modify the function.</p>"}, "LastUpdateStatus": {"shape": "LastUpdateStatus", "documentation": "<p>The status of the last update that was performed on the function. This is first set to <code>Successful</code> after function creation completes.</p>"}, "LastUpdateStatusReason": {"shape": "LastUpdateStatusReason", "documentation": "<p>The reason for the last update that was performed on the function.</p>"}, "LastUpdateStatusReasonCode": {"shape": "LastUpdateStatusReasonCode", "documentation": "<p>The reason code for the last update that was performed on the function.</p>"}, "FileSystemConfigs": {"shape": "FileSystemConfigList", "documentation": "<p>Connection settings for an Amazon EFS file system.</p>"}}, "documentation": "<p>Details about a function's configuration.</p>"}, "FunctionEventInvokeConfig": {"type": "structure", "members": {"LastModified": {"shape": "Date", "documentation": "<p>The date and time that the configuration was last updated.</p>"}, "FunctionArn": {"shape": "FunctionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the function.</p>"}, "MaximumRetryAttempts": {"shape": "MaximumRetryAttempts", "documentation": "<p>The maximum number of times to retry when the function returns an error.</p>"}, "MaximumEventAgeInSeconds": {"shape": "MaximumEventAgeInSeconds", "documentation": "<p>The maximum age of a request that Lambda sends to a function for processing.</p>"}, "DestinationConfig": {"shape": "DestinationConfig", "documentation": "<p>A destination for events after they have been sent to a function for processing.</p> <p class=\"title\"> <b>Destinations</b> </p> <ul> <li> <p> <b>Function</b> - The Amazon Resource Name (ARN) of a Lambda function.</p> </li> <li> <p> <b>Queue</b> - The ARN of an SQS queue.</p> </li> <li> <p> <b>Topic</b> - The ARN of an SNS topic.</p> </li> <li> <p> <b>Event Bus</b> - The ARN of an Amazon EventBridge event bus.</p> </li> </ul>"}}}, "FunctionEventInvokeConfigList": {"type": "list", "member": {"shape": "FunctionEventInvokeConfig"}}, "FunctionList": {"type": "list", "member": {"shape": "FunctionConfiguration"}}, "FunctionName": {"type": "string", "max": 140, "min": 1, "pattern": "(arn:(aws[a-zA-Z-]*)?:lambda:)?([a-z]{2}(-gov)?-[a-z]+-\\d{1}:)?(\\d{12}:)?(function:)?([a-zA-Z0-9-_]+)(:(\\$LATEST|[a-zA-Z0-9-_]+))?"}, "FunctionVersion": {"type": "string", "enum": ["ALL"]}, "GetAccountSettingsRequest": {"type": "structure", "members": {}}, "GetAccountSettingsResponse": {"type": "structure", "members": {"AccountLimit": {"shape": "AccountLimit", "documentation": "<p>Limits that are related to concurrency and code storage.</p>"}, "AccountUsage": {"shape": "AccountUsage", "documentation": "<p>The number of functions and amount of storage in use.</p>"}}}, "GetAliasRequest": {"type": "structure", "required": ["FunctionName", "Name"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>MyFunction</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:MyFunction</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Name": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name of the alias.</p>", "location": "uri", "locationName": "Name"}}}, "GetEventSourceMappingRequest": {"type": "structure", "required": ["UUID"], "members": {"UUID": {"shape": "String", "documentation": "<p>The identifier of the event source mapping.</p>", "location": "uri", "locationName": "UUID"}}}, "GetFunctionConcurrencyRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}}}, "GetFunctionConcurrencyResponse": {"type": "structure", "members": {"ReservedConcurrentExecutions": {"shape": "ReservedConcurrentExecutions", "documentation": "<p>The number of simultaneous executions that are reserved for the function.</p>"}}}, "GetFunctionConfigurationRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "NamespacedFunctionName", "documentation": "<p>The name of the Lambda function, version, or alias.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code> (name-only), <code>my-function:v1</code> (with alias).</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>You can append a version number or alias to any of the formats. The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>Specify a version or alias to get details about a published version of the function.</p>", "location": "querystring", "locationName": "Qualifier"}}}, "GetFunctionEventInvokeConfigRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function, version, or alias.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code> (name-only), <code>my-function:v1</code> (with alias).</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>You can append a version number or alias to any of the formats. The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>A version number or alias name.</p>", "location": "querystring", "locationName": "Qualifier"}}}, "GetFunctionRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "NamespacedFunctionName", "documentation": "<p>The name of the Lambda function, version, or alias.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code> (name-only), <code>my-function:v1</code> (with alias).</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>You can append a version number or alias to any of the formats. The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>Specify a version or alias to get details about a published version of the function.</p>", "location": "querystring", "locationName": "Qualifier"}}}, "GetFunctionResponse": {"type": "structure", "members": {"Configuration": {"shape": "FunctionConfiguration", "documentation": "<p>The configuration of the function or version.</p>"}, "Code": {"shape": "FunctionCodeLocation", "documentation": "<p>The deployment package of the function or version.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The function's <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/tagging.html\">tags</a>.</p>"}, "Concurrency": {"shape": "Concurrency", "documentation": "<p>The function's <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/concurrent-executions.html\">reserved concurrency</a>.</p>"}}}, "GetLayerVersionByArnRequest": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Arn": {"shape": "LayerVersionArn", "documentation": "<p>The ARN of the layer version.</p>", "location": "querystring", "locationName": "<PERSON><PERSON>"}}}, "GetLayerVersionPolicyRequest": {"type": "structure", "required": ["Layer<PERSON>ame", "VersionNumber"], "members": {"LayerName": {"shape": "Layer<PERSON>ame", "documentation": "<p>The name or Amazon Resource Name (ARN) of the layer.</p>", "location": "uri", "locationName": "Layer<PERSON>ame"}, "VersionNumber": {"shape": "LayerVersionNumber", "documentation": "<p>The version number.</p>", "location": "uri", "locationName": "VersionNumber"}}}, "GetLayerVersionPolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "String", "documentation": "<p>The policy document.</p>"}, "RevisionId": {"shape": "String", "documentation": "<p>A unique identifier for the current revision of the policy.</p>"}}}, "GetLayerVersionRequest": {"type": "structure", "required": ["Layer<PERSON>ame", "VersionNumber"], "members": {"LayerName": {"shape": "Layer<PERSON>ame", "documentation": "<p>The name or Amazon Resource Name (ARN) of the layer.</p>", "location": "uri", "locationName": "Layer<PERSON>ame"}, "VersionNumber": {"shape": "LayerVersionNumber", "documentation": "<p>The version number.</p>", "location": "uri", "locationName": "VersionNumber"}}}, "GetLayerVersionResponse": {"type": "structure", "members": {"Content": {"shape": "LayerVersionContentOutput", "documentation": "<p>Details about the layer version.</p>"}, "LayerArn": {"shape": "LayerArn", "documentation": "<p>The ARN of the layer.</p>"}, "LayerVersionArn": {"shape": "LayerVersionArn", "documentation": "<p>The ARN of the layer version.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the version.</p>"}, "CreatedDate": {"shape": "Timestamp", "documentation": "<p>The date that the layer version was created, in <a href=\"https://www.w3.org/TR/NOTE-datetime\">ISO-8601 format</a> (YYYY-MM-DDThh:mm:ss.sTZD).</p>"}, "Version": {"shape": "LayerVersionNumber", "documentation": "<p>The version number.</p>"}, "CompatibleRuntimes": {"shape": "CompatibleRuntimes", "documentation": "<p>The layer's compatible runtimes.</p>"}, "LicenseInfo": {"shape": "LicenseInfo", "documentation": "<p>The layer's software license.</p>"}}}, "GetPolicyRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "NamespacedFunctionName", "documentation": "<p>The name of the Lambda function, version, or alias.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code> (name-only), <code>my-function:v1</code> (with alias).</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>You can append a version number or alias to any of the formats. The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>Specify a version or alias to get the policy for that resource.</p>", "location": "querystring", "locationName": "Qualifier"}}}, "GetPolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "String", "documentation": "<p>The resource-based policy.</p>"}, "RevisionId": {"shape": "String", "documentation": "<p>A unique identifier for the current revision of the policy.</p>"}}}, "GetProvisionedConcurrencyConfigRequest": {"type": "structure", "required": ["FunctionName", "Qualifier"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>The version number or alias name.</p>", "location": "querystring", "locationName": "Qualifier"}}}, "GetProvisionedConcurrencyConfigResponse": {"type": "structure", "members": {"RequestedProvisionedConcurrentExecutions": {"shape": "PositiveInteger", "documentation": "<p>The amount of provisioned concurrency requested.</p>"}, "AvailableProvisionedConcurrentExecutions": {"shape": "NonNegativeInteger", "documentation": "<p>The amount of provisioned concurrency available.</p>"}, "AllocatedProvisionedConcurrentExecutions": {"shape": "NonNegativeInteger", "documentation": "<p>The amount of provisioned concurrency allocated.</p>"}, "Status": {"shape": "ProvisionedConcurrencyStatusEnum", "documentation": "<p>The status of the allocation process.</p>"}, "StatusReason": {"shape": "String", "documentation": "<p>For failed allocations, the reason that provisioned concurrency could not be allocated.</p>"}, "LastModified": {"shape": "Timestamp", "documentation": "<p>The date and time that a user last updated the configuration, in <a href=\"https://www.iso.org/iso-8601-date-and-time-format.html\">ISO 8601 format</a>.</p>"}}}, "Handler": {"type": "string", "max": 128, "pattern": "[^\\s]+"}, "HttpStatus": {"type": "integer"}, "Integer": {"type": "integer"}, "InvalidParameterValueException": {"type": "structure", "members": {"Type": {"shape": "String", "documentation": "<p>The exception type.</p>"}, "message": {"shape": "String", "documentation": "<p>The exception message.</p>"}}, "documentation": "<p>One of the parameters in the request is invalid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidRequestContentException": {"type": "structure", "members": {"Type": {"shape": "String", "documentation": "<p>The exception type.</p>"}, "message": {"shape": "String", "documentation": "<p>The exception message.</p>"}}, "documentation": "<p>The request body could not be parsed as JSON.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidRuntimeException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>The runtime or runtime version specified is not supported.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "InvalidSecurityGroupIDException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>The Security Group ID provided in the Lambda function VPC configuration is invalid.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "InvalidSubnetIDException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>The Subnet ID provided in the Lambda function VPC configuration is invalid.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "InvalidZipFileException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>AWS Lambda could not unzip the deployment package.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "InvocationRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "NamespacedFunctionName", "documentation": "<p>The name of the Lambda function, version, or alias.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code> (name-only), <code>my-function:v1</code> (with alias).</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>You can append a version number or alias to any of the formats. The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "InvocationType": {"shape": "InvocationType", "documentation": "<p>Choose from the following options.</p> <ul> <li> <p> <code>RequestResponse</code> (default) - Invoke the function synchronously. Keep the connection open until the function returns a response or times out. The API response includes the function response and additional data.</p> </li> <li> <p> <code>Event</code> - Invoke the function asynchronously. Send events that fail multiple times to the function's dead-letter queue (if it's configured). The API response only includes a status code.</p> </li> <li> <p> <code>DryRun</code> - Validate parameter values and verify that the user or role has permission to invoke the function.</p> </li> </ul>", "location": "header", "locationName": "X-Amz-Invocation-Type"}, "LogType": {"shape": "LogType", "documentation": "<p>Set to <code>Tail</code> to include the execution log in the response.</p>", "location": "header", "locationName": "X-Amz-Log-Type"}, "ClientContext": {"shape": "String", "documentation": "<p>Up to 3583 bytes of base64-encoded data about the invoking client to pass to the function in the context object.</p>", "location": "header", "locationName": "X-Amz-Client-Context"}, "Payload": {"shape": "Blob", "documentation": "<p>The JSON that you want to provide to your Lambda function as input.</p>"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>Specify a version or alias to invoke a published version of the function.</p>", "location": "querystring", "locationName": "Qualifier"}}, "payload": "Payload"}, "InvocationResponse": {"type": "structure", "members": {"StatusCode": {"shape": "Integer", "documentation": "<p>The HTTP status code is in the 200 range for a successful request. For the <code>RequestResponse</code> invocation type, this status code is 200. For the <code>Event</code> invocation type, this status code is 202. For the <code>DryRun</code> invocation type, the status code is 204.</p>", "location": "statusCode"}, "FunctionError": {"shape": "String", "documentation": "<p>If present, indicates that an error occurred during function execution. Details about the error are included in the response payload.</p>", "location": "header", "locationName": "X-Amz-Function-Error"}, "LogResult": {"shape": "String", "documentation": "<p>The last 4 KB of the execution log, which is base64 encoded.</p>", "location": "header", "locationName": "X-Amz-Log-Result"}, "Payload": {"shape": "Blob", "documentation": "<p>The response from the function, or an error object.</p>"}, "ExecutedVersion": {"shape": "Version", "documentation": "<p>The version of the function that executed. When you invoke a function with an alias, this indicates which version the alias resolved to.</p>", "location": "header", "locationName": "X-Amz-Executed-Version"}}, "payload": "Payload"}, "InvocationType": {"type": "string", "enum": ["Event", "RequestResponse", "DryRun"]}, "InvokeAsyncRequest": {"type": "structure", "required": ["FunctionName", "InvokeArgs"], "members": {"FunctionName": {"shape": "NamespacedFunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "InvokeArgs": {"shape": "BlobStream", "documentation": "<p>The JSON that you want to provide to your Lambda function as input.</p>"}}, "deprecated": true, "payload": "InvokeArgs"}, "InvokeAsyncResponse": {"type": "structure", "members": {"Status": {"shape": "HttpStatus", "documentation": "<p>The status code.</p>", "location": "statusCode"}}, "documentation": "<p>A success response (<code>202 Accepted</code>) indicates that the request is queued for invocation. </p>", "deprecated": true}, "KMSAccessDeniedException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>Lambda was unable to decrypt the environment variables because KMS access was denied. Check the Lambda function's KMS permissions.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "KMSDisabledException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>Lambda was unable to decrypt the environment variables because the KMS key used is disabled. Check the Lambda function's KMS key settings.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "KMSInvalidStateException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p><PERSON><PERSON> was unable to decrypt the environment variables because the KMS key used is in an invalid state for Decrypt. Check the function's KMS key settings.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "KMSKeyArn": {"type": "string", "pattern": "(arn:(aws[a-zA-Z-]*)?:[a-z0-9-.]+:.*)|()"}, "KMSNotFoundException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p><PERSON><PERSON> was unable to decrypt the environment variables because the KMS key was not found. Check the function's KMS key settings. </p>", "error": {"httpStatusCode": 502}, "exception": true}, "LastUpdateStatus": {"type": "string", "enum": ["Successful", "Failed", "InProgress"]}, "LastUpdateStatusReason": {"type": "string"}, "LastUpdateStatusReasonCode": {"type": "string", "enum": ["EniLimitExceeded", "InsufficientRolePermissions", "InvalidConfiguration", "InternalError", "SubnetOutOfIPAddresses", "InvalidSubnet", "InvalidSecurityGroup"]}, "Layer": {"type": "structure", "members": {"Arn": {"shape": "LayerVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the function layer.</p>"}, "CodeSize": {"shape": "<PERSON>", "documentation": "<p>The size of the layer archive in bytes.</p>"}}, "documentation": "<p>An <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a>.</p>"}, "LayerArn": {"type": "string", "max": 140, "min": 1, "pattern": "arn:[a-zA-Z0-9-]+:lambda:[a-zA-Z0-9-]+:\\d{12}:layer:[a-zA-Z0-9-_]+"}, "LayerList": {"type": "list", "member": {"shape": "LayerVersionArn"}}, "LayerName": {"type": "string", "max": 140, "min": 1, "pattern": "(arn:[a-zA-Z0-9-]+:lambda:[a-zA-Z0-9-]+:\\d{12}:layer:[a-zA-Z0-9-_]+)|[a-zA-Z0-9-_]+"}, "LayerPermissionAllowedAction": {"type": "string", "pattern": "lambda:GetLayerVersion"}, "LayerPermissionAllowedPrincipal": {"type": "string", "pattern": "\\d{12}|\\*|arn:(aws[a-zA-Z-]*):iam::\\d{12}:root"}, "LayerVersionArn": {"type": "string", "max": 140, "min": 1, "pattern": "arn:[a-zA-Z0-9-]+:lambda:[a-zA-Z0-9-]+:\\d{12}:layer:[a-zA-Z0-9-_]+:[0-9]+"}, "LayerVersionContentInput": {"type": "structure", "members": {"S3Bucket": {"shape": "S3Bucket", "documentation": "<p>The Amazon S3 bucket of the layer archive.</p>"}, "S3Key": {"shape": "S3Key", "documentation": "<p>The Amazon S3 key of the layer archive.</p>"}, "S3ObjectVersion": {"shape": "S3ObjectVersion", "documentation": "<p>For versioned objects, the version of the layer archive object to use.</p>"}, "ZipFile": {"shape": "Blob", "documentation": "<p>The base64-encoded contents of the layer archive. AWS SDK and AWS CLI clients handle the encoding for you.</p>"}}, "documentation": "<p>A ZIP archive that contains the contents of an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a>. You can specify either an Amazon S3 location, or upload a layer archive directly.</p>"}, "LayerVersionContentOutput": {"type": "structure", "members": {"Location": {"shape": "String", "documentation": "<p>A link to the layer archive in Amazon S3 that is valid for 10 minutes.</p>"}, "CodeSha256": {"shape": "String", "documentation": "<p>The SHA-256 hash of the layer archive.</p>"}, "CodeSize": {"shape": "<PERSON>", "documentation": "<p>The size of the layer archive in bytes.</p>"}}, "documentation": "<p>Details about a version of an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a>.</p>"}, "LayerVersionNumber": {"type": "long"}, "LayerVersionsList": {"type": "list", "member": {"shape": "LayerVersionsListItem"}}, "LayerVersionsListItem": {"type": "structure", "members": {"LayerVersionArn": {"shape": "LayerVersionArn", "documentation": "<p>The ARN of the layer version.</p>"}, "Version": {"shape": "LayerVersionNumber", "documentation": "<p>The version number.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the version.</p>"}, "CreatedDate": {"shape": "Timestamp", "documentation": "<p>The date that the version was created, in ISO 8601 format. For example, <code>2018-11-27T15:10:45.123+0000</code>.</p>"}, "CompatibleRuntimes": {"shape": "CompatibleRuntimes", "documentation": "<p>The layer's compatible runtimes.</p>"}, "LicenseInfo": {"shape": "LicenseInfo", "documentation": "<p>The layer's open-source license.</p>"}}, "documentation": "<p>Details about a version of an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a>.</p>"}, "LayersList": {"type": "list", "member": {"shape": "LayersListItem"}}, "LayersListItem": {"type": "structure", "members": {"LayerName": {"shape": "Layer<PERSON>ame", "documentation": "<p>The name of the layer.</p>"}, "LayerArn": {"shape": "LayerArn", "documentation": "<p>The Amazon Resource Name (ARN) of the function layer.</p>"}, "LatestMatchingVersion": {"shape": "LayerVersionsListItem", "documentation": "<p>The newest version of the layer.</p>"}}, "documentation": "<p>Details about an <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">AWS Lambda layer</a>.</p>"}, "LayersReferenceList": {"type": "list", "member": {"shape": "Layer"}}, "LicenseInfo": {"type": "string", "max": 512}, "ListAliasesRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>MyFunction</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:MyFunction</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "FunctionVersion": {"shape": "Version", "documentation": "<p>Specify a function version to only list aliases that invoke that version.</p>", "location": "querystring", "locationName": "FunctionVersion"}, "Marker": {"shape": "String", "documentation": "<p>Specify the pagination token that's returned by a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "<PERSON><PERSON>"}, "MaxItems": {"shape": "MaxListItems", "documentation": "<p>Limit the number of aliases returned.</p>", "location": "querystring", "locationName": "MaxItems"}}}, "ListAliasesResponse": {"type": "structure", "members": {"NextMarker": {"shape": "String", "documentation": "<p>The pagination token that's included if more results are available.</p>"}, "Aliases": {"shape": "AliasList", "documentation": "<p>A list of aliases.</p>"}}}, "ListEventSourceMappingsRequest": {"type": "structure", "members": {"EventSourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the event source.</p> <ul> <li> <p> <b>Amazon Kinesis</b> - The ARN of the data stream or a stream consumer.</p> </li> <li> <p> <b>Amazon DynamoDB Streams</b> - The ARN of the stream.</p> </li> <li> <p> <b>Amazon Simple Queue Service</b> - The ARN of the queue.</p> </li> </ul>", "location": "querystring", "locationName": "EventSourceArn"}, "FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>MyFunction</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction</code>.</p> </li> <li> <p> <b>Version or Alias ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction:PROD</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:MyFunction</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it's limited to 64 characters in length.</p>", "location": "querystring", "locationName": "FunctionName"}, "Marker": {"shape": "String", "documentation": "<p>A pagination token returned by a previous call.</p>", "location": "querystring", "locationName": "<PERSON><PERSON>"}, "MaxItems": {"shape": "MaxListItems", "documentation": "<p>The maximum number of event source mappings to return.</p>", "location": "querystring", "locationName": "MaxItems"}}}, "ListEventSourceMappingsResponse": {"type": "structure", "members": {"NextMarker": {"shape": "String", "documentation": "<p>A pagination token that's returned when the response doesn't contain all event source mappings.</p>"}, "EventSourceMappings": {"shape": "EventSourceMappingsList", "documentation": "<p>A list of event source mappings.</p>"}}}, "ListFunctionEventInvokeConfigsRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Marker": {"shape": "String", "documentation": "<p>Specify the pagination token that's returned by a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "<PERSON><PERSON>"}, "MaxItems": {"shape": "MaxFunctionEventInvokeConfigListItems", "documentation": "<p>The maximum number of configurations to return.</p>", "location": "querystring", "locationName": "MaxItems"}}}, "ListFunctionEventInvokeConfigsResponse": {"type": "structure", "members": {"FunctionEventInvokeConfigs": {"shape": "FunctionEventInvokeConfigList", "documentation": "<p>A list of configurations.</p>"}, "NextMarker": {"shape": "String", "documentation": "<p>The pagination token that's included if more results are available.</p>"}}}, "ListFunctionsRequest": {"type": "structure", "members": {"MasterRegion": {"shape": "MasterRegion", "documentation": "<p>For Lambda@Edge functions, the AWS Region of the master function. For example, <code>us-east-1</code> filters the list of functions to only include Lambda@Edge functions replicated from a master function in US East (N. Virginia). If specified, you must set <code>FunctionVersion</code> to <code>ALL</code>.</p>", "location": "querystring", "locationName": "MasterRegion"}, "FunctionVersion": {"shape": "FunctionVersion", "documentation": "<p>Set to <code>ALL</code> to include entries for all published versions of each function.</p>", "location": "querystring", "locationName": "FunctionVersion"}, "Marker": {"shape": "String", "documentation": "<p>Specify the pagination token that's returned by a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "<PERSON><PERSON>"}, "MaxItems": {"shape": "MaxListItems", "documentation": "<p>The maximum number of functions to return.</p>", "location": "querystring", "locationName": "MaxItems"}}}, "ListFunctionsResponse": {"type": "structure", "members": {"NextMarker": {"shape": "String", "documentation": "<p>The pagination token that's included if more results are available.</p>"}, "Functions": {"shape": "FunctionList", "documentation": "<p>A list of Lambda functions.</p>"}}, "documentation": "<p>A list of Lambda functions.</p>"}, "ListLayerVersionsRequest": {"type": "structure", "required": ["Layer<PERSON>ame"], "members": {"CompatibleRuntime": {"shape": "Runtime", "documentation": "<p>A runtime identifier. For example, <code>go1.x</code>.</p>", "location": "querystring", "locationName": "CompatibleRuntime"}, "LayerName": {"shape": "Layer<PERSON>ame", "documentation": "<p>The name or Amazon Resource Name (ARN) of the layer.</p>", "location": "uri", "locationName": "Layer<PERSON>ame"}, "Marker": {"shape": "String", "documentation": "<p>A pagination token returned by a previous call.</p>", "location": "querystring", "locationName": "<PERSON><PERSON>"}, "MaxItems": {"shape": "MaxLayerListItems", "documentation": "<p>The maximum number of versions to return.</p>", "location": "querystring", "locationName": "MaxItems"}}}, "ListLayerVersionsResponse": {"type": "structure", "members": {"NextMarker": {"shape": "String", "documentation": "<p>A pagination token returned when the response doesn't contain all versions.</p>"}, "LayerVersions": {"shape": "LayerVersionsList", "documentation": "<p>A list of versions.</p>"}}}, "ListLayersRequest": {"type": "structure", "members": {"CompatibleRuntime": {"shape": "Runtime", "documentation": "<p>A runtime identifier. For example, <code>go1.x</code>.</p>", "location": "querystring", "locationName": "CompatibleRuntime"}, "Marker": {"shape": "String", "documentation": "<p>A pagination token returned by a previous call.</p>", "location": "querystring", "locationName": "<PERSON><PERSON>"}, "MaxItems": {"shape": "MaxLayerListItems", "documentation": "<p>The maximum number of layers to return.</p>", "location": "querystring", "locationName": "MaxItems"}}}, "ListLayersResponse": {"type": "structure", "members": {"NextMarker": {"shape": "String", "documentation": "<p>A pagination token returned when the response doesn't contain all layers.</p>"}, "Layers": {"shape": "LayersList", "documentation": "<p>A list of function layers.</p>"}}}, "ListProvisionedConcurrencyConfigsRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Marker": {"shape": "String", "documentation": "<p>Specify the pagination token that's returned by a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "<PERSON><PERSON>"}, "MaxItems": {"shape": "MaxProvisionedConcurrencyConfigListItems", "documentation": "<p>Specify a number to limit the number of configurations returned.</p>", "location": "querystring", "locationName": "MaxItems"}}}, "ListProvisionedConcurrencyConfigsResponse": {"type": "structure", "members": {"ProvisionedConcurrencyConfigs": {"shape": "ProvisionedConcurrencyConfigList", "documentation": "<p>A list of provisioned concurrency configurations.</p>"}, "NextMarker": {"shape": "String", "documentation": "<p>The pagination token that's included if more results are available.</p>"}}}, "ListTagsRequest": {"type": "structure", "required": ["Resource"], "members": {"Resource": {"shape": "FunctionArn", "documentation": "<p>The function's Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "ARN"}}}, "ListTagsResponse": {"type": "structure", "members": {"Tags": {"shape": "Tags", "documentation": "<p>The function's tags.</p>"}}}, "ListVersionsByFunctionRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "NamespacedFunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>MyFunction</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:MyFunction</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Marker": {"shape": "String", "documentation": "<p>Specify the pagination token that's returned by a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "<PERSON><PERSON>"}, "MaxItems": {"shape": "MaxListItems", "documentation": "<p>The maximum number of versions to return.</p>", "location": "querystring", "locationName": "MaxItems"}}}, "ListVersionsByFunctionResponse": {"type": "structure", "members": {"NextMarker": {"shape": "String", "documentation": "<p>The pagination token that's included if more results are available.</p>"}, "Versions": {"shape": "FunctionList", "documentation": "<p>A list of Lambda function versions.</p>"}}}, "LocalMountPath": {"type": "string", "max": 160, "pattern": "^/mnt/[a-zA-Z0-9-_.]+$"}, "LogType": {"type": "string", "enum": ["None", "Tail"]}, "Long": {"type": "long"}, "MasterRegion": {"type": "string", "pattern": "ALL|[a-z]{2}(-gov)?-[a-z]+-\\d{1}"}, "MaxFunctionEventInvokeConfigListItems": {"type": "integer", "max": 50, "min": 1}, "MaxLayerListItems": {"type": "integer", "max": 50, "min": 1}, "MaxListItems": {"type": "integer", "max": 10000, "min": 1}, "MaxProvisionedConcurrencyConfigListItems": {"type": "integer", "max": 50, "min": 1}, "MaximumBatchingWindowInSeconds": {"type": "integer", "max": 300, "min": 0}, "MaximumEventAgeInSeconds": {"type": "integer", "max": 21600, "min": 60}, "MaximumRecordAgeInSeconds": {"type": "integer", "max": 604800, "min": 60}, "MaximumRetryAttempts": {"type": "integer", "max": 2, "min": 0}, "MaximumRetryAttemptsEventSourceMapping": {"type": "integer", "max": 10000, "min": 0}, "MemorySize": {"type": "integer", "max": 3008, "min": 128}, "NameSpacedFunctionArn": {"type": "string", "pattern": "arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?"}, "NamespacedFunctionName": {"type": "string", "max": 170, "min": 1, "pattern": "(arn:(aws[a-zA-Z-]*)?:lambda:)?([a-z]{2}(-gov)?-[a-z]+-\\d{1}:)?(\\d{12}:)?(function:)?([a-zA-Z0-9-_\\.]+)(:(\\$LATEST|[a-zA-Z0-9-_]+))?"}, "NamespacedStatementId": {"type": "string", "max": 100, "min": 1, "pattern": "([a-zA-Z0-9-_.]+)"}, "NonNegativeInteger": {"type": "integer", "min": 0}, "OnFailure": {"type": "structure", "members": {"Destination": {"shape": "DestinationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the destination resource.</p>"}}, "documentation": "<p>A destination for events that failed processing.</p>"}, "OnSuccess": {"type": "structure", "members": {"Destination": {"shape": "DestinationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the destination resource.</p>"}}, "documentation": "<p>A destination for events that were processed successfully.</p>"}, "OrganizationId": {"type": "string", "pattern": "o-[a-z0-9]{10,32}"}, "ParallelizationFactor": {"type": "integer", "max": 10, "min": 1}, "PolicyLengthExceededException": {"type": "structure", "members": {"Type": {"shape": "String"}, "message": {"shape": "String"}}, "documentation": "<p>The permissions policy for the resource is too large. <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/limits.html\">Learn more</a> </p>", "error": {"httpStatusCode": 400}, "exception": true}, "PositiveInteger": {"type": "integer", "min": 1}, "PreconditionFailedException": {"type": "structure", "members": {"Type": {"shape": "String", "documentation": "<p>The exception type.</p>"}, "message": {"shape": "String", "documentation": "<p>The exception message.</p>"}}, "documentation": "<p>The RevisionId provided does not match the latest RevisionId for the Lambda function or alias. Call the <code>GetFunction</code> or the <code>GetAlias</code> API to retrieve the latest RevisionId for your resource.</p>", "error": {"httpStatusCode": 412}, "exception": true}, "Principal": {"type": "string", "pattern": ".*"}, "ProvisionedConcurrencyConfigList": {"type": "list", "member": {"shape": "ProvisionedConcurrencyConfigListItem"}}, "ProvisionedConcurrencyConfigListItem": {"type": "structure", "members": {"FunctionArn": {"shape": "FunctionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the alias or version.</p>"}, "RequestedProvisionedConcurrentExecutions": {"shape": "PositiveInteger", "documentation": "<p>The amount of provisioned concurrency requested.</p>"}, "AvailableProvisionedConcurrentExecutions": {"shape": "NonNegativeInteger", "documentation": "<p>The amount of provisioned concurrency available.</p>"}, "AllocatedProvisionedConcurrentExecutions": {"shape": "NonNegativeInteger", "documentation": "<p>The amount of provisioned concurrency allocated.</p>"}, "Status": {"shape": "ProvisionedConcurrencyStatusEnum", "documentation": "<p>The status of the allocation process.</p>"}, "StatusReason": {"shape": "String", "documentation": "<p>For failed allocations, the reason that provisioned concurrency could not be allocated.</p>"}, "LastModified": {"shape": "Timestamp", "documentation": "<p>The date and time that a user last updated the configuration, in <a href=\"https://www.iso.org/iso-8601-date-and-time-format.html\">ISO 8601 format</a>.</p>"}}, "documentation": "<p>Details about the provisioned concurrency configuration for a function alias or version.</p>"}, "ProvisionedConcurrencyConfigNotFoundException": {"type": "structure", "members": {"Type": {"shape": "String"}, "message": {"shape": "String"}}, "documentation": "<p>The specified configuration does not exist.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ProvisionedConcurrencyStatusEnum": {"type": "string", "enum": ["IN_PROGRESS", "READY", "FAILED"]}, "PublishLayerVersionRequest": {"type": "structure", "required": ["Layer<PERSON>ame", "Content"], "members": {"LayerName": {"shape": "Layer<PERSON>ame", "documentation": "<p>The name or Amazon Resource Name (ARN) of the layer.</p>", "location": "uri", "locationName": "Layer<PERSON>ame"}, "Description": {"shape": "Description", "documentation": "<p>The description of the version.</p>"}, "Content": {"shape": "LayerVersionContentInput", "documentation": "<p>The function layer archive.</p>"}, "CompatibleRuntimes": {"shape": "CompatibleRuntimes", "documentation": "<p>A list of compatible <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/lambda-runtimes.html\">function runtimes</a>. Used for filtering with <a>ListLayers</a> and <a>ListLayerVersions</a>.</p>"}, "LicenseInfo": {"shape": "LicenseInfo", "documentation": "<p>The layer's software license. It can be any of the following:</p> <ul> <li> <p>An <a href=\"https://spdx.org/licenses/\">SPDX license identifier</a>. For example, <code>MIT</code>.</p> </li> <li> <p>The URL of a license hosted on the internet. For example, <code>https://opensource.org/licenses/MIT</code>.</p> </li> <li> <p>The full text of the license.</p> </li> </ul>"}}}, "PublishLayerVersionResponse": {"type": "structure", "members": {"Content": {"shape": "LayerVersionContentOutput", "documentation": "<p>Details about the layer version.</p>"}, "LayerArn": {"shape": "LayerArn", "documentation": "<p>The ARN of the layer.</p>"}, "LayerVersionArn": {"shape": "LayerVersionArn", "documentation": "<p>The ARN of the layer version.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the version.</p>"}, "CreatedDate": {"shape": "Timestamp", "documentation": "<p>The date that the layer version was created, in <a href=\"https://www.w3.org/TR/NOTE-datetime\">ISO-8601 format</a> (YYYY-MM-DDThh:mm:ss.sTZD).</p>"}, "Version": {"shape": "LayerVersionNumber", "documentation": "<p>The version number.</p>"}, "CompatibleRuntimes": {"shape": "CompatibleRuntimes", "documentation": "<p>The layer's compatible runtimes.</p>"}, "LicenseInfo": {"shape": "LicenseInfo", "documentation": "<p>The layer's software license.</p>"}}}, "PublishVersionRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>MyFunction</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:MyFunction</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "CodeSha256": {"shape": "String", "documentation": "<p>Only publish a version if the hash value matches the value that's specified. Use this option to avoid publishing a version if the function code has changed since you last updated it. You can get the hash for the version that you uploaded from the output of <a>UpdateFunctionCode</a>.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description for the version to override the description in the function configuration.</p>"}, "RevisionId": {"shape": "String", "documentation": "<p>Only update the function if the revision ID matches the ID that's specified. Use this option to avoid publishing a version if the function configuration has changed since you last updated it.</p>"}}}, "PutFunctionConcurrencyRequest": {"type": "structure", "required": ["FunctionName", "ReservedConcurrentExecutions"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "ReservedConcurrentExecutions": {"shape": "ReservedConcurrentExecutions", "documentation": "<p>The number of simultaneous executions to reserve for the function.</p>"}}}, "PutFunctionEventInvokeConfigRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function, version, or alias.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code> (name-only), <code>my-function:v1</code> (with alias).</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>You can append a version number or alias to any of the formats. The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>A version number or alias name.</p>", "location": "querystring", "locationName": "Qualifier"}, "MaximumRetryAttempts": {"shape": "MaximumRetryAttempts", "documentation": "<p>The maximum number of times to retry when the function returns an error.</p>"}, "MaximumEventAgeInSeconds": {"shape": "MaximumEventAgeInSeconds", "documentation": "<p>The maximum age of a request that Lambda sends to a function for processing.</p>"}, "DestinationConfig": {"shape": "DestinationConfig", "documentation": "<p>A destination for events after they have been sent to a function for processing.</p> <p class=\"title\"> <b>Destinations</b> </p> <ul> <li> <p> <b>Function</b> - The Amazon Resource Name (ARN) of a Lambda function.</p> </li> <li> <p> <b>Queue</b> - The ARN of an SQS queue.</p> </li> <li> <p> <b>Topic</b> - The ARN of an SNS topic.</p> </li> <li> <p> <b>Event Bus</b> - The ARN of an Amazon EventBridge event bus.</p> </li> </ul>"}}}, "PutProvisionedConcurrencyConfigRequest": {"type": "structure", "required": ["FunctionName", "Qualifier", "ProvisionedConcurrentExecutions"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>The version number or alias name.</p>", "location": "querystring", "locationName": "Qualifier"}, "ProvisionedConcurrentExecutions": {"shape": "PositiveInteger", "documentation": "<p>The amount of provisioned concurrency to allocate for the version or alias.</p>"}}}, "PutProvisionedConcurrencyConfigResponse": {"type": "structure", "members": {"RequestedProvisionedConcurrentExecutions": {"shape": "PositiveInteger", "documentation": "<p>The amount of provisioned concurrency requested.</p>"}, "AvailableProvisionedConcurrentExecutions": {"shape": "NonNegativeInteger", "documentation": "<p>The amount of provisioned concurrency available.</p>"}, "AllocatedProvisionedConcurrentExecutions": {"shape": "NonNegativeInteger", "documentation": "<p>The amount of provisioned concurrency allocated.</p>"}, "Status": {"shape": "ProvisionedConcurrencyStatusEnum", "documentation": "<p>The status of the allocation process.</p>"}, "StatusReason": {"shape": "String", "documentation": "<p>For failed allocations, the reason that provisioned concurrency could not be allocated.</p>"}, "LastModified": {"shape": "Timestamp", "documentation": "<p>The date and time that a user last updated the configuration, in <a href=\"https://www.iso.org/iso-8601-date-and-time-format.html\">ISO 8601 format</a>.</p>"}}}, "Qualifier": {"type": "string", "max": 128, "min": 1, "pattern": "(|[a-zA-Z0-9$_-]+)"}, "RemoveLayerVersionPermissionRequest": {"type": "structure", "required": ["Layer<PERSON>ame", "VersionNumber", "StatementId"], "members": {"LayerName": {"shape": "Layer<PERSON>ame", "documentation": "<p>The name or Amazon Resource Name (ARN) of the layer.</p>", "location": "uri", "locationName": "Layer<PERSON>ame"}, "VersionNumber": {"shape": "LayerVersionNumber", "documentation": "<p>The version number.</p>", "location": "uri", "locationName": "VersionNumber"}, "StatementId": {"shape": "StatementId", "documentation": "<p>The identifier that was specified when the statement was added.</p>", "location": "uri", "locationName": "StatementId"}, "RevisionId": {"shape": "String", "documentation": "<p>Only update the policy if the revision ID matches the ID specified. Use this option to avoid modifying a policy that has changed since you last read it.</p>", "location": "querystring", "locationName": "RevisionId"}}}, "RemovePermissionRequest": {"type": "structure", "required": ["FunctionName", "StatementId"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function, version, or alias.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code> (name-only), <code>my-function:v1</code> (with alias).</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>You can append a version number or alias to any of the formats. The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "StatementId": {"shape": "NamespacedStatementId", "documentation": "<p>Statement ID of the permission to remove.</p>", "location": "uri", "locationName": "StatementId"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>Specify a version or alias to remove permissions from a published version of the function.</p>", "location": "querystring", "locationName": "Qualifier"}, "RevisionId": {"shape": "String", "documentation": "<p>Only update the policy if the revision ID matches the ID that's specified. Use this option to avoid modifying a policy that has changed since you last read it.</p>", "location": "querystring", "locationName": "RevisionId"}}}, "RequestTooLargeException": {"type": "structure", "members": {"Type": {"shape": "String"}, "message": {"shape": "String"}}, "documentation": "<p>The request payload exceeded the <code>Invoke</code> request body JSON input limit. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/limits.html\">Limits</a>. </p>", "error": {"httpStatusCode": 413}, "exception": true}, "ReservedConcurrentExecutions": {"type": "integer", "min": 0}, "ResourceArn": {"type": "string", "pattern": "(arn:(aws[a-zA-Z-]*)?:[a-z0-9-.]+:.*)|()"}, "ResourceConflictException": {"type": "structure", "members": {"Type": {"shape": "String", "documentation": "<p>The exception type.</p>"}, "message": {"shape": "String", "documentation": "<p>The exception message.</p>"}}, "documentation": "<p>The resource already exists, or another operation is in progress.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ResourceInUseException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>The operation conflicts with the resource's availability. For example, you attempted to update an EventSource Mapping in CREATING, or tried to delete a EventSource mapping currently in the UPDATING state.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>The resource specified in the request does not exist.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResourceNotReadyException": {"type": "structure", "members": {"Type": {"shape": "String", "documentation": "<p>The exception type.</p>"}, "message": {"shape": "String", "documentation": "<p>The exception message.</p>"}}, "documentation": "<p>The function is inactive and its VPC connection is no longer available. Wait for the VPC connection to reestablish and try again.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "RoleArn": {"type": "string", "pattern": "arn:(aws[a-zA-Z-]*)?:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+"}, "Runtime": {"type": "string", "enum": ["nodejs", "nodejs4.3", "nodejs6.10", "nodejs8.10", "nodejs10.x", "nodejs12.x", "java8", "java11", "python2.7", "python3.6", "python3.7", "python3.8", "dotnetcore1.0", "dotnetcore2.0", "dotnetcore2.1", "dotnetcore3.1", "nodejs4.3-edge", "go1.x", "ruby2.5", "ruby2.7", "provided"]}, "S3Bucket": {"type": "string", "max": 63, "min": 3, "pattern": "^[0-9A-Za-z\\.\\-_]*(?<!\\.)$"}, "S3Key": {"type": "string", "max": 1024, "min": 1}, "S3ObjectVersion": {"type": "string", "max": 1024, "min": 1}, "SecurityGroupId": {"type": "string"}, "SecurityGroupIds": {"type": "list", "member": {"shape": "SecurityGroupId"}, "max": 5}, "SensitiveString": {"type": "string", "sensitive": true}, "ServiceException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>The AWS Lambda service encountered an internal error.</p>", "error": {"httpStatusCode": 500}, "exception": true}, "SourceOwner": {"type": "string", "pattern": "\\d{12}"}, "State": {"type": "string", "enum": ["Pending", "Active", "Inactive", "Failed"]}, "StateReason": {"type": "string"}, "StateReasonCode": {"type": "string", "enum": ["Idle", "Creating", "Restoring", "EniLimitExceeded", "InsufficientRolePermissions", "InvalidConfiguration", "InternalError", "SubnetOutOfIPAddresses", "InvalidSubnet", "InvalidSecurityGroup"]}, "StatementId": {"type": "string", "max": 100, "min": 1, "pattern": "([a-zA-Z0-9-_]+)"}, "String": {"type": "string"}, "SubnetIPAddressLimitReachedException": {"type": "structure", "members": {"Type": {"shape": "String"}, "Message": {"shape": "String"}}, "documentation": "<p>AWS Lambda was not able to set up VPC access for the Lambda function because one or more configured subnets has no available IP addresses.</p>", "error": {"httpStatusCode": 502}, "exception": true}, "SubnetId": {"type": "string"}, "SubnetIds": {"type": "list", "member": {"shape": "SubnetId"}, "max": 16}, "TagKey": {"type": "string"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}}, "TagResourceRequest": {"type": "structure", "required": ["Resource", "Tags"], "members": {"Resource": {"shape": "FunctionArn", "documentation": "<p>The function's Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "ARN"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of tags to apply to the function.</p>"}}}, "TagValue": {"type": "string"}, "Tags": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}}, "ThrottleReason": {"type": "string", "enum": ["ConcurrentInvocationLimitExceeded", "FunctionInvocationRateLimitExceeded", "ReservedFunctionConcurrentInvocationLimitExceeded", "ReservedFunctionInvocationRateLimitExceeded", "CallerRateLimitExceeded"]}, "Timeout": {"type": "integer", "min": 1}, "Timestamp": {"type": "string"}, "TooManyRequestsException": {"type": "structure", "members": {"retryAfterSeconds": {"shape": "String", "documentation": "<p>The number of seconds the caller should wait before retrying.</p>", "location": "header", "locationName": "Retry-After"}, "Type": {"shape": "String"}, "message": {"shape": "String"}, "Reason": {"shape": "ThrottleReason"}}, "documentation": "<p>The request throughput limit was exceeded.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "TracingConfig": {"type": "structure", "members": {"Mode": {"shape": "TracingMode", "documentation": "<p>The tracing mode.</p>"}}, "documentation": "<p>The function's AWS X-Ray tracing configuration. To sample and record incoming requests, set <code>Mode</code> to <code>Active</code>.</p>"}, "TracingConfigResponse": {"type": "structure", "members": {"Mode": {"shape": "TracingMode", "documentation": "<p>The tracing mode.</p>"}}, "documentation": "<p>The function's AWS X-Ray tracing configuration.</p>"}, "TracingMode": {"type": "string", "enum": ["Active", "PassThrough"]}, "UnreservedConcurrentExecutions": {"type": "integer", "min": 0}, "UnsupportedMediaTypeException": {"type": "structure", "members": {"Type": {"shape": "String"}, "message": {"shape": "String"}}, "documentation": "<p>The content type of the <code>Invoke</code> request body is not JSON.</p>", "error": {"httpStatusCode": 415}, "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["Resource", "TagKeys"], "members": {"Resource": {"shape": "FunctionArn", "documentation": "<p>The function's Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "ARN"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>A list of tag keys to remove from the function.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UpdateAliasRequest": {"type": "structure", "required": ["FunctionName", "Name"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>MyFunction</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:MyFunction</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Name": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name of the alias.</p>", "location": "uri", "locationName": "Name"}, "FunctionVersion": {"shape": "Version", "documentation": "<p>The function version that the alias invokes.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the alias.</p>"}, "RoutingConfig": {"shape": "AliasRoutingConfiguration", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-aliases.html#configuring-alias-routing\">routing configuration</a> of the alias.</p>"}, "RevisionId": {"shape": "String", "documentation": "<p>Only update the alias if the revision ID matches the ID that's specified. Use this option to avoid modifying an alias that has changed since you last read it.</p>"}}}, "UpdateEventSourceMappingRequest": {"type": "structure", "required": ["UUID"], "members": {"UUID": {"shape": "String", "documentation": "<p>The identifier of the event source mapping.</p>", "location": "uri", "locationName": "UUID"}, "FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>MyFunction</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction</code>.</p> </li> <li> <p> <b>Version or Alias ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:MyFunction:PROD</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:MyFunction</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it's limited to 64 characters in length.</p>"}, "Enabled": {"shape": "Enabled", "documentation": "<p>Disables the event source mapping to pause polling and invocation.</p>"}, "BatchSize": {"shape": "BatchSize", "documentation": "<p>The maximum number of items to retrieve in a single batch.</p> <ul> <li> <p> <b>Amazon Kinesis</b> - Default 100. Max 10,000.</p> </li> <li> <p> <b>Amazon DynamoDB Streams</b> - Default 100. Max 1,000.</p> </li> <li> <p> <b>Amazon Simple Queue Service</b> - Default 10. Max 10.</p> </li> </ul>"}, "MaximumBatchingWindowInSeconds": {"shape": "MaximumBatchingWindowInSeconds", "documentation": "<p>(Streams) The maximum amount of time to gather records before invoking the function, in seconds.</p>"}, "DestinationConfig": {"shape": "DestinationConfig", "documentation": "<p>(Streams) An Amazon SQS queue or Amazon SNS topic destination for discarded records.</p>"}, "MaximumRecordAgeInSeconds": {"shape": "MaximumRecordAgeInSeconds", "documentation": "<p>(Streams) The maximum age of a record that Lambda sends to a function for processing.</p>"}, "BisectBatchOnFunctionError": {"shape": "BisectBatchOnFunctionError", "documentation": "<p>(Streams) If the function returns an error, split the batch in two and retry.</p>"}, "MaximumRetryAttempts": {"shape": "MaximumRetryAttemptsEventSourceMapping", "documentation": "<p>(Streams) The maximum number of times to retry when the function returns an error.</p>"}, "ParallelizationFactor": {"shape": "ParallelizationFactor", "documentation": "<p>(Streams) The number of batches to process from each shard concurrently.</p>"}}}, "UpdateFunctionCodeRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "ZipFile": {"shape": "Blob", "documentation": "<p>The base64-encoded contents of the deployment package. AWS SDK and AWS CLI clients handle the encoding for you.</p>"}, "S3Bucket": {"shape": "S3Bucket", "documentation": "<p>An Amazon S3 bucket in the same AWS Region as your function. The bucket can be in a different AWS account.</p>"}, "S3Key": {"shape": "S3Key", "documentation": "<p>The Amazon S3 key of the deployment package.</p>"}, "S3ObjectVersion": {"shape": "S3ObjectVersion", "documentation": "<p>For versioned objects, the version of the deployment package object to use.</p>"}, "Publish": {"shape": "Boolean", "documentation": "<p>Set to true to publish a new version of the function after updating the code. This has the same effect as calling <a>PublishVersion</a> separately.</p>"}, "DryRun": {"shape": "Boolean", "documentation": "<p>Set to true to validate the request parameters and access permissions without modifying the function code.</p>"}, "RevisionId": {"shape": "String", "documentation": "<p>Only update the function if the revision ID matches the ID that's specified. Use this option to avoid modifying a function that has changed since you last read it.</p>"}}}, "UpdateFunctionConfigurationRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code>.</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Role": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the function's execution role.</p>"}, "Handler": {"shape": "Handler", "documentation": "<p>The name of the method within your code that Lambda calls to execute your function. The format includes the file name. It can also include namespaces and other qualifiers, depending on the runtime. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/programming-model-v2.html\">Programming Model</a>.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the function.</p>"}, "Timeout": {"shape": "Timeout", "documentation": "<p>The amount of time that Lambda allows a function to run before stopping it. The default is 3 seconds. The maximum allowed value is 900 seconds.</p>"}, "MemorySize": {"shape": "MemorySize", "documentation": "<p>The amount of memory that your function has access to. Increasing the function's memory also increases its CPU allocation. The default value is 128 MB. The value must be a multiple of 64 MB.</p>"}, "VpcConfig": {"shape": "VpcConfig", "documentation": "<p>For network connectivity to AWS resources in a VPC, specify a list of security groups and subnets in the VPC. When you connect a function to a VPC, it can only access resources and the internet through that VPC. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-vpc.html\">VPC Settings</a>.</p>"}, "Environment": {"shape": "Environment", "documentation": "<p>Environment variables that are accessible from function code during execution.</p>"}, "Runtime": {"shape": "Runtime", "documentation": "<p>The identifier of the function's <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/lambda-runtimes.html\">runtime</a>.</p>"}, "DeadLetterConfig": {"shape": "DeadLetterConfig", "documentation": "<p>A dead letter queue configuration that specifies the queue or topic where Lambda sends asynchronous events when they fail processing. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/invocation-async.html#dlq\">Dead Letter Queues</a>.</p>"}, "KMSKeyArn": {"shape": "KMSKeyArn", "documentation": "<p>The ARN of the AWS Key Management Service (AWS KMS) key that's used to encrypt your function's environment variables. If it's not provided, AWS Lambda uses a default service key.</p>"}, "TracingConfig": {"shape": "TracingConfig", "documentation": "<p>Set <code>Mode</code> to <code>Active</code> to sample and trace a subset of incoming requests with AWS X-Ray.</p>"}, "RevisionId": {"shape": "String", "documentation": "<p>Only update the function if the revision ID matches the ID that's specified. Use this option to avoid modifying a function that has changed since you last read it.</p>"}, "Layers": {"shape": "LayerList", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\">function layers</a> to add to the function's execution environment. Specify each layer by its ARN, including the version.</p>"}, "FileSystemConfigs": {"shape": "FileSystemConfigList", "documentation": "<p>Connection settings for an Amazon EFS file system.</p>"}}}, "UpdateFunctionEventInvokeConfigRequest": {"type": "structure", "required": ["FunctionName"], "members": {"FunctionName": {"shape": "FunctionName", "documentation": "<p>The name of the Lambda function, version, or alias.</p> <p class=\"title\"> <b>Name formats</b> </p> <ul> <li> <p> <b>Function name</b> - <code>my-function</code> (name-only), <code>my-function:v1</code> (with alias).</p> </li> <li> <p> <b>Function ARN</b> - <code>arn:aws:lambda:us-west-2:************:function:my-function</code>.</p> </li> <li> <p> <b>Partial ARN</b> - <code>************:function:my-function</code>.</p> </li> </ul> <p>You can append a version number or alias to any of the formats. The length constraint applies only to the full ARN. If you specify only the function name, it is limited to 64 characters in length.</p>", "location": "uri", "locationName": "FunctionName"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>A version number or alias name.</p>", "location": "querystring", "locationName": "Qualifier"}, "MaximumRetryAttempts": {"shape": "MaximumRetryAttempts", "documentation": "<p>The maximum number of times to retry when the function returns an error.</p>"}, "MaximumEventAgeInSeconds": {"shape": "MaximumEventAgeInSeconds", "documentation": "<p>The maximum age of a request that Lambda sends to a function for processing.</p>"}, "DestinationConfig": {"shape": "DestinationConfig", "documentation": "<p>A destination for events after they have been sent to a function for processing.</p> <p class=\"title\"> <b>Destinations</b> </p> <ul> <li> <p> <b>Function</b> - The Amazon Resource Name (ARN) of a Lambda function.</p> </li> <li> <p> <b>Queue</b> - The ARN of an SQS queue.</p> </li> <li> <p> <b>Topic</b> - The ARN of an SNS topic.</p> </li> <li> <p> <b>Event Bus</b> - The ARN of an Amazon EventBridge event bus.</p> </li> </ul>"}}}, "Version": {"type": "string", "max": 1024, "min": 1, "pattern": "(\\$LATEST|[0-9]+)"}, "VpcConfig": {"type": "structure", "members": {"SubnetIds": {"shape": "SubnetIds", "documentation": "<p>A list of VPC subnet IDs.</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupIds", "documentation": "<p>A list of VPC security groups IDs.</p>"}}, "documentation": "<p>The VPC security groups and subnets that are attached to a Lambda function. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-vpc.html\">VPC Settings</a>.</p>"}, "VpcConfigResponse": {"type": "structure", "members": {"SubnetIds": {"shape": "SubnetIds", "documentation": "<p>A list of VPC subnet IDs.</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupIds", "documentation": "<p>A list of VPC security groups IDs.</p>"}, "VpcId": {"shape": "VpcId", "documentation": "<p>The ID of the VPC.</p>"}}, "documentation": "<p>The VPC security groups and subnets that are attached to a Lambda function.</p>"}, "VpcId": {"type": "string"}, "Weight": {"type": "double", "max": 1.0, "min": 0.0}}, "documentation": "<fullname>AWS Lambda</fullname> <p> <b>Overview</b> </p> <p>This is the <i>AWS Lambda API Reference</i>. The AWS Lambda Developer Guide provides additional information. For the service overview, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/welcome.html\">What is AWS Lambda</a>, and for information about how the service works, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/lambda-introduction.html\">AWS Lambda: How it Works</a> in the <b>AWS Lambda Developer Guide</b>.</p>"}