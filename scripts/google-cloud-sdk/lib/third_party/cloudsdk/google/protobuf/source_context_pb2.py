# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/source_context.proto
"""Generated protocol buffer code."""
from cloudsdk.google.protobuf import descriptor as _descriptor
from cloudsdk.google.protobuf import descriptor_pool as _descriptor_pool
from cloudsdk.google.protobuf import message as _message
from cloudsdk.google.protobuf import reflection as _reflection
from cloudsdk.google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n$google/protobuf/source_context.proto\x12\x0fgoogle.protobuf\"\"\n\rSourceContext\x12\x11\n\tfile_name\x18\x01 \x01(\tB\x8a\x01\n\x13\x63om.google.protobufB\x12SourceContextProtoP\x01Z6google.golang.org/protobuf/types/known/sourcecontextpb\xa2\x02\x03GPB\xaa\x02\x1eGoogle.Protobuf.WellKnownTypesb\x06proto3')



_SOURCECONTEXT = DESCRIPTOR.message_types_by_name['SourceContext']
SourceContext = _reflection.GeneratedProtocolMessageType('SourceContext', (_message.Message,), {
  'DESCRIPTOR' : _SOURCECONTEXT,
  '__module__' : 'google.protobuf.source_context_pb2'
  # @@protoc_insertion_point(class_scope:google.protobuf.SourceContext)
  })
_sym_db.RegisterMessage(SourceContext)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\023com.google.protobufB\022SourceContextProtoP\001Z6google.golang.org/protobuf/types/known/sourcecontextpb\242\002\003GPB\252\002\036Google.Protobuf.WellKnownTypes'
  _SOURCECONTEXT._serialized_start=57
  _SOURCECONTEXT._serialized_end=91
# @@protoc_insertion_point(module_scope)
