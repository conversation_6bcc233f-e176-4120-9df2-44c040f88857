# coding: utf-8
"""
    Kubernetes

    No description provided (generated by Swagger Codegen
    https://github.com/swagger-api/swagger-codegen)

    OpenAPI spec version: v1.14.4

    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""

from __future__ import absolute_import

import sys
import os
import re

# python 2 and python 3 compatibility library
from six import iteritems

from ..api_client import ApiClient


class ExtensionsApi(object):
  """
    NOTE: This class is auto generated by the swagger code generator program.
    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

  def __init__(self, api_client=None):
    if api_client is None:
      api_client = ApiClient()
    self.api_client = api_client

  def get_api_group(self, **kwargs):
    """
        get information of a group
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_api_group(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :return: V1APIGroup
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.get_api_group_with_http_info(**kwargs)
    else:
      (data) = self.get_api_group_with_http_info(**kwargs)
      return data

  def get_api_group_with_http_info(self, **kwargs):
    """
        get information of a group
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_api_group_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :return: V1APIGroup
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = []
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method get_api_group' % key)
      params[key] = val
    del params['kwargs']

    collection_formats = {}

    path_params = {}

    query_params = []

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/extensions/',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1APIGroup',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)
