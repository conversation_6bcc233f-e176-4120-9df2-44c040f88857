# coding: utf-8
"""
    Kubernetes

    No description provided (generated by Swagger Codegen
    https://github.com/swagger-api/swagger-codegen)

    OpenAPI spec version: v1.14.4

    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""

from __future__ import absolute_import

import sys
import os
import re

# python 2 and python 3 compatibility library
from six import iteritems

from ..api_client import ApiClient


class AppsV1beta2Api(object):
  """
    NOTE: This class is auto generated by the swagger code generator program.
    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

  def __init__(self, api_client=None):
    if api_client is None:
      api_client = ApiClient()
    self.api_client = api_client

  def create_namespaced_controller_revision(self, namespace, body, **kwargs):
    """
        create a ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_namespaced_controller_revision(namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2ControllerRevision body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2ControllerRevision
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.create_namespaced_controller_revision_with_http_info(
          namespace, body, **kwargs)
    else:
      (data) = self.create_namespaced_controller_revision_with_http_info(
          namespace, body, **kwargs)
      return data

  def create_namespaced_controller_revision_with_http_info(
      self, namespace, body, **kwargs):
    """
        create a ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.create_namespaced_controller_revision_with_http_info(namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2ControllerRevision body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2ControllerRevision
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['namespace', 'body', 'pretty', 'dry_run', 'field_manager']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method create_namespaced_controller_revision' %
                        key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `create_namespaced_controller_revision`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `create_namespaced_controller_revision`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/controllerrevisions',
        'POST',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ControllerRevision',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def create_namespaced_daemon_set(self, namespace, body, **kwargs):
    """
        create a DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_namespaced_daemon_set(namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2DaemonSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.create_namespaced_daemon_set_with_http_info(
          namespace, body, **kwargs)
    else:
      (data) = self.create_namespaced_daemon_set_with_http_info(
          namespace, body, **kwargs)
      return data

  def create_namespaced_daemon_set_with_http_info(self, namespace, body,
                                                  **kwargs):
    """
        create a DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_namespaced_daemon_set_with_http_info(namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2DaemonSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['namespace', 'body', 'pretty', 'dry_run', 'field_manager']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method create_namespaced_daemon_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `create_namespaced_daemon_set`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `create_namespaced_daemon_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/daemonsets',
        'POST',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2DaemonSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def create_namespaced_deployment(self, namespace, body, **kwargs):
    """
        create a Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_namespaced_deployment(namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2Deployment body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.create_namespaced_deployment_with_http_info(
          namespace, body, **kwargs)
    else:
      (data) = self.create_namespaced_deployment_with_http_info(
          namespace, body, **kwargs)
      return data

  def create_namespaced_deployment_with_http_info(self, namespace, body,
                                                  **kwargs):
    """
        create a Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_namespaced_deployment_with_http_info(namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2Deployment body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['namespace', 'body', 'pretty', 'dry_run', 'field_manager']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method create_namespaced_deployment' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `create_namespaced_deployment`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `create_namespaced_deployment`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments',
        'POST',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Deployment',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def create_namespaced_replica_set(self, namespace, body, **kwargs):
    """
        create a ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_namespaced_replica_set(namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2ReplicaSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.create_namespaced_replica_set_with_http_info(
          namespace, body, **kwargs)
    else:
      (data) = self.create_namespaced_replica_set_with_http_info(
          namespace, body, **kwargs)
      return data

  def create_namespaced_replica_set_with_http_info(self, namespace, body,
                                                   **kwargs):
    """
        create a ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_namespaced_replica_set_with_http_info(namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2ReplicaSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['namespace', 'body', 'pretty', 'dry_run', 'field_manager']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method create_namespaced_replica_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `create_namespaced_replica_set`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `create_namespaced_replica_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets',
        'POST',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ReplicaSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def create_namespaced_stateful_set(self, namespace, body, **kwargs):
    """
        create a StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_namespaced_stateful_set(namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2StatefulSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.create_namespaced_stateful_set_with_http_info(
          namespace, body, **kwargs)
    else:
      (data) = self.create_namespaced_stateful_set_with_http_info(
          namespace, body, **kwargs)
      return data

  def create_namespaced_stateful_set_with_http_info(self, namespace, body,
                                                    **kwargs):
    """
        create a StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.create_namespaced_stateful_set_with_http_info(namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2StatefulSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['namespace', 'body', 'pretty', 'dry_run', 'field_manager']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method create_namespaced_stateful_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `create_namespaced_stateful_set`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `create_namespaced_stateful_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets',
        'POST',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2StatefulSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def delete_collection_namespaced_controller_revision(self, namespace,
                                                       **kwargs):
    """
        delete collection of ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.delete_collection_namespaced_controller_revision(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.delete_collection_namespaced_controller_revision_with_http_info(
          namespace, **kwargs)
    else:
      (data
      ) = self.delete_collection_namespaced_controller_revision_with_http_info(
          namespace, **kwargs)
      return data

  def delete_collection_namespaced_controller_revision_with_http_info(
      self, namespace, **kwargs):
    """
        delete collection of ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.delete_collection_namespaced_controller_revision_with_http_info(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'namespace', 'pretty', '_continue', 'field_selector', 'label_selector',
        'limit', 'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError(
            "Got an unexpected keyword argument '%s'"
            ' to method delete_collection_namespaced_controller_revision' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `delete_collection_namespaced_controller_revision`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/controllerrevisions',
        'DELETE',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1Status',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def delete_collection_namespaced_daemon_set(self, namespace, **kwargs):
    """
        delete collection of DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_collection_namespaced_daemon_set(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.delete_collection_namespaced_daemon_set_with_http_info(
          namespace, **kwargs)
    else:
      (data) = self.delete_collection_namespaced_daemon_set_with_http_info(
          namespace, **kwargs)
      return data

  def delete_collection_namespaced_daemon_set_with_http_info(
      self, namespace, **kwargs):
    """
        delete collection of DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.delete_collection_namespaced_daemon_set_with_http_info(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'namespace', 'pretty', '_continue', 'field_selector', 'label_selector',
        'limit', 'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method delete_collection_namespaced_daemon_set' %
                        key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `delete_collection_namespaced_daemon_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/daemonsets',
        'DELETE',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1Status',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def delete_collection_namespaced_deployment(self, namespace, **kwargs):
    """
        delete collection of Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_collection_namespaced_deployment(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.delete_collection_namespaced_deployment_with_http_info(
          namespace, **kwargs)
    else:
      (data) = self.delete_collection_namespaced_deployment_with_http_info(
          namespace, **kwargs)
      return data

  def delete_collection_namespaced_deployment_with_http_info(
      self, namespace, **kwargs):
    """
        delete collection of Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.delete_collection_namespaced_deployment_with_http_info(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'namespace', 'pretty', '_continue', 'field_selector', 'label_selector',
        'limit', 'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method delete_collection_namespaced_deployment' %
                        key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `delete_collection_namespaced_deployment`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments',
        'DELETE',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1Status',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def delete_collection_namespaced_replica_set(self, namespace, **kwargs):
    """
        delete collection of ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_collection_namespaced_replica_set(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.delete_collection_namespaced_replica_set_with_http_info(
          namespace, **kwargs)
    else:
      (data) = self.delete_collection_namespaced_replica_set_with_http_info(
          namespace, **kwargs)
      return data

  def delete_collection_namespaced_replica_set_with_http_info(
      self, namespace, **kwargs):
    """
        delete collection of ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.delete_collection_namespaced_replica_set_with_http_info(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'namespace', 'pretty', '_continue', 'field_selector', 'label_selector',
        'limit', 'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method delete_collection_namespaced_replica_set' %
                        key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `delete_collection_namespaced_replica_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets',
        'DELETE',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1Status',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def delete_collection_namespaced_stateful_set(self, namespace, **kwargs):
    """
        delete collection of StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_collection_namespaced_stateful_set(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.delete_collection_namespaced_stateful_set_with_http_info(
          namespace, **kwargs)
    else:
      (data) = self.delete_collection_namespaced_stateful_set_with_http_info(
          namespace, **kwargs)
      return data

  def delete_collection_namespaced_stateful_set_with_http_info(
      self, namespace, **kwargs):
    """
        delete collection of StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.delete_collection_namespaced_stateful_set_with_http_info(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'namespace', 'pretty', '_continue', 'field_selector', 'label_selector',
        'limit', 'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method delete_collection_namespaced_stateful_set' %
                        key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `delete_collection_namespaced_stateful_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets',
        'DELETE',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1Status',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def delete_namespaced_controller_revision(self, name, namespace, **kwargs):
    """
        delete a ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_namespaced_controller_revision(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ControllerRevision (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param V1DeleteOptions body:
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param int grace_period_seconds: The duration in seconds before the
        object should be deleted. Value must be non-negative integer. The value
        zero indicates delete immediately. If this value is nil, the default
        grace period for the specified type will be used. Defaults to a per
        object value if not specified. zero means delete immediately.
        :param bool orphan_dependents: Deprecated: please use the
        PropagationPolicy, this field will be deprecated in 1.7. Should the
        dependent objects be orphaned. If true/false, the \"orphan\" finalizer
        will be added to/removed from the object's finalizers list. Either this
        field or PropagationPolicy may be set, but not both.
        :param str propagation_policy: Whether and how garbage collection will
        be performed. Either this field or OrphanDependents may be set, but not
        both. The default policy is decided by the existing finalizer set in the
        metadata.finalizers and the resource-specific default policy. Acceptable
        values are: 'Orphan' - orphan the dependents; 'Background' - allow the
        garbage collector to delete the dependents in the background;
        'Foreground' - a cascading policy that deletes all dependents in the
        foreground.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.delete_namespaced_controller_revision_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.delete_namespaced_controller_revision_with_http_info(
          name, namespace, **kwargs)
      return data

  def delete_namespaced_controller_revision_with_http_info(
      self, name, namespace, **kwargs):
    """
        delete a ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.delete_namespaced_controller_revision_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ControllerRevision (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param V1DeleteOptions body:
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param int grace_period_seconds: The duration in seconds before the
        object should be deleted. Value must be non-negative integer. The value
        zero indicates delete immediately. If this value is nil, the default
        grace period for the specified type will be used. Defaults to a per
        object value if not specified. zero means delete immediately.
        :param bool orphan_dependents: Deprecated: please use the
        PropagationPolicy, this field will be deprecated in 1.7. Should the
        dependent objects be orphaned. If true/false, the \"orphan\" finalizer
        will be added to/removed from the object's finalizers list. Either this
        field or PropagationPolicy may be set, but not both.
        :param str propagation_policy: Whether and how garbage collection will
        be performed. Either this field or OrphanDependents may be set, but not
        both. The default policy is decided by the existing finalizer set in the
        metadata.finalizers and the resource-specific default policy. Acceptable
        values are: 'Orphan' - orphan the dependents; 'Background' - allow the
        garbage collector to delete the dependents in the background;
        'Foreground' - a cascading policy that deletes all dependents in the
        foreground.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'pretty', 'body', 'dry_run',
        'grace_period_seconds', 'orphan_dependents', 'propagation_policy'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method delete_namespaced_controller_revision' %
                        key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `delete_namespaced_controller_revision`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `delete_namespaced_controller_revision`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'grace_period_seconds' in params:
      query_params.append(
          ('gracePeriodSeconds', params['grace_period_seconds']))
    if 'orphan_dependents' in params:
      query_params.append(('orphanDependents', params['orphan_dependents']))
    if 'propagation_policy' in params:
      query_params.append(('propagationPolicy', params['propagation_policy']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/controllerrevisions/{name}',
        'DELETE',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1Status',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def delete_namespaced_daemon_set(self, name, namespace, **kwargs):
    """
        delete a DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_namespaced_daemon_set(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param V1DeleteOptions body:
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param int grace_period_seconds: The duration in seconds before the
        object should be deleted. Value must be non-negative integer. The value
        zero indicates delete immediately. If this value is nil, the default
        grace period for the specified type will be used. Defaults to a per
        object value if not specified. zero means delete immediately.
        :param bool orphan_dependents: Deprecated: please use the
        PropagationPolicy, this field will be deprecated in 1.7. Should the
        dependent objects be orphaned. If true/false, the \"orphan\" finalizer
        will be added to/removed from the object's finalizers list. Either this
        field or PropagationPolicy may be set, but not both.
        :param str propagation_policy: Whether and how garbage collection will
        be performed. Either this field or OrphanDependents may be set, but not
        both. The default policy is decided by the existing finalizer set in the
        metadata.finalizers and the resource-specific default policy. Acceptable
        values are: 'Orphan' - orphan the dependents; 'Background' - allow the
        garbage collector to delete the dependents in the background;
        'Foreground' - a cascading policy that deletes all dependents in the
        foreground.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.delete_namespaced_daemon_set_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.delete_namespaced_daemon_set_with_http_info(
          name, namespace, **kwargs)
      return data

  def delete_namespaced_daemon_set_with_http_info(self, name, namespace,
                                                  **kwargs):
    """
        delete a DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_namespaced_daemon_set_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param V1DeleteOptions body:
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param int grace_period_seconds: The duration in seconds before the
        object should be deleted. Value must be non-negative integer. The value
        zero indicates delete immediately. If this value is nil, the default
        grace period for the specified type will be used. Defaults to a per
        object value if not specified. zero means delete immediately.
        :param bool orphan_dependents: Deprecated: please use the
        PropagationPolicy, this field will be deprecated in 1.7. Should the
        dependent objects be orphaned. If true/false, the \"orphan\" finalizer
        will be added to/removed from the object's finalizers list. Either this
        field or PropagationPolicy may be set, but not both.
        :param str propagation_policy: Whether and how garbage collection will
        be performed. Either this field or OrphanDependents may be set, but not
        both. The default policy is decided by the existing finalizer set in the
        metadata.finalizers and the resource-specific default policy. Acceptable
        values are: 'Orphan' - orphan the dependents; 'Background' - allow the
        garbage collector to delete the dependents in the background;
        'Foreground' - a cascading policy that deletes all dependents in the
        foreground.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'pretty', 'body', 'dry_run',
        'grace_period_seconds', 'orphan_dependents', 'propagation_policy'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method delete_namespaced_daemon_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `delete_namespaced_daemon_set`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `delete_namespaced_daemon_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'grace_period_seconds' in params:
      query_params.append(
          ('gracePeriodSeconds', params['grace_period_seconds']))
    if 'orphan_dependents' in params:
      query_params.append(('orphanDependents', params['orphan_dependents']))
    if 'propagation_policy' in params:
      query_params.append(('propagationPolicy', params['propagation_policy']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/daemonsets/{name}',
        'DELETE',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1Status',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def delete_namespaced_deployment(self, name, namespace, **kwargs):
    """
        delete a Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_namespaced_deployment(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param V1DeleteOptions body:
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param int grace_period_seconds: The duration in seconds before the
        object should be deleted. Value must be non-negative integer. The value
        zero indicates delete immediately. If this value is nil, the default
        grace period for the specified type will be used. Defaults to a per
        object value if not specified. zero means delete immediately.
        :param bool orphan_dependents: Deprecated: please use the
        PropagationPolicy, this field will be deprecated in 1.7. Should the
        dependent objects be orphaned. If true/false, the \"orphan\" finalizer
        will be added to/removed from the object's finalizers list. Either this
        field or PropagationPolicy may be set, but not both.
        :param str propagation_policy: Whether and how garbage collection will
        be performed. Either this field or OrphanDependents may be set, but not
        both. The default policy is decided by the existing finalizer set in the
        metadata.finalizers and the resource-specific default policy. Acceptable
        values are: 'Orphan' - orphan the dependents; 'Background' - allow the
        garbage collector to delete the dependents in the background;
        'Foreground' - a cascading policy that deletes all dependents in the
        foreground.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.delete_namespaced_deployment_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.delete_namespaced_deployment_with_http_info(
          name, namespace, **kwargs)
      return data

  def delete_namespaced_deployment_with_http_info(self, name, namespace,
                                                  **kwargs):
    """
        delete a Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_namespaced_deployment_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param V1DeleteOptions body:
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param int grace_period_seconds: The duration in seconds before the
        object should be deleted. Value must be non-negative integer. The value
        zero indicates delete immediately. If this value is nil, the default
        grace period for the specified type will be used. Defaults to a per
        object value if not specified. zero means delete immediately.
        :param bool orphan_dependents: Deprecated: please use the
        PropagationPolicy, this field will be deprecated in 1.7. Should the
        dependent objects be orphaned. If true/false, the \"orphan\" finalizer
        will be added to/removed from the object's finalizers list. Either this
        field or PropagationPolicy may be set, but not both.
        :param str propagation_policy: Whether and how garbage collection will
        be performed. Either this field or OrphanDependents may be set, but not
        both. The default policy is decided by the existing finalizer set in the
        metadata.finalizers and the resource-specific default policy. Acceptable
        values are: 'Orphan' - orphan the dependents; 'Background' - allow the
        garbage collector to delete the dependents in the background;
        'Foreground' - a cascading policy that deletes all dependents in the
        foreground.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'pretty', 'body', 'dry_run',
        'grace_period_seconds', 'orphan_dependents', 'propagation_policy'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method delete_namespaced_deployment' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `delete_namespaced_deployment`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `delete_namespaced_deployment`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'grace_period_seconds' in params:
      query_params.append(
          ('gracePeriodSeconds', params['grace_period_seconds']))
    if 'orphan_dependents' in params:
      query_params.append(('orphanDependents', params['orphan_dependents']))
    if 'propagation_policy' in params:
      query_params.append(('propagationPolicy', params['propagation_policy']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments/{name}',
        'DELETE',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1Status',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def delete_namespaced_replica_set(self, name, namespace, **kwargs):
    """
        delete a ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_namespaced_replica_set(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param V1DeleteOptions body:
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param int grace_period_seconds: The duration in seconds before the
        object should be deleted. Value must be non-negative integer. The value
        zero indicates delete immediately. If this value is nil, the default
        grace period for the specified type will be used. Defaults to a per
        object value if not specified. zero means delete immediately.
        :param bool orphan_dependents: Deprecated: please use the
        PropagationPolicy, this field will be deprecated in 1.7. Should the
        dependent objects be orphaned. If true/false, the \"orphan\" finalizer
        will be added to/removed from the object's finalizers list. Either this
        field or PropagationPolicy may be set, but not both.
        :param str propagation_policy: Whether and how garbage collection will
        be performed. Either this field or OrphanDependents may be set, but not
        both. The default policy is decided by the existing finalizer set in the
        metadata.finalizers and the resource-specific default policy. Acceptable
        values are: 'Orphan' - orphan the dependents; 'Background' - allow the
        garbage collector to delete the dependents in the background;
        'Foreground' - a cascading policy that deletes all dependents in the
        foreground.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.delete_namespaced_replica_set_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.delete_namespaced_replica_set_with_http_info(
          name, namespace, **kwargs)
      return data

  def delete_namespaced_replica_set_with_http_info(self, name, namespace,
                                                   **kwargs):
    """
        delete a ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_namespaced_replica_set_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param V1DeleteOptions body:
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param int grace_period_seconds: The duration in seconds before the
        object should be deleted. Value must be non-negative integer. The value
        zero indicates delete immediately. If this value is nil, the default
        grace period for the specified type will be used. Defaults to a per
        object value if not specified. zero means delete immediately.
        :param bool orphan_dependents: Deprecated: please use the
        PropagationPolicy, this field will be deprecated in 1.7. Should the
        dependent objects be orphaned. If true/false, the \"orphan\" finalizer
        will be added to/removed from the object's finalizers list. Either this
        field or PropagationPolicy may be set, but not both.
        :param str propagation_policy: Whether and how garbage collection will
        be performed. Either this field or OrphanDependents may be set, but not
        both. The default policy is decided by the existing finalizer set in the
        metadata.finalizers and the resource-specific default policy. Acceptable
        values are: 'Orphan' - orphan the dependents; 'Background' - allow the
        garbage collector to delete the dependents in the background;
        'Foreground' - a cascading policy that deletes all dependents in the
        foreground.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'pretty', 'body', 'dry_run',
        'grace_period_seconds', 'orphan_dependents', 'propagation_policy'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method delete_namespaced_replica_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `delete_namespaced_replica_set`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `delete_namespaced_replica_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'grace_period_seconds' in params:
      query_params.append(
          ('gracePeriodSeconds', params['grace_period_seconds']))
    if 'orphan_dependents' in params:
      query_params.append(('orphanDependents', params['orphan_dependents']))
    if 'propagation_policy' in params:
      query_params.append(('propagationPolicy', params['propagation_policy']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets/{name}',
        'DELETE',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1Status',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def delete_namespaced_stateful_set(self, name, namespace, **kwargs):
    """
        delete a StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_namespaced_stateful_set(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param V1DeleteOptions body:
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param int grace_period_seconds: The duration in seconds before the
        object should be deleted. Value must be non-negative integer. The value
        zero indicates delete immediately. If this value is nil, the default
        grace period for the specified type will be used. Defaults to a per
        object value if not specified. zero means delete immediately.
        :param bool orphan_dependents: Deprecated: please use the
        PropagationPolicy, this field will be deprecated in 1.7. Should the
        dependent objects be orphaned. If true/false, the \"orphan\" finalizer
        will be added to/removed from the object's finalizers list. Either this
        field or PropagationPolicy may be set, but not both.
        :param str propagation_policy: Whether and how garbage collection will
        be performed. Either this field or OrphanDependents may be set, but not
        both. The default policy is decided by the existing finalizer set in the
        metadata.finalizers and the resource-specific default policy. Acceptable
        values are: 'Orphan' - orphan the dependents; 'Background' - allow the
        garbage collector to delete the dependents in the background;
        'Foreground' - a cascading policy that deletes all dependents in the
        foreground.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.delete_namespaced_stateful_set_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.delete_namespaced_stateful_set_with_http_info(
          name, namespace, **kwargs)
      return data

  def delete_namespaced_stateful_set_with_http_info(self, name, namespace,
                                                    **kwargs):
    """
        delete a StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_namespaced_stateful_set_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param V1DeleteOptions body:
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param int grace_period_seconds: The duration in seconds before the
        object should be deleted. Value must be non-negative integer. The value
        zero indicates delete immediately. If this value is nil, the default
        grace period for the specified type will be used. Defaults to a per
        object value if not specified. zero means delete immediately.
        :param bool orphan_dependents: Deprecated: please use the
        PropagationPolicy, this field will be deprecated in 1.7. Should the
        dependent objects be orphaned. If true/false, the \"orphan\" finalizer
        will be added to/removed from the object's finalizers list. Either this
        field or PropagationPolicy may be set, but not both.
        :param str propagation_policy: Whether and how garbage collection will
        be performed. Either this field or OrphanDependents may be set, but not
        both. The default policy is decided by the existing finalizer set in the
        metadata.finalizers and the resource-specific default policy. Acceptable
        values are: 'Orphan' - orphan the dependents; 'Background' - allow the
        garbage collector to delete the dependents in the background;
        'Foreground' - a cascading policy that deletes all dependents in the
        foreground.
        :return: V1Status
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'pretty', 'body', 'dry_run',
        'grace_period_seconds', 'orphan_dependents', 'propagation_policy'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method delete_namespaced_stateful_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `delete_namespaced_stateful_set`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `delete_namespaced_stateful_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'grace_period_seconds' in params:
      query_params.append(
          ('gracePeriodSeconds', params['grace_period_seconds']))
    if 'orphan_dependents' in params:
      query_params.append(('orphanDependents', params['orphan_dependents']))
    if 'propagation_policy' in params:
      query_params.append(('propagationPolicy', params['propagation_policy']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets/{name}',
        'DELETE',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1Status',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def get_api_resources(self, **kwargs):
    """
        get available resources
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_api_resources(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :return: V1APIResourceList
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.get_api_resources_with_http_info(**kwargs)
    else:
      (data) = self.get_api_resources_with_http_info(**kwargs)
      return data

  def get_api_resources_with_http_info(self, **kwargs):
    """
        get available resources
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_api_resources_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :return: V1APIResourceList
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = []
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method get_api_resources' % key)
      params[key] = val
    del params['kwargs']

    collection_formats = {}

    path_params = {}

    query_params = []

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1APIResourceList',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def list_controller_revision_for_all_namespaces(self, **kwargs):
    """
        list or watch objects of kind ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.list_controller_revision_for_all_namespaces(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str pretty: If 'true', then the output is pretty printed.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2ControllerRevisionList
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.list_controller_revision_for_all_namespaces_with_http_info(
          **kwargs)
    else:
      (data) = self.list_controller_revision_for_all_namespaces_with_http_info(
          **kwargs)
      return data

  def list_controller_revision_for_all_namespaces_with_http_info(
      self, **kwargs):
    """
        list or watch objects of kind ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.list_controller_revision_for_all_namespaces_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str pretty: If 'true', then the output is pretty printed.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2ControllerRevisionList
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        '_continue', 'field_selector', 'label_selector', 'limit', 'pretty',
        'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError(
            "Got an unexpected keyword argument '%s'"
            ' to method list_controller_revision_for_all_namespaces' % key)
      params[key] = val
    del params['kwargs']

    collection_formats = {}

    path_params = {}

    query_params = []
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf', 'application/json;stream=watch', 'application/vnd.kubernetes.protobuf;stream=watch'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/controllerrevisions',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ControllerRevisionList',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def list_daemon_set_for_all_namespaces(self, **kwargs):
    """
        list or watch objects of kind DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_daemon_set_for_all_namespaces(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str pretty: If 'true', then the output is pretty printed.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2DaemonSetList
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.list_daemon_set_for_all_namespaces_with_http_info(**kwargs)
    else:
      (data) = self.list_daemon_set_for_all_namespaces_with_http_info(**kwargs)
      return data

  def list_daemon_set_for_all_namespaces_with_http_info(self, **kwargs):
    """
        list or watch objects of kind DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.list_daemon_set_for_all_namespaces_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str pretty: If 'true', then the output is pretty printed.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2DaemonSetList
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        '_continue', 'field_selector', 'label_selector', 'limit', 'pretty',
        'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method list_daemon_set_for_all_namespaces' % key)
      params[key] = val
    del params['kwargs']

    collection_formats = {}

    path_params = {}

    query_params = []
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf', 'application/json;stream=watch', 'application/vnd.kubernetes.protobuf;stream=watch'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/daemonsets',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2DaemonSetList',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def list_deployment_for_all_namespaces(self, **kwargs):
    """
        list or watch objects of kind Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_deployment_for_all_namespaces(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str pretty: If 'true', then the output is pretty printed.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2DeploymentList
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.list_deployment_for_all_namespaces_with_http_info(**kwargs)
    else:
      (data) = self.list_deployment_for_all_namespaces_with_http_info(**kwargs)
      return data

  def list_deployment_for_all_namespaces_with_http_info(self, **kwargs):
    """
        list or watch objects of kind Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.list_deployment_for_all_namespaces_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str pretty: If 'true', then the output is pretty printed.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2DeploymentList
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        '_continue', 'field_selector', 'label_selector', 'limit', 'pretty',
        'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method list_deployment_for_all_namespaces' % key)
      params[key] = val
    del params['kwargs']

    collection_formats = {}

    path_params = {}

    query_params = []
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf', 'application/json;stream=watch', 'application/vnd.kubernetes.protobuf;stream=watch'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/deployments',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2DeploymentList',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def list_namespaced_controller_revision(self, namespace, **kwargs):
    """
        list or watch objects of kind ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_namespaced_controller_revision(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2ControllerRevisionList
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.list_namespaced_controller_revision_with_http_info(
          namespace, **kwargs)
    else:
      (data) = self.list_namespaced_controller_revision_with_http_info(
          namespace, **kwargs)
      return data

  def list_namespaced_controller_revision_with_http_info(
      self, namespace, **kwargs):
    """
        list or watch objects of kind ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.list_namespaced_controller_revision_with_http_info(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2ControllerRevisionList
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'namespace', 'pretty', '_continue', 'field_selector', 'label_selector',
        'limit', 'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method list_namespaced_controller_revision' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `list_namespaced_controller_revision`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf', 'application/json;stream=watch', 'application/vnd.kubernetes.protobuf;stream=watch'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/controllerrevisions',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ControllerRevisionList',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def list_namespaced_daemon_set(self, namespace, **kwargs):
    """
        list or watch objects of kind DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_namespaced_daemon_set(namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2DaemonSetList
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.list_namespaced_daemon_set_with_http_info(namespace, **kwargs)
    else:
      (data) = self.list_namespaced_daemon_set_with_http_info(
          namespace, **kwargs)
      return data

  def list_namespaced_daemon_set_with_http_info(self, namespace, **kwargs):
    """
        list or watch objects of kind DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_namespaced_daemon_set_with_http_info(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2DaemonSetList
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'namespace', 'pretty', '_continue', 'field_selector', 'label_selector',
        'limit', 'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method list_namespaced_daemon_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `list_namespaced_daemon_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf', 'application/json;stream=watch', 'application/vnd.kubernetes.protobuf;stream=watch'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/daemonsets',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2DaemonSetList',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def list_namespaced_deployment(self, namespace, **kwargs):
    """
        list or watch objects of kind Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_namespaced_deployment(namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2DeploymentList
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.list_namespaced_deployment_with_http_info(namespace, **kwargs)
    else:
      (data) = self.list_namespaced_deployment_with_http_info(
          namespace, **kwargs)
      return data

  def list_namespaced_deployment_with_http_info(self, namespace, **kwargs):
    """
        list or watch objects of kind Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_namespaced_deployment_with_http_info(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2DeploymentList
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'namespace', 'pretty', '_continue', 'field_selector', 'label_selector',
        'limit', 'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method list_namespaced_deployment' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `list_namespaced_deployment`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf', 'application/json;stream=watch', 'application/vnd.kubernetes.protobuf;stream=watch'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2DeploymentList',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def list_namespaced_replica_set(self, namespace, **kwargs):
    """
        list or watch objects of kind ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_namespaced_replica_set(namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2ReplicaSetList
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.list_namespaced_replica_set_with_http_info(
          namespace, **kwargs)
    else:
      (data) = self.list_namespaced_replica_set_with_http_info(
          namespace, **kwargs)
      return data

  def list_namespaced_replica_set_with_http_info(self, namespace, **kwargs):
    """
        list or watch objects of kind ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_namespaced_replica_set_with_http_info(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2ReplicaSetList
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'namespace', 'pretty', '_continue', 'field_selector', 'label_selector',
        'limit', 'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method list_namespaced_replica_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `list_namespaced_replica_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf', 'application/json;stream=watch', 'application/vnd.kubernetes.protobuf;stream=watch'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ReplicaSetList',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def list_namespaced_stateful_set(self, namespace, **kwargs):
    """
        list or watch objects of kind StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_namespaced_stateful_set(namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2StatefulSetList
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.list_namespaced_stateful_set_with_http_info(
          namespace, **kwargs)
    else:
      (data) = self.list_namespaced_stateful_set_with_http_info(
          namespace, **kwargs)
      return data

  def list_namespaced_stateful_set_with_http_info(self, namespace, **kwargs):
    """
        list or watch objects of kind StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_namespaced_stateful_set_with_http_info(namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2StatefulSetList
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'namespace', 'pretty', '_continue', 'field_selector', 'label_selector',
        'limit', 'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method list_namespaced_stateful_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `list_namespaced_stateful_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf', 'application/json;stream=watch', 'application/vnd.kubernetes.protobuf;stream=watch'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2StatefulSetList',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def list_replica_set_for_all_namespaces(self, **kwargs):
    """
        list or watch objects of kind ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_replica_set_for_all_namespaces(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str pretty: If 'true', then the output is pretty printed.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2ReplicaSetList
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.list_replica_set_for_all_namespaces_with_http_info(**kwargs)
    else:
      (data) = self.list_replica_set_for_all_namespaces_with_http_info(**kwargs)
      return data

  def list_replica_set_for_all_namespaces_with_http_info(self, **kwargs):
    """
        list or watch objects of kind ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.list_replica_set_for_all_namespaces_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str pretty: If 'true', then the output is pretty printed.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2ReplicaSetList
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        '_continue', 'field_selector', 'label_selector', 'limit', 'pretty',
        'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method list_replica_set_for_all_namespaces' % key)
      params[key] = val
    del params['kwargs']

    collection_formats = {}

    path_params = {}

    query_params = []
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf', 'application/json;stream=watch', 'application/vnd.kubernetes.protobuf;stream=watch'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/replicasets',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ReplicaSetList',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def list_stateful_set_for_all_namespaces(self, **kwargs):
    """
        list or watch objects of kind StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_stateful_set_for_all_namespaces(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str pretty: If 'true', then the output is pretty printed.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2StatefulSetList
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.list_stateful_set_for_all_namespaces_with_http_info(**kwargs)
    else:
      (data) = self.list_stateful_set_for_all_namespaces_with_http_info(
          **kwargs)
      return data

  def list_stateful_set_for_all_namespaces_with_http_info(self, **kwargs):
    """
        list or watch objects of kind StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.list_stateful_set_for_all_namespaces_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str _continue: The continue option should be set when retrieving
        more results from the server. Since this value is server defined,
        clients may only use the continue value from a previous query result
        with identical query parameters (except for the value of continue) and
        the server may reject a continue value it does not recognize. If the
        specified continue value is no longer valid whether due to expiration
        (generally five to fifteen minutes) or a configuration change on the
        server, the server will respond with a 410 ResourceExpired error
        together with a continue token. If the client needs a consistent list,
        it must restart their list without the continue field. Otherwise, the
        client may send another list request with the token received with the
        410 error, the server will respond with a list starting from the next
        key, but from the latest snapshot, which is inconsistent from the
        previous list results - objects that are created, modified, or deleted
        after the first list request will be included in the response, as long
        as their keys are after the \"next key\".  This field is not supported
        when watch is true. Clients may start a watch from the last
        resourceVersion value returned by the server and not miss any
        modifications.
        :param str field_selector: A selector to restrict the list of returned
        objects by their fields. Defaults to everything.
        :param str label_selector: A selector to restrict the list of returned
        objects by their labels. Defaults to everything.
        :param int limit: limit is a maximum number of responses to return for a
        list call. If more items exist, the server will set the `continue` field
        on the list metadata to a value that can be used with the same initial
        query to retrieve the next set of results. Setting a limit may return
        fewer than the requested amount of items (up to zero items) in the event
        all requested objects are filtered out and clients should only use the
        presence of the continue field to determine whether more results are
        available. Servers may choose not to support the limit argument and will
        return all of the available results. If limit is specified and the
        continue field is empty, clients may assume that no more results are
        available. This field is not supported if watch is true.  The server
        guarantees that the objects returned when using continue will be
        identical to issuing a single list call without a limit - that is, no
        objects created, modified, or deleted after the first request is issued
        will be included in any subsequent continued requests. This is sometimes
        referred to as a consistent snapshot, and ensures that a client that is
        using limit to receive smaller chunks of a very large result can ensure
        they see all possible objects. If objects are updated during a chunked
        list the version of the object that was present at the time the first
        list result was calculated is returned.
        :param str pretty: If 'true', then the output is pretty printed.
        :param str resource_version: When specified with a watch call, shows
        changes that occur after that particular version of a resource. Defaults
        to changes from the beginning of history. When specified for list: - if
        unset, then the result is returned from remote storage based on
        quorum-read flag; - if it's 0, then we simply return what we currently
        have in cache, no guarantee; - if set to non zero, then the result is at
        least as fresh as given rv.
        :param int timeout_seconds: Timeout for the list/watch call. This limits
        the duration of the call, regardless of any activity or inactivity.
        :param bool watch: Watch for changes to the described resources and
        return them as a stream of add, update, and remove notifications.
        Specify resourceVersion.
        :return: V1beta2StatefulSetList
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        '_continue', 'field_selector', 'label_selector', 'limit', 'pretty',
        'resource_version', 'timeout_seconds', 'watch'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method list_stateful_set_for_all_namespaces' % key)
      params[key] = val
    del params['kwargs']

    collection_formats = {}

    path_params = {}

    query_params = []
    if '_continue' in params:
      query_params.append(('continue', params['_continue']))
    if 'field_selector' in params:
      query_params.append(('fieldSelector', params['field_selector']))
    if 'label_selector' in params:
      query_params.append(('labelSelector', params['label_selector']))
    if 'limit' in params:
      query_params.append(('limit', params['limit']))
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'resource_version' in params:
      query_params.append(('resourceVersion', params['resource_version']))
    if 'timeout_seconds' in params:
      query_params.append(('timeoutSeconds', params['timeout_seconds']))
    if 'watch' in params:
      query_params.append(('watch', params['watch']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf', 'application/json;stream=watch', 'application/vnd.kubernetes.protobuf;stream=watch'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/statefulsets',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2StatefulSetList',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def patch_namespaced_controller_revision(self, name, namespace, body,
                                           **kwargs):
    """
        partially update the specified ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_controller_revision(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ControllerRevision (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2ControllerRevision
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.patch_namespaced_controller_revision_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.patch_namespaced_controller_revision_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def patch_namespaced_controller_revision_with_http_info(
      self, name, namespace, body, **kwargs):
    """
        partially update the specified ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.patch_namespaced_controller_revision_with_http_info(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ControllerRevision (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2ControllerRevision
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager',
        'force'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method patch_namespaced_controller_revision' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `patch_namespaced_controller_revision`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `patch_namespaced_controller_revision`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `patch_namespaced_controller_revision`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))
    if 'force' in params:
      query_params.append(('force', params['force']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json-patch+json', 'application/merge-patch+json', 'application/strategic-merge-patch+json'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/controllerrevisions/{name}',
        'PATCH',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ControllerRevision',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def patch_namespaced_daemon_set(self, name, namespace, body, **kwargs):
    """
        partially update the specified DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_daemon_set(name, namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.patch_namespaced_daemon_set_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.patch_namespaced_daemon_set_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def patch_namespaced_daemon_set_with_http_info(self, name, namespace, body,
                                                 **kwargs):
    """
        partially update the specified DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_daemon_set_with_http_info(name,
        namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager',
        'force'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method patch_namespaced_daemon_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `patch_namespaced_daemon_set`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `patch_namespaced_daemon_set`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `patch_namespaced_daemon_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))
    if 'force' in params:
      query_params.append(('force', params['force']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json-patch+json', 'application/merge-patch+json', 'application/strategic-merge-patch+json'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/daemonsets/{name}',
        'PATCH',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2DaemonSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def patch_namespaced_daemon_set_status(self, name, namespace, body, **kwargs):
    """
        partially update status of the specified DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_daemon_set_status(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.patch_namespaced_daemon_set_status_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.patch_namespaced_daemon_set_status_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def patch_namespaced_daemon_set_status_with_http_info(self, name, namespace,
                                                        body, **kwargs):
    """
        partially update status of the specified DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_daemon_set_status_with_http_info(name,
        namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager',
        'force'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method patch_namespaced_daemon_set_status' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `patch_namespaced_daemon_set_status`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `patch_namespaced_daemon_set_status`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `patch_namespaced_daemon_set_status`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))
    if 'force' in params:
      query_params.append(('force', params['force']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json-patch+json', 'application/merge-patch+json', 'application/strategic-merge-patch+json'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/daemonsets/{name}/status',
        'PATCH',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2DaemonSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def patch_namespaced_deployment(self, name, namespace, body, **kwargs):
    """
        partially update the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_deployment(name, namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.patch_namespaced_deployment_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.patch_namespaced_deployment_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def patch_namespaced_deployment_with_http_info(self, name, namespace, body,
                                                 **kwargs):
    """
        partially update the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_deployment_with_http_info(name,
        namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager',
        'force'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method patch_namespaced_deployment' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `patch_namespaced_deployment`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `patch_namespaced_deployment`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `patch_namespaced_deployment`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))
    if 'force' in params:
      query_params.append(('force', params['force']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json-patch+json', 'application/merge-patch+json', 'application/strategic-merge-patch+json'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments/{name}',
        'PATCH',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Deployment',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def patch_namespaced_deployment_scale(self, name, namespace, body, **kwargs):
    """
        partially update scale of the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_deployment_scale(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.patch_namespaced_deployment_scale_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.patch_namespaced_deployment_scale_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def patch_namespaced_deployment_scale_with_http_info(self, name, namespace,
                                                       body, **kwargs):
    """
        partially update scale of the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_deployment_scale_with_http_info(name,
        namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager',
        'force'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method patch_namespaced_deployment_scale' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `patch_namespaced_deployment_scale`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `patch_namespaced_deployment_scale`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `patch_namespaced_deployment_scale`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))
    if 'force' in params:
      query_params.append(('force', params['force']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json-patch+json', 'application/merge-patch+json', 'application/strategic-merge-patch+json'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments/{name}/scale',
        'PATCH',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Scale',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def patch_namespaced_deployment_status(self, name, namespace, body, **kwargs):
    """
        partially update status of the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_deployment_status(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.patch_namespaced_deployment_status_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.patch_namespaced_deployment_status_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def patch_namespaced_deployment_status_with_http_info(self, name, namespace,
                                                        body, **kwargs):
    """
        partially update status of the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_deployment_status_with_http_info(name,
        namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager',
        'force'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method patch_namespaced_deployment_status' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `patch_namespaced_deployment_status`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `patch_namespaced_deployment_status`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `patch_namespaced_deployment_status`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))
    if 'force' in params:
      query_params.append(('force', params['force']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json-patch+json', 'application/merge-patch+json', 'application/strategic-merge-patch+json'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments/{name}/status',
        'PATCH',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Deployment',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def patch_namespaced_replica_set(self, name, namespace, body, **kwargs):
    """
        partially update the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_replica_set(name, namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.patch_namespaced_replica_set_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.patch_namespaced_replica_set_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def patch_namespaced_replica_set_with_http_info(self, name, namespace, body,
                                                  **kwargs):
    """
        partially update the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_replica_set_with_http_info(name,
        namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager',
        'force'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method patch_namespaced_replica_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `patch_namespaced_replica_set`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `patch_namespaced_replica_set`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `patch_namespaced_replica_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))
    if 'force' in params:
      query_params.append(('force', params['force']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json-patch+json', 'application/merge-patch+json', 'application/strategic-merge-patch+json'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets/{name}',
        'PATCH',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ReplicaSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def patch_namespaced_replica_set_scale(self, name, namespace, body, **kwargs):
    """
        partially update scale of the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_replica_set_scale(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.patch_namespaced_replica_set_scale_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.patch_namespaced_replica_set_scale_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def patch_namespaced_replica_set_scale_with_http_info(self, name, namespace,
                                                        body, **kwargs):
    """
        partially update scale of the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_replica_set_scale_with_http_info(name,
        namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager',
        'force'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method patch_namespaced_replica_set_scale' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `patch_namespaced_replica_set_scale`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `patch_namespaced_replica_set_scale`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `patch_namespaced_replica_set_scale`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))
    if 'force' in params:
      query_params.append(('force', params['force']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json-patch+json', 'application/merge-patch+json', 'application/strategic-merge-patch+json'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets/{name}/scale',
        'PATCH',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Scale',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def patch_namespaced_replica_set_status(self, name, namespace, body,
                                          **kwargs):
    """
        partially update status of the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_replica_set_status(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.patch_namespaced_replica_set_status_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.patch_namespaced_replica_set_status_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def patch_namespaced_replica_set_status_with_http_info(
      self, name, namespace, body, **kwargs):
    """
        partially update status of the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.patch_namespaced_replica_set_status_with_http_info(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager',
        'force'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method patch_namespaced_replica_set_status' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `patch_namespaced_replica_set_status`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `patch_namespaced_replica_set_status`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `patch_namespaced_replica_set_status`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))
    if 'force' in params:
      query_params.append(('force', params['force']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json-patch+json', 'application/merge-patch+json', 'application/strategic-merge-patch+json'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets/{name}/status',
        'PATCH',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ReplicaSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def patch_namespaced_stateful_set(self, name, namespace, body, **kwargs):
    """
        partially update the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_stateful_set(name, namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.patch_namespaced_stateful_set_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.patch_namespaced_stateful_set_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def patch_namespaced_stateful_set_with_http_info(self, name, namespace, body,
                                                   **kwargs):
    """
        partially update the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_stateful_set_with_http_info(name,
        namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager',
        'force'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method patch_namespaced_stateful_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `patch_namespaced_stateful_set`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `patch_namespaced_stateful_set`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `patch_namespaced_stateful_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))
    if 'force' in params:
      query_params.append(('force', params['force']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json-patch+json', 'application/merge-patch+json', 'application/strategic-merge-patch+json'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets/{name}',
        'PATCH',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2StatefulSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def patch_namespaced_stateful_set_scale(self, name, namespace, body,
                                          **kwargs):
    """
        partially update scale of the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_stateful_set_scale(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.patch_namespaced_stateful_set_scale_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.patch_namespaced_stateful_set_scale_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def patch_namespaced_stateful_set_scale_with_http_info(
      self, name, namespace, body, **kwargs):
    """
        partially update scale of the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.patch_namespaced_stateful_set_scale_with_http_info(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager',
        'force'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method patch_namespaced_stateful_set_scale' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `patch_namespaced_stateful_set_scale`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `patch_namespaced_stateful_set_scale`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `patch_namespaced_stateful_set_scale`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))
    if 'force' in params:
      query_params.append(('force', params['force']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json-patch+json', 'application/merge-patch+json', 'application/strategic-merge-patch+json'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets/{name}/scale',
        'PATCH',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Scale',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def patch_namespaced_stateful_set_status(self, name, namespace, body,
                                           **kwargs):
    """
        partially update status of the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.patch_namespaced_stateful_set_status(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.patch_namespaced_stateful_set_status_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.patch_namespaced_stateful_set_status_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def patch_namespaced_stateful_set_status_with_http_info(
      self, name, namespace, body, **kwargs):
    """
        partially update status of the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.patch_namespaced_stateful_set_status_with_http_info(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param object body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint. This field is
        required for apply requests (application/apply-patch) but optional for
        non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).
        :param bool force: Force is going to \"force\" Apply requests. It means
        user will re-acquire conflicting fields owned by other people. Force
        flag must be unset for non-apply patch requests.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager',
        'force'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method patch_namespaced_stateful_set_status' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `patch_namespaced_stateful_set_status`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `patch_namespaced_stateful_set_status`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `patch_namespaced_stateful_set_status`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))
    if 'force' in params:
      query_params.append(('force', params['force']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['application/json-patch+json', 'application/merge-patch+json', 'application/strategic-merge-patch+json'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets/{name}/status',
        'PATCH',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2StatefulSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def read_namespaced_controller_revision(self, name, namespace, **kwargs):
    """
        read the specified ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_controller_revision(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ControllerRevision (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param bool exact: Should the export be exact.  Exact export maintains
        cluster-specific fields like 'Namespace'. Deprecated. Planned for
        removal in 1.18.
        :param bool export: Should this value be exported.  Export strips fields
        that a user can not specify. Deprecated. Planned for removal in 1.18.
        :return: V1beta2ControllerRevision
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.read_namespaced_controller_revision_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.read_namespaced_controller_revision_with_http_info(
          name, namespace, **kwargs)
      return data

  def read_namespaced_controller_revision_with_http_info(
      self, name, namespace, **kwargs):
    """
        read the specified ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.read_namespaced_controller_revision_with_http_info(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ControllerRevision (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param bool exact: Should the export be exact.  Exact export maintains
        cluster-specific fields like 'Namespace'. Deprecated. Planned for
        removal in 1.18.
        :param bool export: Should this value be exported.  Export strips fields
        that a user can not specify. Deprecated. Planned for removal in 1.18.
        :return: V1beta2ControllerRevision
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['name', 'namespace', 'pretty', 'exact', 'export']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method read_namespaced_controller_revision' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `read_namespaced_controller_revision`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `read_namespaced_controller_revision`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'exact' in params:
      query_params.append(('exact', params['exact']))
    if 'export' in params:
      query_params.append(('export', params['export']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/controllerrevisions/{name}',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ControllerRevision',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def read_namespaced_daemon_set(self, name, namespace, **kwargs):
    """
        read the specified DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_daemon_set(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param bool exact: Should the export be exact.  Exact export maintains
        cluster-specific fields like 'Namespace'. Deprecated. Planned for
        removal in 1.18.
        :param bool export: Should this value be exported.  Export strips fields
        that a user can not specify. Deprecated. Planned for removal in 1.18.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.read_namespaced_daemon_set_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.read_namespaced_daemon_set_with_http_info(
          name, namespace, **kwargs)
      return data

  def read_namespaced_daemon_set_with_http_info(self, name, namespace,
                                                **kwargs):
    """
        read the specified DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_daemon_set_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param bool exact: Should the export be exact.  Exact export maintains
        cluster-specific fields like 'Namespace'. Deprecated. Planned for
        removal in 1.18.
        :param bool export: Should this value be exported.  Export strips fields
        that a user can not specify. Deprecated. Planned for removal in 1.18.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['name', 'namespace', 'pretty', 'exact', 'export']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method read_namespaced_daemon_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `read_namespaced_daemon_set`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `read_namespaced_daemon_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'exact' in params:
      query_params.append(('exact', params['exact']))
    if 'export' in params:
      query_params.append(('export', params['export']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/daemonsets/{name}',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2DaemonSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def read_namespaced_daemon_set_status(self, name, namespace, **kwargs):
    """
        read status of the specified DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_daemon_set_status(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.read_namespaced_daemon_set_status_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.read_namespaced_daemon_set_status_with_http_info(
          name, namespace, **kwargs)
      return data

  def read_namespaced_daemon_set_status_with_http_info(self, name, namespace,
                                                       **kwargs):
    """
        read status of the specified DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_daemon_set_status_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['name', 'namespace', 'pretty']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method read_namespaced_daemon_set_status' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `read_namespaced_daemon_set_status`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `read_namespaced_daemon_set_status`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/daemonsets/{name}/status',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2DaemonSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def read_namespaced_deployment(self, name, namespace, **kwargs):
    """
        read the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_deployment(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param bool exact: Should the export be exact.  Exact export maintains
        cluster-specific fields like 'Namespace'. Deprecated. Planned for
        removal in 1.18.
        :param bool export: Should this value be exported.  Export strips fields
        that a user can not specify. Deprecated. Planned for removal in 1.18.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.read_namespaced_deployment_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.read_namespaced_deployment_with_http_info(
          name, namespace, **kwargs)
      return data

  def read_namespaced_deployment_with_http_info(self, name, namespace,
                                                **kwargs):
    """
        read the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_deployment_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param bool exact: Should the export be exact.  Exact export maintains
        cluster-specific fields like 'Namespace'. Deprecated. Planned for
        removal in 1.18.
        :param bool export: Should this value be exported.  Export strips fields
        that a user can not specify. Deprecated. Planned for removal in 1.18.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['name', 'namespace', 'pretty', 'exact', 'export']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method read_namespaced_deployment' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `read_namespaced_deployment`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `read_namespaced_deployment`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'exact' in params:
      query_params.append(('exact', params['exact']))
    if 'export' in params:
      query_params.append(('export', params['export']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments/{name}',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Deployment',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def read_namespaced_deployment_scale(self, name, namespace, **kwargs):
    """
        read scale of the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_deployment_scale(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.read_namespaced_deployment_scale_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.read_namespaced_deployment_scale_with_http_info(
          name, namespace, **kwargs)
      return data

  def read_namespaced_deployment_scale_with_http_info(self, name, namespace,
                                                      **kwargs):
    """
        read scale of the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_deployment_scale_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['name', 'namespace', 'pretty']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method read_namespaced_deployment_scale' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `read_namespaced_deployment_scale`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `read_namespaced_deployment_scale`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments/{name}/scale',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Scale',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def read_namespaced_deployment_status(self, name, namespace, **kwargs):
    """
        read status of the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_deployment_status(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.read_namespaced_deployment_status_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.read_namespaced_deployment_status_with_http_info(
          name, namespace, **kwargs)
      return data

  def read_namespaced_deployment_status_with_http_info(self, name, namespace,
                                                       **kwargs):
    """
        read status of the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_deployment_status_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['name', 'namespace', 'pretty']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method read_namespaced_deployment_status' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `read_namespaced_deployment_status`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `read_namespaced_deployment_status`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments/{name}/status',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Deployment',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def read_namespaced_replica_set(self, name, namespace, **kwargs):
    """
        read the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_replica_set(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param bool exact: Should the export be exact.  Exact export maintains
        cluster-specific fields like 'Namespace'. Deprecated. Planned for
        removal in 1.18.
        :param bool export: Should this value be exported.  Export strips fields
        that a user can not specify. Deprecated. Planned for removal in 1.18.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.read_namespaced_replica_set_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.read_namespaced_replica_set_with_http_info(
          name, namespace, **kwargs)
      return data

  def read_namespaced_replica_set_with_http_info(self, name, namespace,
                                                 **kwargs):
    """
        read the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_replica_set_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param bool exact: Should the export be exact.  Exact export maintains
        cluster-specific fields like 'Namespace'. Deprecated. Planned for
        removal in 1.18.
        :param bool export: Should this value be exported.  Export strips fields
        that a user can not specify. Deprecated. Planned for removal in 1.18.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['name', 'namespace', 'pretty', 'exact', 'export']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method read_namespaced_replica_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `read_namespaced_replica_set`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `read_namespaced_replica_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'exact' in params:
      query_params.append(('exact', params['exact']))
    if 'export' in params:
      query_params.append(('export', params['export']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets/{name}',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ReplicaSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def read_namespaced_replica_set_scale(self, name, namespace, **kwargs):
    """
        read scale of the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_replica_set_scale(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.read_namespaced_replica_set_scale_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.read_namespaced_replica_set_scale_with_http_info(
          name, namespace, **kwargs)
      return data

  def read_namespaced_replica_set_scale_with_http_info(self, name, namespace,
                                                       **kwargs):
    """
        read scale of the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_replica_set_scale_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['name', 'namespace', 'pretty']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method read_namespaced_replica_set_scale' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `read_namespaced_replica_set_scale`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `read_namespaced_replica_set_scale`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets/{name}/scale',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Scale',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def read_namespaced_replica_set_status(self, name, namespace, **kwargs):
    """
        read status of the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_replica_set_status(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.read_namespaced_replica_set_status_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.read_namespaced_replica_set_status_with_http_info(
          name, namespace, **kwargs)
      return data

  def read_namespaced_replica_set_status_with_http_info(self, name, namespace,
                                                        **kwargs):
    """
        read status of the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_replica_set_status_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['name', 'namespace', 'pretty']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method read_namespaced_replica_set_status' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `read_namespaced_replica_set_status`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `read_namespaced_replica_set_status`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets/{name}/status',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ReplicaSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def read_namespaced_stateful_set(self, name, namespace, **kwargs):
    """
        read the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_stateful_set(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param bool exact: Should the export be exact.  Exact export maintains
        cluster-specific fields like 'Namespace'. Deprecated. Planned for
        removal in 1.18.
        :param bool export: Should this value be exported.  Export strips fields
        that a user can not specify. Deprecated. Planned for removal in 1.18.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.read_namespaced_stateful_set_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.read_namespaced_stateful_set_with_http_info(
          name, namespace, **kwargs)
      return data

  def read_namespaced_stateful_set_with_http_info(self, name, namespace,
                                                  **kwargs):
    """
        read the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_stateful_set_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param bool exact: Should the export be exact.  Exact export maintains
        cluster-specific fields like 'Namespace'. Deprecated. Planned for
        removal in 1.18.
        :param bool export: Should this value be exported.  Export strips fields
        that a user can not specify. Deprecated. Planned for removal in 1.18.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['name', 'namespace', 'pretty', 'exact', 'export']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method read_namespaced_stateful_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `read_namespaced_stateful_set`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `read_namespaced_stateful_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'exact' in params:
      query_params.append(('exact', params['exact']))
    if 'export' in params:
      query_params.append(('export', params['export']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets/{name}',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2StatefulSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def read_namespaced_stateful_set_scale(self, name, namespace, **kwargs):
    """
        read scale of the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_stateful_set_scale(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.read_namespaced_stateful_set_scale_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.read_namespaced_stateful_set_scale_with_http_info(
          name, namespace, **kwargs)
      return data

  def read_namespaced_stateful_set_scale_with_http_info(self, name, namespace,
                                                        **kwargs):
    """
        read scale of the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_stateful_set_scale_with_http_info(name,
        namespace, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['name', 'namespace', 'pretty']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method read_namespaced_stateful_set_scale' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `read_namespaced_stateful_set_scale`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `read_namespaced_stateful_set_scale`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets/{name}/scale',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Scale',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def read_namespaced_stateful_set_status(self, name, namespace, **kwargs):
    """
        read status of the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.read_namespaced_stateful_set_status(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.read_namespaced_stateful_set_status_with_http_info(
          name, namespace, **kwargs)
    else:
      (data) = self.read_namespaced_stateful_set_status_with_http_info(
          name, namespace, **kwargs)
      return data

  def read_namespaced_stateful_set_status_with_http_info(
      self, name, namespace, **kwargs):
    """
        read status of the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.read_namespaced_stateful_set_status_with_http_info(name, namespace,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = ['name', 'namespace', 'pretty']
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method read_namespaced_stateful_set_status' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `read_namespaced_stateful_set_status`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `read_namespaced_stateful_set_status`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets/{name}/status',
        'GET',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2StatefulSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def replace_namespaced_controller_revision(self, name, namespace, body,
                                             **kwargs):
    """
        replace the specified ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_controller_revision(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ControllerRevision (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2ControllerRevision body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2ControllerRevision
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.replace_namespaced_controller_revision_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.replace_namespaced_controller_revision_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def replace_namespaced_controller_revision_with_http_info(
      self, name, namespace, body, **kwargs):
    """
        replace the specified ControllerRevision
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.replace_namespaced_controller_revision_with_http_info(name,
        namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ControllerRevision (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2ControllerRevision body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2ControllerRevision
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method replace_namespaced_controller_revision' %
                        key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `replace_namespaced_controller_revision`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `replace_namespaced_controller_revision`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `replace_namespaced_controller_revision`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/controllerrevisions/{name}',
        'PUT',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ControllerRevision',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def replace_namespaced_daemon_set(self, name, namespace, body, **kwargs):
    """
        replace the specified DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_daemon_set(name, namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2DaemonSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.replace_namespaced_daemon_set_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.replace_namespaced_daemon_set_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def replace_namespaced_daemon_set_with_http_info(self, name, namespace, body,
                                                   **kwargs):
    """
        replace the specified DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_daemon_set_with_http_info(name,
        namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2DaemonSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method replace_namespaced_daemon_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `replace_namespaced_daemon_set`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `replace_namespaced_daemon_set`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `replace_namespaced_daemon_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/daemonsets/{name}',
        'PUT',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2DaemonSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def replace_namespaced_daemon_set_status(self, name, namespace, body,
                                           **kwargs):
    """
        replace status of the specified DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_daemon_set_status(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2DaemonSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.replace_namespaced_daemon_set_status_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.replace_namespaced_daemon_set_status_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def replace_namespaced_daemon_set_status_with_http_info(
      self, name, namespace, body, **kwargs):
    """
        replace status of the specified DaemonSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.replace_namespaced_daemon_set_status_with_http_info(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the DaemonSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2DaemonSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2DaemonSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method replace_namespaced_daemon_set_status' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `replace_namespaced_daemon_set_status`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `replace_namespaced_daemon_set_status`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `replace_namespaced_daemon_set_status`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/daemonsets/{name}/status',
        'PUT',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2DaemonSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def replace_namespaced_deployment(self, name, namespace, body, **kwargs):
    """
        replace the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_deployment(name, namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2Deployment body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.replace_namespaced_deployment_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.replace_namespaced_deployment_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def replace_namespaced_deployment_with_http_info(self, name, namespace, body,
                                                   **kwargs):
    """
        replace the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_deployment_with_http_info(name,
        namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2Deployment body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method replace_namespaced_deployment' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `replace_namespaced_deployment`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `replace_namespaced_deployment`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `replace_namespaced_deployment`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments/{name}',
        'PUT',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Deployment',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def replace_namespaced_deployment_scale(self, name, namespace, body,
                                          **kwargs):
    """
        replace scale of the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_deployment_scale(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2Scale body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.replace_namespaced_deployment_scale_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.replace_namespaced_deployment_scale_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def replace_namespaced_deployment_scale_with_http_info(
      self, name, namespace, body, **kwargs):
    """
        replace scale of the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.replace_namespaced_deployment_scale_with_http_info(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2Scale body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method replace_namespaced_deployment_scale' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `replace_namespaced_deployment_scale`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `replace_namespaced_deployment_scale`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `replace_namespaced_deployment_scale`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments/{name}/scale',
        'PUT',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Scale',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def replace_namespaced_deployment_status(self, name, namespace, body,
                                           **kwargs):
    """
        replace status of the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_deployment_status(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2Deployment body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.replace_namespaced_deployment_status_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.replace_namespaced_deployment_status_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def replace_namespaced_deployment_status_with_http_info(
      self, name, namespace, body, **kwargs):
    """
        replace status of the specified Deployment
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.replace_namespaced_deployment_status_with_http_info(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Deployment (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2Deployment body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2Deployment
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method replace_namespaced_deployment_status' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `replace_namespaced_deployment_status`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `replace_namespaced_deployment_status`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `replace_namespaced_deployment_status`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/deployments/{name}/status',
        'PUT',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Deployment',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def replace_namespaced_replica_set(self, name, namespace, body, **kwargs):
    """
        replace the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_replica_set(name, namespace, body,
        async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2ReplicaSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.replace_namespaced_replica_set_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.replace_namespaced_replica_set_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def replace_namespaced_replica_set_with_http_info(self, name, namespace, body,
                                                    **kwargs):
    """
        replace the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_replica_set_with_http_info(name,
        namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2ReplicaSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method replace_namespaced_replica_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `replace_namespaced_replica_set`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `replace_namespaced_replica_set`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `replace_namespaced_replica_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets/{name}',
        'PUT',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ReplicaSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def replace_namespaced_replica_set_scale(self, name, namespace, body,
                                           **kwargs):
    """
        replace scale of the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_replica_set_scale(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2Scale body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.replace_namespaced_replica_set_scale_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.replace_namespaced_replica_set_scale_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def replace_namespaced_replica_set_scale_with_http_info(
      self, name, namespace, body, **kwargs):
    """
        replace scale of the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread =
        api.replace_namespaced_replica_set_scale_with_http_info(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2Scale body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method replace_namespaced_replica_set_scale' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `replace_namespaced_replica_set_scale`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `replace_namespaced_replica_set_scale`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `replace_namespaced_replica_set_scale`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets/{name}/scale',
        'PUT',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Scale',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def replace_namespaced_replica_set_status(self, name, namespace, body,
                                            **kwargs):
    """
        replace status of the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_replica_set_status(name, namespace,
        body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and
        projects (required)
        :param V1beta2ReplicaSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should
        not be persisted. An invalid or unrecognized dryRun directive will
        result in an error response and no further processing of the request.
        Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the
        actor or entity that is making these changes. The value must be less
        than or 128 characters long, and only contain printable characters, as
        defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.replace_namespaced_replica_set_status_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.replace_namespaced_replica_set_status_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def replace_namespaced_replica_set_status_with_http_info(
      self, name, namespace, body, **kwargs):
    """
        replace status of the specified ReplicaSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_replica_set_status_with_http_info(name, namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the ReplicaSet (required)
        :param str namespace: object name and auth scope, such as for teams and projects (required)
        :param V1beta2ReplicaSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2ReplicaSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method replace_namespaced_replica_set_status' %
                        key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `replace_namespaced_replica_set_status`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `replace_namespaced_replica_set_status`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `replace_namespaced_replica_set_status`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/replicasets/{name}/status',
        'PUT',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2ReplicaSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def replace_namespaced_stateful_set(self, name, namespace, body, **kwargs):
    """
        replace the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_stateful_set(name, namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and projects (required)
        :param V1beta2StatefulSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.replace_namespaced_stateful_set_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.replace_namespaced_stateful_set_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def replace_namespaced_stateful_set_with_http_info(self, name, namespace,
                                                     body, **kwargs):
    """
        replace the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_stateful_set_with_http_info(name, namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and projects (required)
        :param V1beta2StatefulSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method replace_namespaced_stateful_set' % key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `replace_namespaced_stateful_set`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `replace_namespaced_stateful_set`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `replace_namespaced_stateful_set`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets/{name}',
        'PUT',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2StatefulSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def replace_namespaced_stateful_set_scale(self, name, namespace, body,
                                            **kwargs):
    """
        replace scale of the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_stateful_set_scale(name, namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and projects (required)
        :param V1beta2Scale body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.replace_namespaced_stateful_set_scale_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.replace_namespaced_stateful_set_scale_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def replace_namespaced_stateful_set_scale_with_http_info(
      self, name, namespace, body, **kwargs):
    """
        replace scale of the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_stateful_set_scale_with_http_info(name, namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the Scale (required)
        :param str namespace: object name and auth scope, such as for teams and projects (required)
        :param V1beta2Scale body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2Scale
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method replace_namespaced_stateful_set_scale' %
                        key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `replace_namespaced_stateful_set_scale`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `replace_namespaced_stateful_set_scale`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `replace_namespaced_stateful_set_scale`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets/{name}/scale',
        'PUT',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2Scale',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)

  def replace_namespaced_stateful_set_status(self, name, namespace, body,
                                             **kwargs):
    """
        replace status of the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_stateful_set_status(name, namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and projects (required)
        :param V1beta2StatefulSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """
    kwargs['_return_http_data_only'] = True
    if kwargs.get('async_req'):
      return self.replace_namespaced_stateful_set_status_with_http_info(
          name, namespace, body, **kwargs)
    else:
      (data) = self.replace_namespaced_stateful_set_status_with_http_info(
          name, namespace, body, **kwargs)
      return data

  def replace_namespaced_stateful_set_status_with_http_info(
      self, name, namespace, body, **kwargs):
    """
        replace status of the specified StatefulSet
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.replace_namespaced_stateful_set_status_with_http_info(name, namespace, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str name: name of the StatefulSet (required)
        :param str namespace: object name and auth scope, such as for teams and projects (required)
        :param V1beta2StatefulSet body: (required)
        :param str pretty: If 'true', then the output is pretty printed.
        :param str dry_run: When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed
        :param str field_manager: fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.
        :return: V1beta2StatefulSet
                 If the method is called asynchronously,
                 returns the request thread.
        """

    all_params = [
        'name', 'namespace', 'body', 'pretty', 'dry_run', 'field_manager'
    ]
    all_params.append('async_req')
    all_params.append('_return_http_data_only')
    all_params.append('_preload_content')
    all_params.append('_request_timeout')

    params = locals()
    for key, val in iteritems(params['kwargs']):
      if key not in all_params:
        raise TypeError("Got an unexpected keyword argument '%s'"
                        ' to method replace_namespaced_stateful_set_status' %
                        key)
      params[key] = val
    del params['kwargs']
    # verify the required parameter 'name' is set
    if ('name' not in params) or (params['name'] is None):
      raise ValueError(
          'Missing the required parameter `name` when calling `replace_namespaced_stateful_set_status`'
      )
    # verify the required parameter 'namespace' is set
    if ('namespace' not in params) or (params['namespace'] is None):
      raise ValueError(
          'Missing the required parameter `namespace` when calling `replace_namespaced_stateful_set_status`'
      )
    # verify the required parameter 'body' is set
    if ('body' not in params) or (params['body'] is None):
      raise ValueError(
          'Missing the required parameter `body` when calling `replace_namespaced_stateful_set_status`'
      )

    collection_formats = {}

    path_params = {}
    if 'name' in params:
      path_params['name'] = params['name']
    if 'namespace' in params:
      path_params['namespace'] = params['namespace']

    query_params = []
    if 'pretty' in params:
      query_params.append(('pretty', params['pretty']))
    if 'dry_run' in params:
      query_params.append(('dryRun', params['dry_run']))
    if 'field_manager' in params:
      query_params.append(('fieldManager', params['field_manager']))

    header_params = {}

    form_params = []
    local_var_files = {}

    body_params = None
    if 'body' in params:
      body_params = params['body']
    # HTTP header `Accept`
    header_params['Accept'] = self.api_client.\
        select_header_accept(['application/json', 'application/yaml', 'application/vnd.kubernetes.protobuf'])

    # HTTP header `Content-Type`
    header_params['Content-Type'] = self.api_client.\
        select_header_content_type(['*/*'])

    # Authentication setting
    auth_settings = ['BearerToken']

    return self.api_client.call_api(
        '/apis/apps/v1beta2/namespaces/{namespace}/statefulsets/{name}/status',
        'PUT',
        path_params,
        query_params,
        header_params,
        body=body_params,
        post_params=form_params,
        files=local_var_files,
        response_type='V1beta2StatefulSet',
        auth_settings=auth_settings,
        async_req=params.get('async_req'),
        _return_http_data_only=params.get('_return_http_data_only'),
        _preload_content=params.get('_preload_content', True),
        _request_timeout=params.get('_request_timeout'),
        collection_formats=collection_formats)
