# coding: utf-8
"""
    Kubernetes

    No description provided (generated by Swagger Codegen
    https://github.com/swagger-api/swagger-codegen)

    OpenAPI spec version: v1.14.4

    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""

from pprint import pformat
from six import iteritems
import re


class V1beta1APIServiceStatus(object):
  """
    NOTE: This class is auto generated by the swagger code generator program.
    Do not edit the class manually.
    """
  """
    Attributes:
      swagger_types (dict): The key is attribute name and the value is attribute
        type.
      attribute_map (dict): The key is attribute name and the value is json key
        in definition.
  """
  swagger_types = {'conditions': 'list[V1beta1APIServiceCondition]'}

  attribute_map = {'conditions': 'conditions'}

  def __init__(self, conditions=None):
    """
        V1beta1APIServiceStatus - a model defined in Swagger
        """

    self._conditions = None
    self.discriminator = None

    if conditions is not None:
      self.conditions = conditions

  @property
  def conditions(self):
    """
        Gets the conditions of this V1beta1APIServiceStatus.
        Current service state of apiService.

        :return: The conditions of this V1beta1APIServiceStatus.
        :rtype: list[V1beta1APIServiceCondition]
        """
    return self._conditions

  @conditions.setter
  def conditions(self, conditions):
    """
        Sets the conditions of this V1beta1APIServiceStatus.
        Current service state of apiService.

        :param conditions: The conditions of this V1beta1APIServiceStatus.
        :type: list[V1beta1APIServiceCondition]
        """

    self._conditions = conditions

  def to_dict(self):
    """
        Returns the model properties as a dict
        """
    result = {}

    for attr, _ in iteritems(self.swagger_types):
      value = getattr(self, attr)
      if isinstance(value, list):
        result[attr] = list(
            map(lambda x: x.to_dict() if hasattr(x, 'to_dict') else x, value))
      elif hasattr(value, 'to_dict'):
        result[attr] = value.to_dict()
      elif isinstance(value, dict):
        result[attr] = dict(
            map(
                lambda item: (item[0], item[1].to_dict())
                if hasattr(item[1], 'to_dict') else item, value.items()))
      else:
        result[attr] = value

    return result

  def to_str(self):
    """
        Returns the string representation of the model
        """
    return pformat(self.to_dict())

  def __repr__(self):
    """
        For `print` and `pprint`
        """
    return self.to_str()

  def __eq__(self, other):
    """
        Returns true if both objects are equal
        """
    if not isinstance(other, V1beta1APIServiceStatus):
      return False

    return self.__dict__ == other.__dict__

  def __ne__(self, other):
    """
        Returns true if both objects are not equal
        """
    return not self == other
