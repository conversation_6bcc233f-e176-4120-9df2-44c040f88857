# coding: utf-8
"""
    Kubernetes

    No description provided (generated by Swagger Codegen
    https://github.com/swagger-api/swagger-codegen)

    OpenAPI spec version: v1.14.4

    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""

from pprint import pformat
from six import iteritems
import re


class V1beta1CustomResourceConversion(object):
  """
    NOTE: This class is auto generated by the swagger code generator program.
    Do not edit the class manually.
    """
  """
    Attributes:
      swagger_types (dict): The key is attribute name and the value is attribute
        type.
      attribute_map (dict): The key is attribute name and the value is json key
        in definition.
  """
  swagger_types = {
      'conversion_review_versions': 'list[str]',
      'strategy': 'str',
      'webhook_client_config': 'ApiextensionsV1beta1WebhookClientConfig'
  }

  attribute_map = {
      'conversion_review_versions': 'conversionReviewVersions',
      'strategy': 'strategy',
      'webhook_client_config': 'webhookClientConfig'
  }

  def __init__(self,
               conversion_review_versions=None,
               strategy=None,
               webhook_client_config=None):
    """
        V1beta1CustomResourceConversion - a model defined in Swagger
        """

    self._conversion_review_versions = None
    self._strategy = None
    self._webhook_client_config = None
    self.discriminator = None

    if conversion_review_versions is not None:
      self.conversion_review_versions = conversion_review_versions
    self.strategy = strategy
    if webhook_client_config is not None:
      self.webhook_client_config = webhook_client_config

  @property
  def conversion_review_versions(self):
    """
        Gets the conversion_review_versions of this
        V1beta1CustomResourceConversion.
        ConversionReviewVersions is an ordered list of preferred
        `ConversionReview` versions the Webhook expects. API server will try to
        use first version in the list which it supports. If none of the versions
        specified in this list supported by API server, conversion will fail for
        this object. If a persisted Webhook configuration specifies allowed
        versions and does not include any versions known to the API Server,
        calls to the webhook will fail. Default to `['v1beta1']`.

        :return: The conversion_review_versions of this
        V1beta1CustomResourceConversion.
        :rtype: list[str]
        """
    return self._conversion_review_versions

  @conversion_review_versions.setter
  def conversion_review_versions(self, conversion_review_versions):
    """
        Sets the conversion_review_versions of this
        V1beta1CustomResourceConversion.
        ConversionReviewVersions is an ordered list of preferred
        `ConversionReview` versions the Webhook expects. API server will try to
        use first version in the list which it supports. If none of the versions
        specified in this list supported by API server, conversion will fail for
        this object. If a persisted Webhook configuration specifies allowed
        versions and does not include any versions known to the API Server,
        calls to the webhook will fail. Default to `['v1beta1']`.

        :param conversion_review_versions: The conversion_review_versions of
        this V1beta1CustomResourceConversion.
        :type: list[str]
        """

    self._conversion_review_versions = conversion_review_versions

  @property
  def strategy(self):
    """
        Gets the strategy of this V1beta1CustomResourceConversion.
        `strategy` specifies the conversion strategy. Allowed values are: -
        `None`: The converter only change the apiVersion and would not touch any
        other field in the CR. - `Webhook`: API Server will call to an external
        webhook to do the conversion. Additional information is needed for this
        option.

        :return: The strategy of this V1beta1CustomResourceConversion.
        :rtype: str
        """
    return self._strategy

  @strategy.setter
  def strategy(self, strategy):
    """
        Sets the strategy of this V1beta1CustomResourceConversion.
        `strategy` specifies the conversion strategy. Allowed values are: -
        `None`: The converter only change the apiVersion and would not touch any
        other field in the CR. - `Webhook`: API Server will call to an external
        webhook to do the conversion. Additional information is needed for this
        option.

        :param strategy: The strategy of this V1beta1CustomResourceConversion.
        :type: str
        """
    if strategy is None:
      raise ValueError('Invalid value for `strategy`, must not be `None`')

    self._strategy = strategy

  @property
  def webhook_client_config(self):
    """
        Gets the webhook_client_config of this V1beta1CustomResourceConversion.
        `webhookClientConfig` is the instructions for how to call the webhook if
        strategy is `Webhook`. This field is alpha-level and is only honored by
        servers that enable the CustomResourceWebhookConversion feature.

        :return: The webhook_client_config of this
        V1beta1CustomResourceConversion.
        :rtype: ApiextensionsV1beta1WebhookClientConfig
        """
    return self._webhook_client_config

  @webhook_client_config.setter
  def webhook_client_config(self, webhook_client_config):
    """
        Sets the webhook_client_config of this V1beta1CustomResourceConversion.
        `webhookClientConfig` is the instructions for how to call the webhook if
        strategy is `Webhook`. This field is alpha-level and is only honored by
        servers that enable the CustomResourceWebhookConversion feature.

        :param webhook_client_config: The webhook_client_config of this
        V1beta1CustomResourceConversion.
        :type: ApiextensionsV1beta1WebhookClientConfig
        """

    self._webhook_client_config = webhook_client_config

  def to_dict(self):
    """
        Returns the model properties as a dict
        """
    result = {}

    for attr, _ in iteritems(self.swagger_types):
      value = getattr(self, attr)
      if isinstance(value, list):
        result[attr] = list(
            map(lambda x: x.to_dict() if hasattr(x, 'to_dict') else x, value))
      elif hasattr(value, 'to_dict'):
        result[attr] = value.to_dict()
      elif isinstance(value, dict):
        result[attr] = dict(
            map(
                lambda item: (item[0], item[1].to_dict())
                if hasattr(item[1], 'to_dict') else item, value.items()))
      else:
        result[attr] = value

    return result

  def to_str(self):
    """
        Returns the string representation of the model
        """
    return pformat(self.to_dict())

  def __repr__(self):
    """
        For `print` and `pprint`
        """
    return self.to_str()

  def __eq__(self, other):
    """
        Returns true if both objects are equal
        """
    if not isinstance(other, V1beta1CustomResourceConversion):
      return False

    return self.__dict__ == other.__dict__

  def __ne__(self, other):
    """
        Returns true if both objects are not equal
        """
    return not self == other
