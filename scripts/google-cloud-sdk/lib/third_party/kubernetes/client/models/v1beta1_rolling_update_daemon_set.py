# coding: utf-8
"""
    Kubernetes

    No description provided (generated by Swagger Codegen
    https://github.com/swagger-api/swagger-codegen)

    OpenAPI spec version: v1.14.4

    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""

from pprint import pformat
from six import iteritems
import re


class V1beta1RollingUpdateDaemonSet(object):
  """
    NOTE: This class is auto generated by the swagger code generator program.
    Do not edit the class manually.
    """
  """
    Attributes:
      swagger_types (dict): The key is attribute name and the value is attribute
        type.
      attribute_map (dict): The key is attribute name and the value is json key
        in definition.
  """
  swagger_types = {'max_unavailable': 'object'}

  attribute_map = {'max_unavailable': 'maxUnavailable'}

  def __init__(self, max_unavailable=None):
    """
        V1beta1RollingUpdateDaemonSet - a model defined in Swagger
        """

    self._max_unavailable = None
    self.discriminator = None

    if max_unavailable is not None:
      self.max_unavailable = max_unavailable

  @property
  def max_unavailable(self):
    """
        Gets the max_unavailable of this V1beta1RollingUpdateDaemonSet.
        The maximum number of DaemonSet pods that can be unavailable during the
        update. Value can be an absolute number (ex: 5) or a percentage of total
        number of DaemonSet pods at the start of the update (ex: 10%). Absolute
        number is calculated from percentage by rounding up. This cannot be 0.
        Default value is 1. Example: when this is set to 30%, at most 30% of the
        total number of nodes that should be running the daemon pod (i.e.
        status.desiredNumberScheduled) can have their pods stopped for an update
        at any given time. The update starts by stopping at most 30% of those
        DaemonSet pods and then brings up new DaemonSet pods in their place.
        Once the new pods are available, it then proceeds onto other DaemonSet
        pods, thus ensuring that at least 70% of original number of DaemonSet
        pods are available at all times during the update.

        :return: The max_unavailable of this V1beta1RollingUpdateDaemonSet.
        :rtype: object
        """
    return self._max_unavailable

  @max_unavailable.setter
  def max_unavailable(self, max_unavailable):
    """
        Sets the max_unavailable of this V1beta1RollingUpdateDaemonSet.
        The maximum number of DaemonSet pods that can be unavailable during the
        update. Value can be an absolute number (ex: 5) or a percentage of total
        number of DaemonSet pods at the start of the update (ex: 10%). Absolute
        number is calculated from percentage by rounding up. This cannot be 0.
        Default value is 1. Example: when this is set to 30%, at most 30% of the
        total number of nodes that should be running the daemon pod (i.e.
        status.desiredNumberScheduled) can have their pods stopped for an update
        at any given time. The update starts by stopping at most 30% of those
        DaemonSet pods and then brings up new DaemonSet pods in their place.
        Once the new pods are available, it then proceeds onto other DaemonSet
        pods, thus ensuring that at least 70% of original number of DaemonSet
        pods are available at all times during the update.

        :param max_unavailable: The max_unavailable of this
        V1beta1RollingUpdateDaemonSet.
        :type: object
        """

    self._max_unavailable = max_unavailable

  def to_dict(self):
    """
        Returns the model properties as a dict
        """
    result = {}

    for attr, _ in iteritems(self.swagger_types):
      value = getattr(self, attr)
      if isinstance(value, list):
        result[attr] = list(
            map(lambda x: x.to_dict() if hasattr(x, 'to_dict') else x, value))
      elif hasattr(value, 'to_dict'):
        result[attr] = value.to_dict()
      elif isinstance(value, dict):
        result[attr] = dict(
            map(
                lambda item: (item[0], item[1].to_dict())
                if hasattr(item[1], 'to_dict') else item, value.items()))
      else:
        result[attr] = value

    return result

  def to_str(self):
    """
        Returns the string representation of the model
        """
    return pformat(self.to_dict())

  def __repr__(self):
    """
        For `print` and `pprint`
        """
    return self.to_str()

  def __eq__(self, other):
    """
        Returns true if both objects are equal
        """
    if not isinstance(other, V1beta1RollingUpdateDaemonSet):
      return False

    return self.__dict__ == other.__dict__

  def __ne__(self, other):
    """
        Returns true if both objects are not equal
        """
    return not self == other
