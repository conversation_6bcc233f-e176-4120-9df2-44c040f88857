# coding: utf-8
"""
    Kubernetes

    No description provided (generated by Swagger Codegen
    https://github.com/swagger-api/swagger-codegen)

    OpenAPI spec version: v1.14.4

    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""

from pprint import pformat
from six import iteritems
import re


class V1SecretReference(object):
  """
    NOTE: This class is auto generated by the swagger code generator program.
    Do not edit the class manually.
    """
  """
    Attributes:
      swagger_types (dict): The key is attribute name and the value is attribute
        type.
      attribute_map (dict): The key is attribute name and the value is json key
        in definition.
  """
  swagger_types = {'name': 'str', 'namespace': 'str'}

  attribute_map = {'name': 'name', 'namespace': 'namespace'}

  def __init__(self, name=None, namespace=None):
    """
        V1SecretReference - a model defined in Swagger
        """

    self._name = None
    self._namespace = None
    self.discriminator = None

    if name is not None:
      self.name = name
    if namespace is not None:
      self.namespace = namespace

  @property
  def name(self):
    """
        Gets the name of this V1SecretReference.
        Name is unique within a namespace to reference a secret resource.

        :return: The name of this V1SecretReference.
        :rtype: str
        """
    return self._name

  @name.setter
  def name(self, name):
    """
        Sets the name of this V1SecretReference.
        Name is unique within a namespace to reference a secret resource.

        :param name: The name of this V1SecretReference.
        :type: str
        """

    self._name = name

  @property
  def namespace(self):
    """
        Gets the namespace of this V1SecretReference.
        Namespace defines the space within which the secret name must be unique.

        :return: The namespace of this V1SecretReference.
        :rtype: str
        """
    return self._namespace

  @namespace.setter
  def namespace(self, namespace):
    """
        Sets the namespace of this V1SecretReference.
        Namespace defines the space within which the secret name must be unique.

        :param namespace: The namespace of this V1SecretReference.
        :type: str
        """

    self._namespace = namespace

  def to_dict(self):
    """
        Returns the model properties as a dict
        """
    result = {}

    for attr, _ in iteritems(self.swagger_types):
      value = getattr(self, attr)
      if isinstance(value, list):
        result[attr] = list(
            map(lambda x: x.to_dict() if hasattr(x, 'to_dict') else x, value))
      elif hasattr(value, 'to_dict'):
        result[attr] = value.to_dict()
      elif isinstance(value, dict):
        result[attr] = dict(
            map(
                lambda item: (item[0], item[1].to_dict())
                if hasattr(item[1], 'to_dict') else item, value.items()))
      else:
        result[attr] = value

    return result

  def to_str(self):
    """
        Returns the string representation of the model
        """
    return pformat(self.to_dict())

  def __repr__(self):
    """
        For `print` and `pprint`
        """
    return self.to_str()

  def __eq__(self, other):
    """
        Returns true if both objects are equal
        """
    if not isinstance(other, V1SecretReference):
      return False

    return self.__dict__ == other.__dict__

  def __ne__(self, other):
    """
        Returns true if both objects are not equal
        """
    return not self == other
