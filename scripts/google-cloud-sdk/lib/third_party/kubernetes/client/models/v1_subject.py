# coding: utf-8
"""
    Kubernetes

    No description provided (generated by Swagger Codegen
    https://github.com/swagger-api/swagger-codegen)

    OpenAPI spec version: v1.14.4

    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""

from pprint import pformat
from six import iteritems
import re


class V1Subject(object):
  """
    NOTE: This class is auto generated by the swagger code generator program.
    Do not edit the class manually.
    """
  """
    Attributes:
      swagger_types (dict): The key is attribute name and the value is attribute
        type.
      attribute_map (dict): The key is attribute name and the value is json key
        in definition.
  """
  swagger_types = {
      'api_group': 'str',
      'kind': 'str',
      'name': 'str',
      'namespace': 'str'
  }

  attribute_map = {
      'api_group': 'apiGroup',
      'kind': 'kind',
      'name': 'name',
      'namespace': 'namespace'
  }

  def __init__(self, api_group=None, kind=None, name=None, namespace=None):
    """
        V1Subject - a model defined in Swagger
        """

    self._api_group = None
    self._kind = None
    self._name = None
    self._namespace = None
    self.discriminator = None

    if api_group is not None:
      self.api_group = api_group
    self.kind = kind
    self.name = name
    if namespace is not None:
      self.namespace = namespace

  @property
  def api_group(self):
    """
        Gets the api_group of this V1Subject.
        APIGroup holds the API group of the referenced subject. Defaults to \"\"
        for ServiceAccount subjects. Defaults to \"rbac.authorization.k8s.io\"
        for User and Group subjects.

        :return: The api_group of this V1Subject.
        :rtype: str
        """
    return self._api_group

  @api_group.setter
  def api_group(self, api_group):
    """
        Sets the api_group of this V1Subject.
        APIGroup holds the API group of the referenced subject. Defaults to \"\"
        for ServiceAccount subjects. Defaults to \"rbac.authorization.k8s.io\"
        for User and Group subjects.

        :param api_group: The api_group of this V1Subject.
        :type: str
        """

    self._api_group = api_group

  @property
  def kind(self):
    """
        Gets the kind of this V1Subject.
        Kind of object being referenced. Values defined by this API group are
        \"User\", \"Group\", and \"ServiceAccount\". If the Authorizer does not
        recognized the kind value, the Authorizer should report an error.

        :return: The kind of this V1Subject.
        :rtype: str
        """
    return self._kind

  @kind.setter
  def kind(self, kind):
    """
        Sets the kind of this V1Subject.
        Kind of object being referenced. Values defined by this API group are
        \"User\", \"Group\", and \"ServiceAccount\". If the Authorizer does not
        recognized the kind value, the Authorizer should report an error.

        :param kind: The kind of this V1Subject.
        :type: str
        """
    if kind is None:
      raise ValueError('Invalid value for `kind`, must not be `None`')

    self._kind = kind

  @property
  def name(self):
    """
        Gets the name of this V1Subject.
        Name of the object being referenced.

        :return: The name of this V1Subject.
        :rtype: str
        """
    return self._name

  @name.setter
  def name(self, name):
    """
        Sets the name of this V1Subject.
        Name of the object being referenced.

        :param name: The name of this V1Subject.
        :type: str
        """
    if name is None:
      raise ValueError('Invalid value for `name`, must not be `None`')

    self._name = name

  @property
  def namespace(self):
    """
        Gets the namespace of this V1Subject.
        Namespace of the referenced object.  If the object kind is
        non-namespace, such as \"User\" or \"Group\", and this value is not
        empty the Authorizer should report an error.

        :return: The namespace of this V1Subject.
        :rtype: str
        """
    return self._namespace

  @namespace.setter
  def namespace(self, namespace):
    """
        Sets the namespace of this V1Subject.
        Namespace of the referenced object.  If the object kind is
        non-namespace, such as \"User\" or \"Group\", and this value is not
        empty the Authorizer should report an error.

        :param namespace: The namespace of this V1Subject.
        :type: str
        """

    self._namespace = namespace

  def to_dict(self):
    """
        Returns the model properties as a dict
        """
    result = {}

    for attr, _ in iteritems(self.swagger_types):
      value = getattr(self, attr)
      if isinstance(value, list):
        result[attr] = list(
            map(lambda x: x.to_dict() if hasattr(x, 'to_dict') else x, value))
      elif hasattr(value, 'to_dict'):
        result[attr] = value.to_dict()
      elif isinstance(value, dict):
        result[attr] = dict(
            map(
                lambda item: (item[0], item[1].to_dict())
                if hasattr(item[1], 'to_dict') else item, value.items()))
      else:
        result[attr] = value

    return result

  def to_str(self):
    """
        Returns the string representation of the model
        """
    return pformat(self.to_dict())

  def __repr__(self):
    """
        For `print` and `pprint`
        """
    return self.to_str()

  def __eq__(self, other):
    """
        Returns true if both objects are equal
        """
    if not isinstance(other, V1Subject):
      return False

    return self.__dict__ == other.__dict__

  def __ne__(self, other):
    """
        Returns true if both objects are not equal
        """
    return not self == other
