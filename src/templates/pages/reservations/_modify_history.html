<div id="tab-6" class="board tab-content">
    <div class="input_wrapper col3">
        <table id="history_table" class="zebra_table">
            {% if is_spa and history_by_amsystem %}
                <thead>
                    <tr>
                        <th>{{ T_DATE }}</th>
                        <th>Status</th>
                        <th>Identificador</th>
                    </tr>
                </thead>
                <tbody>
                        {% for row in history %}
                            <tr>
                                <td>{{ row.date }}</td>
                                <td>{{ row.status }}</td>
                                <td>{{ row.identifier_spa }}</td>
                            </tr>
                        {% endfor %}
                </tbody>
            {% else %}
                <thead>
                    <tr>
                        <th>{{ T_DATE }}</th>
                        <th>{{ T_usuario }}</th>
                        <th>{{ T_HISTORY_CHANGE }}</th>
                    </tr>
                </thead>
                <tbody>
                        {% if reservation.cancellationTimestamp %}
                            {% if not history or (history|length > 1 and not 'cancelled' in history[-1].change and reservation.cancelled) %}
                                <tr>
                                    <td>{{ reservation.cancellationTimestamp }}</td>
                                    <td>{{ extra_info.cancelled_by }}</td>
                                    <td>Reservation cancelled<br>{{ reservation.startDate }} - {{ reservation.endDate }}</td>
                                </tr>
                            {% endif %}
                        {% endif %}
                        {% for row in history|reverse %}
                            <tr>
                                <td>{{ row.timestamp }}</td>
                                <td>{{ row.username }}</td>
                                <td>
                                    {{ row.change | safe}} {% if row.deleted_room_information %} <a href="#" onclick="recovery_deleted_room('{{ row.deleted_room_information.id_rule }}')" style="color:red">{{ T_RECOVERY_ROOM }} <i class="fa fa-repeat" style="color:red"></i></a>{% endif %}

                                    {% if row.deleted_room_information %}
                                        <input type="hidden" id="index_room_{{ row.deleted_room_information.id_rule }}" value="{{ row.deleted_room_information.index_room }}">
                                        <input type="hidden" id="room_key_{{ row.deleted_room_information.id_rule }}" value="{{ row.deleted_room_information.room_key }}">
                                        <input type="hidden" id="rate_key_{{ row.deleted_room_information.id_rule }}" value="{{ row.deleted_room_information.rate_key }}">
                                        <input type="hidden" id="regimen_key_{{ row.deleted_room_information.id_rule }}" value="{{ row.deleted_room_information.regimen_key }}">
                                        <input type="hidden" id="occupancy_{{ row.deleted_room_information.id_rule }}" value="{{ row.deleted_room_information.occupancy }}">
                                        <input type="hidden" id="price_{{ row.deleted_room_information.id_rule }}" value="{{ row.deleted_room_information.price }}">
                                    {% endif %}

                                </td>
                            </tr>
                        {% endfor %}
                            <tr>
                                <td>{{ reservation.timestamp }}</td>
                                <td>{% if reservation.agent %}
                                    Callcenter({{ reservation.agent }})
                                {% else %}
                                    Web
                                {% endif %}
                                </td>

                                <td>Reservation created
                                <br>
                                {{ reservation.startDate }} - {{ reservation.endDate }}</td>
                            </tr>
                </tbody>
            {% endif %}
        </table>
    </div>
</div>
