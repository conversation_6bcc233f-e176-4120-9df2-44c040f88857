<!-- Services 2.0 -->

{% macro build_injector_service2(service, services2, index, service_type) -%}

    <div class="wrapper-services wrapper-injector">
        <input type="hidden" name="num_{{ service_type }}" value="{{ service_associated | length }}" id="num_{{ service_type }}">
        <a class="new_supplement button-activate-add btn btn_small "  onclick='$(".panel-{{ service_type }}").slideToggle("fast");'>{{ T_RESERVATIONS_SERVICES_CREATE }} <i class="far fa-chevron-down"></i></a>
        <div class="panel-{{ service_type }} new_supplement" style="display:none;">
            <div class="entry-item" style="flex-wrap: wrap">
                <div class="input_wrapper col3"><label>{{T_RESERVATIONS_SERVICES_DESCRIPTION}}</label>
                     <div class="input select">
                         {{ build_select_services_injector2(services2, service_type) }}
                     </div>
                </div>
                 {{ build_properties_service2_injector2(service_type) }}
            </div>
        </div>
    </div>

{%- endmacro -%}

{% macro build_select_services2(services2, selected_service, index, service_type) -%}
    {% set selected_service_keys = [] %}
    <select class="gwt-ListBox {{ service_type }}-key" {% if selected_service %}name="{{ service_type }}[{{ index }}][key]"{% endif %} disabled="disabled">
        {% if not service %}<option value="" selected>{{T_RESERVATIONS_COMMONS_SERVICE|safe}}</option>{% endif %}
        <option value="{{ selected_service.key }}" selected>{% if selected_service.manager_name %}{{ selected_service.manager_name }}{% else %}{{ selected_service.name }}{% endif %}</option>
        {% if selected_service.service_key not in selected_service_keys %}
            <option value="{{ selected_service.service_key }}" selected required_inputs="{% if service2_inner %}{{ service2_inner.required_inputs }}{% endif %}">{% if selected_service.manager_name %}{{ selected_service.manager_name }}{% else %}{{ selected_service.name }}{% endif %}</option>
        {% endif %}
    </select>
{%- endmacro -%}

{% macro build_transfer_fields(service2, service_type, index) -%}
    {% if service2.extra_fields %}
        {% for type, transfer in service2.extra_fields.items() %}
            <br>

            <label style="width: 100%; color: #446ca9">{% if type == 'to' and transfer.transfer_flight_number %}{{ T_OUTWARD }}{% elif type == 'from' and transfer.transfer_flight_number %}{{ T_RETURN }}{% endif %}</label>
            <div class="input_wrapper col3" {% if service2.extra_fields %}{% if transfer.transfer_flight_number %}{% else %}style="display: none" {% endif %}{% else %}style="display:none"{% endif %}><label>{{ T_TRANSFER_FLIGHT_NUMBER }}</label>
                 <div class="input">
                     <input type="text" value="{% if service2.extra_fields %}{% if transfer.transfer_flight_number %}{{transfer.transfer_flight_number}}{% endif %}{% endif %}" class="{{ service_type }}-transfer_flight_number-{{ type }}" name="{{ service_type }}[{{ index }}][transfer_flight_number-{{ type }}]" style="width: auto">
                 </div>
            </div>
            <div class="input_wrapper col3" {% if service2.extra_fields %}{% if transfer.transfer_airline %}{% else %}style="display: none" {% endif %}{% else %}style="display:none"{% endif %}><label>{{ T_TRANSFER_AIRLINE }}</label>
                 <div class="input">
                     <input type="text" value="{% if service2.extra_fields %}{% if transfer.transfer_airline %}{{transfer.transfer_airline}}{% endif %}{% endif %}" class="{{ service_type }}-transfer_airline-{{ type }}" name="{{ service_type }}[{{ index }}][transfer_airline-{{ type }}]" style="width: auto">
                 </div>
            </div>
            <div class="input_wrapper col3 " {% if service2.extra_fields %}{% if transfer.transfer_landing_time %}{% else %}style="display: none" {% endif %}{% else %}style="display:none"{% endif %}><label>{{ T_TRANSFER_LANDING_TIME }}</label>
                 <div class="input">
                     <input type="text" value="{% if service2.extra_fields %}{% if transfer.transfer_landing_time %}{{transfer.transfer_landing_time}}{% endif %}{% endif %}" class="{{ service_type }}-transfer_landing_time-{{ type }}" name="{{ service_type }}[{{ index }}][transfer_landing_time-{{ type }}]" style="width: auto">
                 </div>
            </div>
            {% if loop.index == 2 %}
                <div class="input_wrapper col3" {% if service2.extra_fields %}{% if transfer.transfer_comments %}{% else %}style="display: none" {% endif %}{% else %}style="display:none"{% endif %}><label>{{ T_TRANSFER_COMMENTS }}</label>
                     <div class="input">
                         <input type="text" value="{% if service2.extra_fields %}{% if transfer.transfer_comments %}{{transfer.transfer_comments}}{% endif %}{% endif %}" class="{{ service_type }}-transfer_comments" name="{{ service_type }}[{{ index }}][transfer_comments]" style="width: auto">
                     </div>
                </div>
            {% endif %}
        {% endfor %}

    {% else %}
        {% set types = ['to', 'from'] %}
        {% for type in types %}
            <br>
            <label style="width: 100%; color: #446ca9; display: none">{% if type == 'to' %}{{ T_OUTWARD }}{% else %}{{ T_RETURN }}{% endif %}</label>
            <div class="input_wrapper col3 transfer_flight_number-input-{{ type }}" {% if service2.extra_fields %}{% if transfer.transfer_flight_number %}{% else %}style="display: none" {% endif %}{% else %}style="display:none"{% endif %}><label>{{ T_TRANSFER_FLIGHT_NUMBER }}</label>
                 <div class="input">
                     <input type="text" value="{% if service2.extra_fields %}{% if transfer.transfer_flight_number %}{{transfer.transfer_flight_number}}{% endif %}{% endif %}" class="{{ service_type }}-transfer_flight_number-{{ type }}" name="{{ service_type }}[{{ index }}][transfer_flight_number-{{ type }}]" style="width: auto">
                 </div>
            </div>
            <div class="input_wrapper col3 transfer_airline-input-{{ type }}" {% if service2.extra_fields %}{% if transfer.transfer_airline %}{% else %}style="display: none" {% endif %}{% else %}style="display:none"{% endif %}><label>{{ T_TRANSFER_AIRLINE }}</label>
                 <div class="input">
                     <input type="text" value="{% if service2.extra_fields %}{% if transfer.transfer_airline %}{{transfer.transfer_airline}}{% endif %}{% endif %}" class="{{ service_type }}-transfer_airline-{{ type }}" name="{{ service_type }}[{{ index }}][transfer_airline-{{ type }}]" style="width: auto">
                 </div>
            </div>
            <div class="input_wrapper col3 transfer_landing_time-input-{{ type }}" {% if service2.extra_fields %}{% if transfer.transfer_landing_time %}{% else %}style="display: none" {% endif %}{% else %}style="display:none"{% endif %}><label>{{ T_TRANSFER_LANDING_TIME }}</label>
                 <div class="input">
                     <input type="text" value="{% if service2.extra_fields %}{% if transfer.transfer_landing_time %}{{transfer.transfer_landing_time}}{% endif %}{% endif %}" class="{{ service_type }}-transfer_landing_time-{{ type }}" name="{{ service_type }}[{{ index }}][transfer_landing_time-{{ type }}]" style="width: auto">
                 </div>
            </div>
        {% endfor %}
        <div class="input_wrapper col3 transfer_comments-input" {% if service2.extra_fields %}{% if transfer.transfer_comments %}{% else %}style="display: none" {% endif %}{% else %}style="display:none"{% endif %}><label>{{ T_TRANSFER_COMMENTS }}</label>
             <div class="input">
                 <input type="text" value="{% if service2.extra_fields %}{% if transfer.transfer_comments %}{{transfer.transfer_comments}}{% endif %}{% endif %}" class="{{ service_type }}-transfer_comments" name="{{ service_type }}[{{ index }}][transfer_comments]" style="width: auto">
             </div>
        </div>
    {% endif %}

{%- endmacro -%}

{% macro build_transfer_fields_injector(service_type, index) %}
{#    <div class="input_wrapper col3 journey-input" style="display: none"><label>{{ T_JOURNEY }}</label>#}
{#        <div class="input select">#}
{#            <select class="{{ service_type }}-journey-injector" name="{{ service_type }}[{{ index }}][journey]">#}
{#                <option value="">{{ T_SELECT_JOURNEY }}</option>#}
{#                <option value="to">{{ T_OUTWARD }}</option>#}
{#                <option value="from">{{ T_RETURN }}</option>#}
{#                <option value="round_trip">{{ T_ROUND_TRIP }}</option>#}
{#            </select>#}
{#        </div>#}
{#    </div>#}
    {% set types = ['to', 'from'] %}
    {% for type in types %}
        <label style="width: 100%; color: #446ca9; display: none" class="{{ type }}-injector input_wrapper {{ type }}-input   ">{% if type == 'to' %}{{ T_OUTWARD }}{% else %}{{ T_RETURN }}{% endif %}</label>
        <div class="input_wrapper col3 transfer_flight_number-input {{ type }}-injector" style="display: none"><label>{{ T_TRANSFER_FLIGHT_NUMBER }}</label>
            <div class="input">
                <input type="text" class="{{ service_type }}-transfer_flight_number-{{ type }}-injector">
            </div>
        </div>
        <div class="input_wrapper col3 transfer_airline-input {{ type }}-injector" style="display: none"><label>{{ T_TRANSFER_AIRLINE }}</label>
            <div class="input">
                <input type="text" class="{{ service_type }}-transfer_airline-{{ type }}-injector">
            </div>
        </div>
        <div class="input_wrapper col3 transfer_landing_time-input {{ type }}-injector" style="display: none"><label>{{ T_TRANSFER_LANDING_TIME }}</label>
            <div class="input">
                <input type="text" class="{{ service_type }}-transfer_landing_time-{{ type }}-injector">
            </div>
        </div>
    {% endfor %}

    <div class="input_wrapper col3 transfer_comments-input -injector" style="display: none"><label>{{ T_TRANSFER_COMMENTS }}</label>
        <div class="input">
            <input type="text" class="{{ service_type }}-transfer_comments-injector">
        </div>
    </div>

{%- endmacro -%}
{% macro build_properties_service2(service2, index, services2, service_type) -%}

{#    Esto construye el parametros del servicio que ya existe #}

{#    <div class="input_wrapper col3" {% if not service2.amount %}style="display: none"{% endif %}><label>{{T_RESERVATIONS_SERVICES_QUANTITY}}</label>#}
{#    <div class="input_wrapper col3" style="display: none"><label>{{T_RESERVATIONS_SERVICES_QUANTITY}}</label>#}
{#         <div class="input">#}
{#             <input type="text" class="{{ service_type }}-quantity" value="{{service2.amount}}" {% if service2 %}name="{{ service_type }}[{{ index }}][quantity]"{% endif %}/>#}
{#         </div>#}
{#    </div>#}
    <div class="input_wrapper col3"><label>{{T_RESERVATIONS_SERVICES_DESCRIPTION}}</label>
         <div class="input select">
             {{ build_select_services2(services2, service2, index, service_type) }}
         </div>
    </div>

    <div class="input_wrapper col3" style="display: none"><label>{{T_NAME}}</label>
         <div class="input">
             <input type="text" class="{{ service_type }}-service_key" value="{{service2.service_key}}" {% if service2 %}name="{{ service_type }}[{{ index }}][service_key]"{% endif %}/>
         </div>
    </div>


    <div class="input_wrapper col3" {% if not service2.customer_name %}style="display: none"{% endif %}><label>{{T_NAME}}</label>
         <div class="input">
             <input type="text" class="{{ service_type }}-customer_name" value="{{service2.customer_name}}" {% if service2 %}name="{{ service_type }}[{{ index }}][customer_name]"{% endif %}/>
         </div>
    </div>

    <div class="input_wrapper col3" {% if not service2.guest_type %}style="display: none"{% endif %}><label>{{ T_CUSTOMER_TYPE }}</label>
         <div class="input select">
             <select name="{{ service_type }}[{{ index }}][guest_type]" class="{{ service_type }}-guest_type">
                 <option value="adult" {% if service2.guest_type == 'adult' %}selected{% endif %}>{{ T_ADULT }}</option>
                 <option value="child"{% if service2.guest_type == 'child' %}selected{% endif %}>{{ T_KID }}</option>
                 <option value="baby">{% if service2.guest_type == 'baby' %}selected{% endif %}{{ T_BABY }}</option>
             </select>
         </div>
{#        <input type="text" class="{{ service_type }}-guest_type" value="{{service2.guest_type}}" {% if service2 %}name="{{ service_type }}[{{ index }}][guest_type]"{% endif %}/>#}
    </div>

    <div class="input_wrapper col3" style="display: none"><label>{{T_RESERVATIONS_SERVICES_DAYS}}</label>
         <div class="input">
            <input type="text" class="{{ service_type }}-days" value="{{service2.days}}" {% if service2 %}name="{{ service_type }}[{{ index }}][days]"{% endif %}/>
         </div>
    </div>

    <div class="input_wrapper col3" {% if not service2.hour %}style="display: none"{% endif %}><label>{{ T_HOUR }}</label>
        <div class="input select">
            <select class="{{ service_type }}-hour" name="{{ service_type }}[{{ index }}][hour]">
                {% for service in services2 %}
                    {% if service.key == service2.service_key %}
                        {% for hour in service.byHours %}
                            <option value="{{ hour }}" {% if service2.hour == hour %}selected{% endif %}>
                                {{ hour|safe }}
                            </option>
                        {% endfor %}
                    {% endif %}
                {% endfor %}
            </select>
        </div>
    </div>

    <div class="input_wrapper col3" {% if not service2.date %}style="display: none"{% endif %}><label>{{T_DATE}}</label>
        <div class="input">
            {% if service2.entrance_data %}
                <select class="{{ service_type }}-date entrance" name="{{ service_type }}[{{ index }}][date]">
                    {% for date in service2.entrance_data.keys() %}
                            <option entrance_data_json="{{ service2.entrance_data_json }}" value="{{ date }}" {% if date == service2.entrance_date %}selected{% endif %}>{{ date }}</option>
                    {% endfor %}
                </select>
            {% else %}
                <select class="{{ service_type }}-date" name="{{ service_type }}[{{ index }}][date]">
                    <option  value="{{ service2.date }}" selected>{{ service2.date }}</option>
                </select>
            {% endif %}
        </div>
    </div>
    <!-- quizas poner un control de que existe el valor del service2.selected_entrance.. -->
    <div class="input_wrapper col3"><label>{{ T_TICKET }}</label>
        <div class="input">
            <select class="{{ service_type }}-ticket_id" name="{{ service_type }}[{{ index }}][ticket_id]">
                <option value="{% if service2.selected_entrance and service2.selected_entrance.ticket_id %}{{service2.selected_entrance.ticket_id}}{% endif %}" selected>{% if service2.selected_entrance and service2.selected_entrance.ticket_name %}{{service2.selected_entrance.ticket_name}}{% endif %}</option>
            </select>
        </div>
        <input value="{% if service2.selected_entrance and service2.selected_entrance.ticket_name %}{{ service2.selected_entrance.ticket_name }}{% endif %}" type="hidden" class="{{ service_type }}-ticket_name" name="{{ service_type }}[{{ index }}][ticket_name]">
    </div>

    <div class="input_wrapper col3" {% if not service2.amount %}style="display: none"{% endif %}><label>{{ T_RESERVATIONS_SERVICES_QUANTITY }}</label>
        <div class="input">
            <input type="text" class="{{ service_type }}-amount" value="{{service2.amount}}" {% if service2 %}name="{{ service_type }}[{{ index }}][amount]"{% endif %}/>
         </div>
    </div>

{{ build_transfer_fields(service2, service_type, index) }}
    <div class="input_wrapper col3 service-pvp"><label>{{T_RESERVATIONS_SERVICES_TOTAL}}</label>
        <div class="input">
            <input type="text" class="{{ service_type }}-pvp service-pvp" {% if service2 %}name="{{ service_type }}[{{ index }}][pvp]"{% endif %} value="{{ service2.price }}">
        </div>
    </div>

    <div class="input_wrapper col3 recalculated_price_wrapper">
        <button type="button" class="btn btn_small _button_calculate_additional_service_price">{{ T_RECALCULATE }}</button>
        <div class="recalculated_message" style="display: none">price_status</div>
    </div>

    <input type="hidden" class="{{ service_type }}-name" {% if service2 %}name="{{ service_type }}[{{ index }}][name]"{% endif %} value="{{ service2.name }}">

{%- endmacro -%}

{% macro build_entry_service2(service2, services2, index, service_type) -%}

    <div class="wrapper-services additional_service2" id="{{ service_type }}-entry-{{ index }}">
        <div class="counter-service2 counter-{{ service_type }}">
            <span class="label">{{T_RESERVATIONS_SERVICES_EXTRA}} #{{ index }}</span>
            <a class="{{ service_type }}_button_remove button-remove" data-service="{{ index }}"><i class="far fa-backspace"></i></a>
            <a class="advise-message" style="display: none">Servicio no disponible para esta combinación.</a>
        </div>

        <div class="entry-item" style="flex-wrap: wrap">
{#            <div class="input_wrapper col3"><label>{{T_RESERVATIONS_SERVICES_DESCRIPTION}}</label>#}
{#                 <div class="input select">#}
{#                     {{ build_select_services2(services2, service2, index, service_type) }}#}
{#                 </div>#}
{#            </div>#}
            {{ build_properties_service2(service2, index, services2, service_type) }}
        </div>
   </div>

{%- endmacro -%}

{% macro build_select_services_injector2(services2, service_type) -%}
    <select class="gwt-ListBox {{ service_type }}-key-injector service2-key-injector">
         <option value=""  selected>{{T_RESERVATIONS_COMMONS_SERVICE|safe}}</option>
         {% for service2_inner in services2 %}
             <option value="{{ service2_inner.key }}" required_inputs="{{ service2_inner.required_inputs }}" entrance_data="{{ service2_inner.entrance_data_json }}" full_service="{{ service2_inner.formated_byhours }}">{% if service2_inner.manager_name %}{{ service2_inner.manager_name|safe }}{% else %}{{ service2_inner.name|safe }}{% endif %}</option>
         {% endfor %}
    </select>
{%- endmacro -%}

{% macro build_properties_service2_injector2(service_type) -%}
{# Esto carga los parametros que se necesitan rellenar para el nuevo servicio #}
{# room_associated #}
{# category #}
{# date #}
{# amount #}
{# customer_name #}
{# guest_type #}
{# days #}
{# byHours #}
    <div class="input_wrapper col3 ask_customer_name-input" style="display: none"><label>{{T_NAME}}</label>
        <div class="input">
            <input type="text" class="{{ service_type }}-customer_name-injector" value=""/>
        </div>
    </div>
    <div class="input_wrapper col3 guest_type-input" style="display: none"><label>{{ T_CUSTOMER_TYPE }}</label>
         <div class="input select">
             <select class="{{ service_type }}-guest_type-injector">
                 <option value="adult">{{ T_ADULT }}</option>
                 <option value="child">{{ T_KID }}</option>
                 <option value="baby">{{ T_BABY }}</option>
             </select>
         </div>
{#        <input type="text" class="{{ service_type }}-guest_type" value="{{service2.guest_type}}" {% if service2 %}name="{{ service_type }}[{{ index }}][guest_type]"{% endif %}/>#}
    </div>

{#    <div class="input_wrapper col3 quantity-input" style="display: none"><label>{{T_RESERVATIONS_SERVICES_QUANTITY}}</label>#}
{#        <div class="input">#}
{#            <input type="text" class="{{ service_type }}-quantity-injector" value="0"/>#}
{#        </div>#}
{#    </div>#}

    <div class="input_wrapper col3 days-input" style="display: none"><label>{{T_RESERVATIONS_SERVICES_DAYS}}</label>
        <div class="input">
            <input type="text" class="{{ service_type }}-days-injector" value="0"/>
        </div>
    </div>

    <div class="input_wrapper col3 byHours-input" style="display: none"><label>{{ T_HOUR }}</label>
        <div class="input select">
            <select class="{{ service_type }}-hour-injector" name="{{ service_type }}[{{ index }}][hour]">
                <option value=""></option>
            </select>
        </div>
    </div>

    <div class="input_wrapper col3 with_date_selector-input" style="display: none"><label>{{T_DATE}}</label>
        <div class="input">
            <select class="{{ service_type }}-date-injector" name="{{ service_type }}[{{ index }}][date]">
                <option value="">
                </option>
            </select>
        </div>
    </div>

    <div class="input_wrapper col3 is_entrance-input" style="display: none"><label>{{ T_TICKET }}</label>
        <div class="input">
            <select class="{{ service_type }}-ticket_id-injector recalculate_hidden_name" name="{{ service_type }}[{{ index }}][ticket_id]" >
{#                <option value="{{service2.selected_entrance.ticket_id}}" selected>{{service2.selected_entrance.ticket_name}}  </option>#}
            </select>
        </div>
        <input value="" type="hidden" class="{{ service_type }}-ticket_name-injector" name="{{ service_type }}[{{ index }}][ticket_name]">
    </div>

    <div class="input_wrapper col3 amount-input" style="display: none"><label>{{T_RESERVATIONS_SERVICES_QUANTITY}}</label>
        <div class="input">
            <input type="text" class="{{ service_type }}-amount-injector" value="1"/>
        </div>
    </div>


    {{ build_transfer_fields_injector(service_type, index) }}


    <div class="input_wrapper col3 price-input" style="display: none"><label>{{T_RESERVATIONS_SERVICES_PRICE}}</label>
        <div class="input">
            <input type="text" class="{{ service_type }}-pvp-injector">
        </div>
    </div>

    <div class="input_wrapper col3"><label>&nbsp;</label>
         <div class="input">
             <a class="_button_calculate_additional_service_price btn btn_small" style="background: #05940b; border-color: #05940b;">{{ T_RECALCULATE }}</a>
         </div>
     </div>

     <div class="input_wrapper col3"><label>&nbsp;</label>
         <div class="input">
             <a class="{{ service_type }}_button_add btn btn_small" style="background: #05940b; border-color: #05940b;">{{T_RESERVATIONS_SERVICES_ADD}} <i class="far fa-plus"></i></a>
         </div>
     </div>

{%- endmacro -%}



<div id="tab-11" align="left" class="board tab-content" >
    <div style="text-align: center; color: #446ca9"><i class="fas fa-info-circle"></i>{{ T_INFO_SERVICES_WITH_RESTRICTIONS }}</div>
    <div class="wrapper-services-inner" style="width: 70%">
        <div class="save-notification services-notification">
            <label class="warning">{{ T_SAVE_WARNING }}</label>
        </div>

        {% if previous_services_selection_html %}
            <span class="button-activate-add btn btn_small" onclick='$("#previous_services_selection_html").slideToggle("fast");'>{{ T_SHOW_PREVIOUS_SELECTED_SERVICES }}</span>
            <div id="previous_services_selection_html" style="display: none">{{ previous_services_selection_html | safe}}</div>

        {% endif %}

        <div class="services2" style="display: flex; flex-direction: column; margin: 20px 0">
            <input type="hidden" id="previous_services_selection" value="{{ previous_services_selection }}">
        {% if reservation_services2 %}
            {% for service_type, service_associated in reservation_services2.items() %}
                <div class="{{ service_type }}-template count_{{ service_type }}" style="display:none;">
                   {{ build_entry_service2('', services2, '', service_type) }}
                </div>
                <input type="hidden" name="num_{{ service_type }}" value="{{ service_associated | length }}" id="num_{{ service_type }}">

                {% if service_type == 'service_for_all_rooms' %}
                    <span class="button-activate-add btn btn_small" style="margin-bottom: 20px" onclick='$(".{{ service_type }}").slideToggle("fast");'>{{ T_ALL_ROOMS_SERVICE }} <i class="far fa-chevron-down"></i></span>
                {% else %}
                    <span class="button-activate-add btn btn_small" style="margin-bottom: 20px" onclick='$(".{{ service_type }}").slideToggle("fast");'>{{ T_ROOM_SERVICE }} {{ loop.index - 1 }} <i class="far fa-chevron-down"></i></span>
                {% endif %}
                <div class="{{ service_type }} wrapper-services">
                {{ build_injector_service2(service, services2, '', service_type) }}
                {% for service2 in service_associated %}
                    {{ build_entry_service2(service2, services2, loop.index, service_type) }}
                {% endfor %}
                </div>
            {% endfor %}
        {% endif %}
        </div>

    </div>

</div>

<div class="service2_room_-template" style="display:none;">
    {% if as2_active %}
        <input type="hidden" id="check_service2_availability" value="True">
        <input type="hidden" id="session_language" value="{{ session_language }}">
        <input type="hidden" id="reservation_country" value="{{ reservation_country }}">
        {% if was_cancelled %}
            <input type="hidden" id="was_cancelled" value="True">
        {% endif %}
    {% endif %}
    <span class="new_service_room_title  button-activate-add btn btn_small" style="margin-bottom: 20px">{{ T_ROOM_SERVICE }}<i class="far fa-chevron-down"></i></span>
    <div class="new_service_room wrapper-services">
    <div class="new_service_room-template" style="display:none;">
       {{ build_entry_service2('', services2, '', 'new_service_room') }}
    </div>
    {{ build_injector_service2(service, services2, '', 'new_service_room') }}
    {% for service2 in service_associated %}
        {{ build_entry_service2(service2, services2, '', 'new_service_room') }}
    {% endfor %}
    </div>
    <input type="hidden" id="all_services2" value="{{ services2_json }}" name="all_services2">
</div>
