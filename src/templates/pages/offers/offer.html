<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title></title>
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
	<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/css/bootstrap-select.min.css">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
	<style type="text/css">
		body {
            font-family: Roboto, Arial Unicode MS, Arial, sans-serif !important;
            font-size: 13px;
        }

        .offer-modal .modal-content {
            border: 0;
        }

        .offer-modal .modal-body {
            padding: 0;
        }

        .modal-dialog-centered {
        	align-items: flex-start;
        }

        #exampleModal .resize-handle {
			width: 10px;
			height: 10px;
			border-style: solid;
			border-width: 0px 0px 10px 10px;
			border-color: transparent transparent  #325da7  transparent;
			position: fixed;
			right: 0;
			bottom: 0;
			cursor: nwse-resize;
		}

        .btn.btn-add {
            background-color: #325da7;
            color: #fff;
            border-radius: 0;
            height: 38px;
            margin-left: 10px;
            text-transform: uppercase;
            font-size: 13px;
            font-family: Roboto, Arial Unicode MS, Arial, sans-serif !important;
        }

        .btn.btn-add:focus {
            box-shadow: none;
        }

        .offer-modal .form-select {
            border-radius: 0;
        }

        .offer-modal .form-select:focus {
            box-shadow: none;
            border-color: #ced4da;
        }

        .btn.button-close,
        .btn.btn-delete {
            background-color: #325da7;
            color: #fff;
            border-radius: 0;
            text-transform: uppercase;
            font-size: 13px;
            font-family: Roboto, Arial Unicode MS, Arial, sans-serif !important;
            margin: 5px;
        }

        .btn.button-close:focus,
        .btn.btn-delete:focus {
            box-shadow: none;
        }

        .offer-wrapper input {
            border-radius: 0;
            border: 0;
            border-bottom: 1px solid #dddddd;
            width: 100%;
        }

        .heading-offer {
            border-radius: 0;
            border: 0;
            border-bottom: 1px solid #dddddd;
            width: 100%;
            position: relative;
            font-family: Roboto, Arial Unicode MS, Arial, sans-serif !important;
            font-size: 14px;
            font-weight: bold;
        }

        .fa-sort-down {
            position: absolute;
            left: 44px;
            bottom: 4px;

        }

        .offer-wrapper input:focus {
            box-shadow: none;
            border-color: #dddddd;
        }

        .list-group-item.list-group-item-action {
            border-radius: 0;
            display: flex;
            justify-content: space-between;
        }

        .list-group {
            margin-top: 5px;
        }

        .offer-modal {
            display: block !important;
        }

        input:focus-visible {
            outline: 0;
        }

        .bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
            width: 100%;
            border: 1px solid #ddd;
        }

        .btn.dropdown-toggle.btn-light {
            background-color: transparent;
        }

        .btn.dropdown-toggle.btn-light:focus {
            outline: 0 !important;
            box-shadow: none;
        }


        #multiSelectDropdown {
            border: 1px solid #ddd;
            width: 100%;
            background-color: transparent;
            color: #000;
            border-radius: 0;
            text-align: start;
            justify-content: space-between;
            display: flex;
            align-items: center;
            white-space: normal;
            font-size: 13px;
        }

        #multiSelectDropdown:focus {
            box-shadow: none;
            border-color: #dddddd;
        }

        .dropdown {
            width: 100%;
        }

        .dropdown-menu.show {
            display: block;
            width: 100%;
            border-radius: 0;
            padding: 10px;
            font-size: 13px;
        }

        .offer-wrapper .drag-border {
            border: 1px dashed #c1c1c1;
            padding: 5px 5px;
        }

        #offer-list .d-flex:nth-child(odd):hover {
            background-color: #eee;
        }

        #offer-list .d-flex:nth-child(even) {
            background-color: #f5f5f5
        }

        #offerHeading {
            text-align: center;
        }
	</style>
</head>
<body>

<!-- Modal -->
<div class="modal fade offer-modal show" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div id="myDiv" value="{{ data_json }}"></div>
    <div id="mydeletebutton" value="{{ delete_button }}"></div>
    <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
      	<div class="offer-wrapper">
			<div class="heading-offer" id="offerHeading">
                {{ offer_header }}
			</div>

			<div id="offer-list" ondragover="onDragOver(event)">
                {% for option in data.current_selection %}
                	{% for option1 in data.options %}
                  		{% if option in option1 %}
							<div class="d-flex justify-content-between align-items-center" draggable="true" ondragstart="onDragStart(event)">
								<span key="{{option}}"><i class="fas fa-bars me-1"></i>{{ option1[option] | safe }}</span>
								<button class="btn btn-delete" onclick="DeleteOffer(this)">{{delete_button}}</button>
							</div>
                  		{% endif %}
                	{% endfor %}
              {% endfor %}

      		</div>
      	</div>
      	<div class="d-flex mt-3">
			  <div class="dropdown">
				<button class="btn btn-secondary dropdown-toggle" type="button" id="multiSelectDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    {{ options_button }}
				</button>
				<ul class="dropdown-menu" aria-labelledby="multiSelectDropdown">
					{% for option in data.options %}
    	                {% set first_key = option|dictsort|first|first %}
        	                {% if first_key not in data.current_selection %}
								<li key="{{first_key}}">
									<div class="form-check">
									  <input class="form-check-input" type="checkbox" id="{{ loop.index }}">
									  <label class="form-check-label" for="{{ loop.index }}">
										{{ option[first_key] }}
									  </label>
									</div>
								</li>
							{% else %}
								<li key="{{first_key}}" style="display: none;">
									<div class="form-check">
									<input class="form-check-input" type="checkbox" id="{{ loop.index }}">
									<label class="form-check-label" for="{{ loop.index }}">
										{{ option[first_key] }}
									</label>
									</div>
								</li>
                	        {% endif %}
	                {% endfor %}
				</ul>
			  </div>


            <button class="btn btn-add" onclick="addButtonClicked()">{{add_button}}</button>
		</div>
			<div class="mt-3 form-check">
			<input type="checkbox" class="form-check-input" id="exampleCheck1" {% if data.except_for %}checked{% endif %}>
			<label class="form-check-label" for="exampleCheck1">{{except_for_button}}</label>
	  	</div>
	  	<div class="d-flex justify-content-end">
	  		<button type="button" class="btn button-close" data-bs-dismiss="modal">{{close_button}}</button>
	  	</div>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript" src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/js/bootstrap-select.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

<script>
	// This function is for refresh the offers in offer list section
	function RefreshOffers(dataobj, data) {
		var delete_button = $('#mydeletebutton').attr('value');
		var offerList = $('#offer-list');
		dataobj.forEach(function(element) {
			var newElement = $('<div></div>');

			var optionsMap = {};
			data.options.forEach(function(option) {
				var key = Object.keys(option)[0];
				optionsMap[key] = option[key];
			});

			function getValueByKey(key) {
				return optionsMap[key] || null;
			}
			var value = getValueByKey(element);

			if (value == null){
				return
			}

			newElement.addClass('d-flex justify-content-between align-items-center');
			newElement.attr("draggable", true)
			newElement.attr("ondragstart", "onDragStart(event)")

			newElement.html('<i class="fas fa-bars me-1"></i>')

			newElement.html('<span key="' + element + '">' + '<i class="fas fa-bars me-1"></i>'+ value + '</span><button class="btn btn-delete" onclick="DeleteOffer(this)">' + delete_button + '</button>');
			offerList.append(newElement);
			updateComponentSizeAfterDelete()
		});
	}



	// This function is for add offers to offer list section
	function addButtonClicked() {
		var selectElement = $('#multiSelectDropdown').attr('keys').split(',');
		var data = $('#myDiv').attr('value')
		const dataobj = JSON.parse(data);
		var selection_items = [];

		selectElement.forEach(function(key) {
			selection_items.push(key.trim());
			var $elements = $('li[key="' + key.trim() + '"]');
			$elements.each(function() {
				$(this).css('display', 'none');
                $(this).find("input[type='checkbox']").prop("checked", false);
			});
		});

		$('#multiSelectDropdown').attr('keys', '');
		$('#multiSelectDropdown').text('Select options');
		RefreshOffers(selection_items, dataobj);
	}



	// This function is for delete offer from the offer list section
	function DeleteOffer(button) {
		var $parentElement = $(button).parent();
		var $spanElement = $parentElement.find('span');
		var key = $spanElement.attr('key');

		var $elements = $('li[key="' + key + '"]');
		$elements.each(function() {
			$(this).css('display', 'block');
		});

		$parentElement.remove();

        updateComponentSizeAfterDelete();
	}

    function updateComponentSizeAfterDelete() {
    	$("ul.dropdown-menu.show").removeClass("show");
        var component = $(".offer-modal");
        var width = component[0].scrollWidth;
        var height = component[0].scrollHeight;
        console.log("HEIGHT "+height)
        payload = {
                id: "size",
                width: width,
                height: height
            };
        window.parent.postMessage(payload, '*');

        setTimeout(function () {
			window.parent.postMessage(payload, '*');
		}, 500);
    }


	// This is for call add offer API
	$(document).ready(function() {
		$('.btn.button-close').click(function(e) {
			e.preventDefault();
			var current_selection = [];

			var offers = document.getElementById("offer-list").querySelectorAll("div");

			Array.from(offers).forEach(function(element) {
				var spanElement = element.querySelector("span");
				current_selection.push(spanElement.getAttribute("key"));
			});
			var checkbox = document.getElementById('exampleCheck1');
			var isChecked = checkbox.checked;

			var data = {
				"current_selection": current_selection,
				"except_for": isChecked
			};

            payload = {
                id: {{ data.objectId }},
                message: JSON.stringify(data)
            };
            window.parent.postMessage(payload, '*');
		});
	});

</script>


<script>
	// This is for dropdown multiselection
	$(document).ready(function() {

        var componente = $(".offer-modal");

        var width = componente.width();
        var height = componente[0].scrollHeight;

        payload = {
                id: "size",
                width: width,
                height: height
            }
            window.parent.postMessage(payload, '*');

        setTimeout(function () {
			window.parent.postMessage(payload, '*');
		}, 500);


	$('.dropdown-menu').on('click', function(e) {
		e.stopPropagation();
	});

	$('.dropdown-menu .form-check-input').on('change', function() {
		var selectedOptions = [];
		var selectedKeys = [];

		$('.dropdown-menu .form-check-input:checked').each(function() {
			selectedOptions.push($(this).next('label').text());
			selectedKeys.push($(this).closest('li').attr('key'));

		});
		var buttonText = 'Select options';
		if (selectedOptions.length > 0) {
			buttonText = selectedOptions.join(', ');
		}
		$('.dropdown-toggle').text(buttonText);
		$('.dropdown-toggle').attr("keys", selectedKeys.join(', '));

	});
	});


    let draggedItem = null;

    function onDragStart(event) {
        console.log(event)
        draggedItem = event.target;
    }

	function onDragOver(event) {
		event.preventDefault();
		const container = event.target.closest('#offer-list');
		const closestItem = getClosestItem(event.clientY, container);

		if (closestItem !== draggedItem) {
			container.insertBefore(draggedItem, closestItem);
		}
	}

	function getClosestItem(y, container) {
		const items = container.querySelectorAll('.d-flex');
		return [...items].reduce((closest, item) => {
			const box = item.getBoundingClientRect();
			const offset = y - box.top - box.height / 2;
			if (offset < 0 && offset > closest.offset) {
				return { offset, item };
			} else {
				return closest;
			}
		}, { offset: Number.NEGATIVE_INFINITY }).item;
	}


    function onDragEnd() {
        draggedItem = null;
    }

	$(document).ready(function() {
		const $resizableDiv = $('#exampleModal');
		const $resizeHandle = $('<div class="resize-handle"></div>');

		$resizableDiv.append($resizeHandle);

		let isResizing = false;
		let initialHeight;

		$resizeHandle.mousedown(function(e) {
		  isResizing = true;
		  initialHeight = $resizableDiv.height();
		  const startY = e.clientY;

		  $(document).mousemove(resize);
		  $(document).mouseup(stopResize);

		  function resize(e) {
			const newHeight = initialHeight + (e.clientY - startY);

			if (newHeight < 180) {
			  $resizableDiv.css('height', '180px');
			} else {
			  $resizableDiv.css('height', newHeight + 'px');
			  const payload = {
				id: "size",
				width: $resizableDiv.width(),
				height: newHeight
			  };
			  window.parent.postMessage(payload, '*');
			}
		  }

		  function stopResize() {
			isResizing = false;
			$(document).off('mousemove', resize);
			$(document).off('mouseup', stopResize);
		  }
		});

		$(document).mousemove(function(e) {
		  if (isResizing) {
			e.preventDefault();
		  }
		});
  });

</script>

</body>
</html>