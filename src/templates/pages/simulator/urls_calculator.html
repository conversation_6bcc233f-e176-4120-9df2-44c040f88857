<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Calculadora de URLs de motor</title>
    <script src="https://kit.fontawesome.com/d8b9925505.js" defer=""></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: space-evenly;
        }

        .container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            max-width: 800px;
        }

        .form-group {
            width: calc(50% - 10px);
            margin-bottom: 20px;
            display: block;
        }

        .form-group:nth-child(odd) {
            padding-right: 20px;
        }

        .form-group label {
            display: block;
            font-size: 12px;
            margin-bottom: 5px;
            transition: all 0.3s ease;
        }

        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group input[type="date"],
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-group input[type="checkbox"] {
            margin-top: 5px;
        }

        .form-group input[type="text"]:focus,
        .form-group input[type="number"]:focus,
        .form-group input[type="date"]:focus,
        .form-group select:focus {
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
        }

        .url-output {
            position: relative;
            margin-top: 20px;
        }

        .url-output input[type="text"] {
            width: 100%;
            padding: 10px 50px 10px 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-copy {
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            background-color: transparent;
            border: none;
            cursor: pointer;
        }

        .btn-copy i {
            font-size: 20px;
            color: #c7c7c7;
        }

        .btn-generate {
            display: block;
            width: 100%;
            padding: 10px;
            background-color: #007bff;
            border: none;
            color: #fff;
            font-size: 16px;
            font-weight: bold;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .btn-generate:hover {
            background-color: #0056b3;
        }

        h1 {
            width: 64vw;
            text-align: left;
            margin: 30px auto;
            max-width: 800px;
        }

        .form-group input[type="text"].hidden {
            display: none;
        }

        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px 0;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        @media screen and (max-width: 600px) {
            .form-group {
                width: 100%;
            }
        }

        .main-container {
            max-width: 50%;
            padding: 10px;
        }

    </style>
</head>

<body>
<div class="main-container">
    <h1>{{ T_URLS_CALCULATOR_TITLE }}</h1>
    <div class="container">

        <!-- Campos existentes -->
        <div class="form-group">
            <label for="dominio">{{ T_BOOKING_DOMAIN }}:</label>
            <input type="text" id="dominio" value="{{ booking_domain }}">
        </div>
        <div class="form-group">
            <label for="pasoBooking">{{ T_BOOKING_STEP }}:</label>
            <select id="pasoBooking">
                <option value="booking1">booking1</option>
                <option value="booking0">booking0</option>
            </select>
        </div>
        <div class="form-group">
            <label for="numAdultos">{{ T_RESERVATIONS_ROOMS_ADULTS }}:</label>
            <input type="number" id="numAdultos" required="">
        </div>
        <div class="form-group">
            <label for="numNinos">{{ T_RESERVATIONS_ROOMS_KIDS }}:</label>
            <input type="number" id="numNinos">
        </div>
        <div class="form-group">
            <label for="fechaEntrada">{{ T_ENTRY_DATE }}:</label>
            <input type="date" id="fechaEntrada">
        </div>
        <div class="form-group">
            <label for="fechaSalida">{{ T_DEPARTURE_DATE }}:</label>
            <input type="date" id="fechaSalida">
        </div>
        <div class="form-group">
            <label for="entradaManana">{{ T_ENTRY_TOMORROW }}:</label>
            <input type="checkbox" id="entradaManana">
        </div>
        <!-- Nuevo campo "Release" -->
        <div class="form-group">
            <label for="release">Release:</label>
            <input type="text" id="release">
        </div>
        <div class="form-group">
            <label for="currency">Currency:</label>
            <select id="currency">
                <option value=""></option>
                <option value="EUR">EUR</option>
                <option value="USD">USD</option>
                <option value="MXN">MXN</option>
                <option value="ARS">ARS</option>
                <option value="COP">COP</option>
                <option value="GBP">GBP</option>
            </select>
        </div>
        <div class="form-group">
            <label for="packagesFilter" style="display: none" class="tooltip">Filtro de paquetes por nombre (separados por ;):
                <span class="tooltiptext">Introduce el título del paquete sin espacios, sin símbolos y sin la letra ñ</span>
            </label>
            <input type="text" id="packagesFilter" class="hidden">
        </div>
    </div>

     <div class="form-group" id="packagesOrderField" style="display: none;">
            <label for="packagesOrder" class="tooltip" style="display: none;">{{ T_PACKAGES_ORDER }}:
                <span class="tooltiptext">{{ T_PACKAGES_ORDER_TOOLTIP }}</span>
            </label>
            <input type="text" id="packagesOrder" class="hidden">
        </div>
</div>
<div class="main-container" style="margin-top: 100px;">
    <div class="container">
        <div class="form-group">
            <label for="hotelCodes">Hotel codes:</label>
            <input type="text" id="hotelCodes">
        </div>
        <div class="form-group">
            <label for="roomSelected">{{ T_ROOM }}:</label>
            <input type="text" id="roomSelected">
        </div>
        <div class="form-group">
            <label for="rateSelected">{{ T_RATE }}:</label>
            <input type="text" id="rateSelected">
        </div>
        <div class="form-group">
            <label for="idioma">{{ T_LANGUAGE }}:</label>
            <select id="idioma">
                <option value="">{{ T_NONE }}</option>
                <option value="SPANISH">SPANISH</option>
                <option value="ENGLISH">ENGLISH</option>
                <option value="FRENCH">FRENCH</option>
                <option value="GERMAN">GERMAN</option>
                <option value="CATALAN">CATALAN</option>
                <option value="PORTUGUESE">PORTUGUESE</option>
                <option value="RUSSIAN">RUSSIAN</option>
            </select>
        </div>
        <div class="form-group">
            <label for="nochesMinimas">{{ T_MINIMUM_NIGHTS }}:</label>
            <input type="number" id="nochesMinimas">
        </div>
        <div class="form-group">
            <label for="promocode">{{ T_PROMOCODE }}:</label>
            <input type="text" id="promocode">
        </div>
        <div class="form-group">
            <label for="insertarUtm" class="tooltip">{{ T_INSERT_UTM }}:
                <span class="tooltiptext">{{ T_INSERT_UTM_TOOLTIP }}</span>
            </label>
            <input type="text" id="insertarUtm">
        </div>
        <div class="form-group">
            <label for="paquetes" class="tooltip">{{ T_PACKAGES }}:
                <span class="tooltiptext">{{ T_PACKAGES_TOOLTIP }}</span>
            </label>
            <input type="checkbox" id="paquetes" onchange="togglePackagesOrderVisibility()">
        </div>
        <div class="form-group" id="packagesOrderField" style="display: none;">
            <label for="packagesOrder" class="tooltip">{{ T_PACKAGES_ORDER }}:
                <span class="tooltiptext">{{ T_PACKAGES_ORDER_TOOLTIP }}</span>
            </label>
            <input type="text" id="packagesOrder">
        </div>
    </div>
      <div class="container">
        <button class="btn-generate" onclick="generateURL()">{{ T_GENERATE_URL }}</button>
    </div>
    <div class="container url-output">
        <input type="text" id="urlOutput" readonly="">
        <button class="btn-copy" onclick="copyURL()">
            <i class="fas fa-copy"></i>
        </button>
    </div>

</div>

<!-- Datos de configuración para JS -->
<script type="application/json" id="config-data">
    {
        "isAntiscrapingEnabled": {{ is_antiscraping_enabled|tojson }},
        "defaultUtm": {{ default_utm|tojson }}
    }
</script>

<script>
    // Leer datos de configuración
    const configDataElement = document.getElementById('config-data');
    const configData = JSON.parse(configDataElement.textContent || configDataElement.innerText);
    const isAntiscrapingEnabled = configData.isAntiscrapingEnabled;
    const defaultUtm = configData.defaultUtm;

    function generateURL() {

        let dominio = document.getElementById('dominio').value;
        const pasoBooking = document.getElementById('pasoBooking').value;
        const numAdultos = document.getElementById('numAdultos').value;
        const numNinos = document.getElementById('numNinos').value;
        let fechaEntrada = document.getElementById('fechaEntrada').value;
        let fechaSalida = document.getElementById('fechaSalida').value;
        const entradaManana = document.getElementById('entradaManana').checked;
        let hotelCodes = document.getElementById('hotelCodes').value;
        let roomSelected = document.getElementById('roomSelected').value.trim();
        let rateSelected = document.getElementById('rateSelected').value;
        const idioma = document.getElementById('idioma').value;
        const nochesMinimas = document.getElementById('nochesMinimas').value;
        const promocode = document.getElementById('promocode').value;
        let insertarUtm = document.getElementById('insertarUtm').value.trim(); // Trim para quitar espacios
        const paquetes = document.getElementById('paquetes').checked;
        const packagesOrder = document.getElementById('packagesOrder').value;
        const release = document.getElementById('release').value;
        const currency = document.getElementById('currency').value;
        const packagesFilter = document.getElementById('packagesFilter').value;

        // Verificar si el dominio termina con "/" y eliminarlo si es necesario
        if (dominio.endsWith('/')) {
            dominio = dominio.slice(0, -1);
        }
        let url = `${dominio}/${pasoBooking}`;

        const params = [];

        if (numAdultos && numAdultos !== '0') {
            params.push(`adultsRoom1=${numAdultos}`);
        } else {
            alert('El número de adultos es obligatorio');
            return;
        }

        if (numNinos && numNinos !== '0') {
            params.push(`childrenRoom1=${numNinos}`);
        }

        if (fechaEntrada) {
            const [anio, mes, dia] = fechaEntrada.split("-");
            fechaEntrada = `${dia}/${mes}/${anio}`;
            params.push(`startDate=${fechaEntrada}`);
        }

        if (fechaSalida) {
            const [anio, mes, dia] = fechaSalida.split("-");
            fechaSalida = `${dia}/${mes}/${anio}`;
            params.push(`endDate=${fechaSalida}`);
        }

        if (fechaEntrada && fechaSalida) {
            const [diaEntrada, mesEntrada, anioEntrada] = fechaEntrada.split("/");
            const [diaSalida, mesSalida, anioSalida] = fechaSalida.split("/");

            const dateEntrada = new Date(anioEntrada, mesEntrada - 1, diaEntrada);
            const dateSalida = new Date(anioSalida, mesSalida - 1, diaSalida);

            if (dateSalida <= dateEntrada) {
                alert('La fecha de salida no puede ser igual o anterior a la fecha de entrada.');
                return;
            }

        }

        if (entradaManana) {
            params.push('entry_tomorrow=true');
        }

        hotelCodes = hotelCodes.replace(/,/g, '%3B');
        hotelCodes = hotelCodes.replace(/\s+/g, '%3B');
        hotelCodes = hotelCodes.replace(/;/g, '%3B');

        if (hotelCodes && hotelCodes !== '0') {
            if (pasoBooking === 'booking1') {
                params.push(`namespace=${hotelCodes}`);
            } else if (pasoBooking === 'booking0') {
                params.push(`applicationIds=${hotelCodes}`);
            }
        }

        if (idioma && idioma !== '') {
            params.push(`language=${idioma}`);
        }

        if (nochesMinimas && nochesMinimas !== '0') {
            params.push(`days_num=${nochesMinimas}`);
        }

        if (rateSelected) {
            params.push(`rate_filter=${rateSelected}`);
        }

        if (roomSelected) {
            roomSelected = roomSelected.replace(" ", "+")
            params.push(`roomFilter=${roomSelected}`);
        }

        if (promocode) {
            params.push(`promocode=${promocode}`);
        }

        // --- Lógica UTM modificada ---
        if (isAntiscrapingEnabled && !insertarUtm && defaultUtm) {
            // Si antiscraping está activo, el campo UTM está vacío, y hay una UTM por defecto,
            // usamos la UTM por defecto.
            insertarUtm = defaultUtm;
        }

        let utmStringToAdd = ""; // Variable para construir la cadena UTM final
        if (insertarUtm) {
            // Si hay UTM (del usuario o por defecto), preparamos la cadena para añadirla al final
            // Asegurándonos de que empieza con el separador correcto ('?' o '&')
            const separator = params.length > 0 ? '&' : '?';
            if (insertarUtm.startsWith('?') || insertarUtm.startsWith('&')) {
                 // Si ya empieza con un separador, usamos ese separador si es el correcto,
                 // o lo reemplazamos si es incorrecto
                 if (insertarUtm.startsWith(separator)) {
                     utmStringToAdd = insertarUtm;
                 } else {
                     utmStringToAdd = separator + insertarUtm.substring(1);
                 }
            } else {
                 // Si no empieza con separador, añadimos el correcto
                 utmStringToAdd = separator + insertarUtm;
            }
        }
        // --- Fin Lógica UTM modificada ---

        if (paquetes) {
            params.push('package_preselection=true');
            if (packagesOrder) {
                params.push(`packages_order=${packagesOrder}`);
            }
            if (packagesFilter) {
                params.push(`packages_filter=${packagesFilter}`);
            }
        }

        if (release) {
            params.push(`release=${release}`);
        }

        if (currency) {
            params.push(`selected_currency=${currency}`);
        }

        if (params.length > 0) {
            url += `?${params.join('&')}`;
        }

        // Añadir UTM al final si existe
        if (utmStringToAdd) {
             url += utmStringToAdd; // Añadimos la cadena UTM directamente
        }

        url = url.replace(/(?<!https?:)\/\/{2,}/g, '/');
        // Limpieza final por si acaso (ej: ?& -> ?)
        url = url.replace("?&", "?");

        document.getElementById('urlOutput').value = url;
    }

    function copyURL() {
        const urlOutput = document.getElementById('urlOutput');
        urlOutput.select();
        urlOutput.setSelectionRange(0, 99999);
        document.execCommand('copy');
        alert('URL copiada al portapapeles');
    }

    function togglePackagesOrderVisibility() {
        const packagesOrderField = document.getElementById('packagesOrderField');
        const paquetesCheckbox = document.getElementById('paquetes');
        const packagesOrderInput = document.getElementById('packagesOrder');
        const packagesOrderLabel = document.querySelector('label[for="packagesOrder"]');
        const packagesFilterInput = document.getElementById('packagesFilter');
        const packagesFilterLabel = document.querySelector('label[for="packagesFilter"]');

        if (paquetesCheckbox.checked) {
            packagesOrderField.style.display = 'block';
            packagesOrderInput.classList.remove('hidden');
            packagesOrderLabel.style.display = 'block';
            packagesFilterInput.classList.remove('hidden');
            packagesFilterLabel.style.display = 'block';
        } else {
            packagesOrderField.style.display = 'none';
            packagesOrderInput.classList.add('hidden');
            packagesOrderLabel.style.display = 'none';
            packagesFilterInput.classList.add('hidden');
            packagesFilterLabel.style.display = 'none';
        }
    }

    document.addEventListener('DOMContentLoaded', togglePackagesOrderVisibility);
</script>
</body>
</html>