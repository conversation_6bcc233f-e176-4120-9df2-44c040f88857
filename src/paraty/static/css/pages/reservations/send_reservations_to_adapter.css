/* line 1, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
body {
  box-sizing: border-box;
  text-rendering: auto;
  font-family: "Roboto", sans-serif;
  letter-spacing: 0.4px;
  margin-top: 15px;
}
/* line 7, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
body .wraper {
  display: flex;
  justify-content: center;
  color: #446ca9;
  text-align: center;
}
/* line 13, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
body .wraper input:not([type="checkbox"]) {
  border: 1px solid #EDEDED;
  appearance: none;
  border-radius: 3px;
  padding: 7px 14px;
}
/* line 19, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
body .wraper input:not([type="text"]) {
  text-align: center;
}

/* line 26, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.search {
  background-color: #446ca9;
  color: white;
  appearance: none;
  padding: 7px 14px;
  text-transform: uppercase;
  border-radius: 3px;
  font-weight: bold;
  letter-spacing: 1px;
  cursor: pointer;
  display: inline-block;
  margin: 5px;
  max-height: 40px;
}

/* line 41, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.date_container {
  display: flex;
  justify-content: flex-start;
  margin-top: 15px;
}

/* line 46, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.form {
  border: 2px #446ca9 solid;
  border-radius: 5px;
  padding: 15px;
  margin: 5px;
}

/* line 54, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.form-group {
  display: flex;
  flex-direction: column;
  margin: 0px 15px;
  width: 40%;
  text-align: center;
}

/* line 61, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.form-group label {
  margin-bottom: 5px;
}

/* line 64, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.box {
  width: 90%;
  display: contents;
  text-align: center;
}

/* line 70, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.wraper_result {
  text-align: center;
  padding: 15px;
  margin: 0 auto;
}

/* line 76, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.buttons_push {
  align-items: center;
  display: flex;
}
/* line 79, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.buttons_push * {
  width: 50%;
}

/* line 83, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
#reservation_id {
  width: 70%;
}

/* line 86, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.result {
  background-color: #ededed;
  border: groove;
  margin: 5px 0;
  display: flex;
  text-align: center;
  justify-content: space-between;
  align-items: center;
}

/* line 99, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.reservation:nth-child(even) {
  background-color: white;
}

/* line 102, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.push_all_reservations {
  display: none;
}

/* line 106, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.result:nth-child(2n) {
  background-color: white;
}

/* line 112, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.result_extra {
  border: groove;
  margin: 5px 0;
  display: flex;
  text-align: left;
  flex-direction: column;
  cursor: pointer;
}

/* line 122, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.element_extra {
  display: table;
  padding: 1rem 1rem;
}

/* line 127, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.result_header {
  display: flex;
  text-align: center;
  justify-content: space-between;
  border: 1px solid darkgrey;
}

/* line 134, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.element {
  width: 50%;
  display: table;
  padding: 7px 14px;
}

/* line 140, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.element_header {
  width: 50%;
  display: table-cell;
  vertical-align: middle;
  background-color: #446ca9;
  color: white;
  appearance: none;
  padding: 7px 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* line 155, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.check {
  margin: 10px;
}

/* line 162, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
#time_checkbox {
  display: flex;
}

/* line 165, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.timetime {
  display: flex;
  flex-direction: column;
  width: 33.33%;
  justify-content: center;
  text-align: center;
}

/* line 172, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.time_container {
  display: flex;
  justify-content: flex-start;
  margin-top: 15px;
}

/* line 177, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.loader_container {
  text-align: center;
}
/* line 179, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.loader_container .loader {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #446ca9;
  border-radius: 50%;
  width: 15px;
  height: 15px;
  animation: spin 2s linear infinite;
  display: none;
  text-align: center;
  margin: 10px auto 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* line 201, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
#search_by_extrainfo {
  text-align: center;
  margin: 15px 0;
}

/* line 206, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.id {
  margin: 15px 0;
  justify-content: flex-start;
}

/* line 211, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.buttons {
  text-align: center;
}

/* line 215, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.selection {
  margin: 15px 0;
  display: block;
}

/* line 219, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.show_filters {
  text-align: center;
}

/* line 222, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.js-example-basic-multiple {
  width: 100%;
  padding: 12px 15px;
  margin: 8px 0;
  display: inline-block;
  border: 1px solid #ccc;
  box-sizing: border-box;
}

/* line 231, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.select2-container {
  width: 100%;
  margin-bottom: 0;
}

/* line 236, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.select2-container--open .select2-dropdown--below {
  margin-top: 0;
}

/* line 240, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.select2-results__options {
  color: black;
  border-top: solid 1px rgba(0, 0, 0, 0.1);
  height: 150px;
  overflow-y: scroll;
}

/* line 246, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.select2-search--dropdown .select2-search__field {
  position: relative;
  max-width: 100%;
}

/* line 250, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.select2-selection__rendered {
  box-sizing: border-box;
  border: 1px solid #ccc;
  width: 100%;
  padding: 10px 15px;
  display: inline-block;
  text-align: center;
}

/* line 259, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
li {
  background-color: #ececec;
}

/* line 263, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
li:nth-child(even) {
  background-color: white;
}

/* line 267, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
span {
  border-radius: 5px;
}

/* line 270, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
span {
  margin-top: 8px;
  margin-bottom: 10px;
  max-width: 100%;
}

/* line 275, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
span .select2-dropdown {
  width: 100%;
  max-width: 100%;
}

/* line 279, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.extrainfo {
  margin: 15px 0;
}

/* line 286, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
.new_divs {
  margin: 20px;
}

/* line 289, ../../../../sass/pages/reservations/send_reservations_to_adapter.scss */
#name_to_filter {
  width: 40%;
}
