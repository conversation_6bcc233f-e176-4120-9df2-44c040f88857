function send_confirmation(e) {

    $("#sendConfirmReservation").addClass("btn_disable").addClass("btn_loading");

    e.preventDefault();



    var chk_hot = $("input[name=chk_email_hotel]").is(":checked");
    if (chk_hot) {
        $(".custom_hotel_email_label").css({ "display": "block" });
        $(".custom_hotel_email").css({ "display": "block" });
    } else {
        $(".custom_hotel_email_label").css({ "display": "none" });
        $(".custom_hotel_email").css({ "display": "none" });
    }


    var chk_cust = $("input[name=chk_email_customer]").is(":checked");
    if (chk_cust) {
        $(".custom_customer_email_label").css({ "display": "block" });
        $(".custom_customer_email").css({ "display": "block" });
    } else {
        $(".custom_customer_email_label").css({ "display": "none" });
        $(".custom_customer_email").css({ "display": "none" });
    }

    var chk_benefi = $("input[name=chk_email_beneficiary]").is(":checked");
    if (chk_benefi) {
        $(".custom_beneficiary_label").css({ "display": "block" });
        $(".custom_beneficiary_email").css({ "display": "block" });
    } else {
        $(".custom_beneficiary_label").css({ "display": "none" });
        $(".custom_beneficiary_email").css({ "display": "none" });
    }
    $.confirm({

        title: T_RESERVATIONS_RESEND_CONFIRMATION,
        content: $("#popup_confirm_booking").html(),
        columnClass: 'my_confirm',
        autoClose: false,
        onContentReady: () => {
            document.querySelectorAll("input.custom_hotel_email")[1].value = document.querySelector("input[name=hotel_email]").value;
            document.querySelectorAll("input.custom_customer_email")[1].value = document.querySelector("input[name=email]").value;
        },
        buttons: {
            confirm_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
                btnClass: 'btn btn_small',
                keys: ['enter'],
                action: function() {

                    $(".spinner_wrapper_faldon").css("display", "block");

                    var formData = new FormData(document.getElementById("data"));

                    if (chk_cust) {
                        formData.set("email", document.querySelectorAll("input.custom_customer_email")[1].value);
                    }

                    if (chk_hot) {
                        formData.set("hotel_email", document.querySelectorAll("input.custom_hotel_email")[1].value);
                    }

                    if (chk_benefi) {
                        formData.set("beneficiary_email", document.querySelectorAll("input.custom_beneficiary_email")[1].value);
                    }

                    var final_post_dict = {};
                    formData.forEach(function(value, key) {
                        final_post_dict[key] = value;
                    });
                    $.ajax({
                        url: "/reservation/send_confirmation/",
                        data: JSON.stringify(final_post_dict),
                        contentType: "application/json",
                        type: 'POST',
                        success: function(result) {
                            if (result == "OK") {
                                show_alert(T_ALERT_RESERVATION_SEND_MAIL, T_ALERT_RESERVATION_SEND_MAIL_TITLE);
                            } else {
                                show_alert(T_ALERT_RESERVATION_NOT_SAVE, T_ERROR);
                            }
                            $("#sendConfirmReservation").removeClass("btn_disable").removeClass("btn_loading");
                            $(".spinner_wrapper_faldon").css("display", "none");
                        }
                    });




                    return true;
                }
            },
            cancel_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
                btnClass: 'btn btn_small btn_link',
                action: function() {
                    $("#sendConfirmReservation").removeClass("btn_disable").removeClass("btn_loading");
                    $(".spinner_wrapper_faldon").css("display", "none");
                    return true;

                }
            }

        }
    });

}


function send_confirmation_availability(e){
    $("#sendConfirmationAvailability").addClass("btn_disable").addClass("btn_loading");
    e.preventDefault();
    $.confirm({

    title: T_CONFIRM_REQUEST,
    content: $("#popup_confirm_request_").html(),
    columnClass: 'my_confirm',
    autoClose: false,
    buttons: {
        confirm_button: {
            text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
            btnClass: 'btn btn_small',
            keys: ['enter'],
            action: function() {

                $(".spinner_wrapper_faldon").css("display", "block");

                var formData = new FormData(document.getElementById("data"));

                var final_post_dict = {
                    'identifier': $('.identifier').val(),
                    'status': 'confirmed',
                    'hotel_code': $('.real_hotel_code').val(),
                    'action': 'pending_reservation_status_changed'
                };
                var domain = $('#booking_domain').val();
                $.ajax({
                    url: domain + "/utils",
                    data: $.param(final_post_dict),
                    type: 'POST',
                    success: function(result) {
                        if (result == "OK") {
                            show_alert(T_ALERT_RESERVATION_SEND_MAIL, T_ALERT_RESERVATION_SEND_MAIL_TITLE);
                        } else {
                            show_alert(T_ALERT_RESERVATION_NOT_SAVE, T_ERROR);
                        }
                        $("#sendConfirmationAvailability").removeClass("btn_disable").removeClass("btn_loading");
                        $(".spinner_wrapper_faldon").css("display", "none");
                    }
                });
                return true;
            }
        },
        cancel_button: {
            text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
            btnClass: 'btn btn_small btn_link',
            action: function() {
                $("#sendConfirmationAvailability").removeClass("btn_disable").removeClass("btn_loading");
                $(".spinner_wrapper_faldon").css("display", "none");
                return true;

            }
        }

        }
    });
}

function send_reservation_to_channel(e) {
    e.preventDefault();
    var channel_info = $('#channel_info').val();
    var final_post_dict = {
        'hotel_code': $("#real_hotel_code").val(),
        'identifier': $("input[name=identifier]").val(),
        'channel_info': channel_info,
        'from_modify': 'True'
    };
    var confirm_content = T_SEND_RESERVATION_CONFIRM.replace('@@channel@@', $('#channel_info :selected').text());

    $.confirm({
        title: T_SEND_RESERVATION_TITLE,
        content: confirm_content.replace(/&lt;br&gt;/g, '<br>'),
        columnClass: 'my_confirm',
        autoClose: false,
        buttons: {
            confirm_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
                btnClass: 'btn btn_small',
                keys: ['enter', 'shift'],
                action: function () {
                    $("#sendReservationToChannel").addClass("btn_disable").addClass("btn_loading");
                    $(".spinner_wrapper_faldon").css("display", "block");
                    let session_key = $('#session_key').val();
                    $.ajax({
                        url: '/reservations/reservations_push?sessionKey=' + session_key,
                        data: JSON.stringify(final_post_dict),
                        contentType: "application/json",
                        type: 'POST',
                        success: function (result) {
                            if (result === 'OK') {
                                show_alert(T_PUSH_OK, '');
                            } else {
                                show_alert(T_PUSH_KO, T_ERROR);
                            }
                            $("#sendReservationToChannel").removeClass("btn_disable").removeClass("btn_loading");
                            $(".spinner_wrapper_faldon").css("display", "none");
                        },
                        error: function (error) {
                            show_alert(T_PUSH_KO, T_ERROR);
                            $("#sendReservationToChannel").removeClass("btn_disable").removeClass("btn_loading");
                            $(".spinner_wrapper_faldon").css("display", "none");
                        }
                    });
                    return true;
                }
            },
            cancel_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
                btnClass: 'btn btn_small btn_link',
                action: function () {
                    $("#sendReservationToChannel").removeClass("btn_disable").removeClass("btn_loading");
                    $(".spinner_wrapper_faldon").css("display", "none");
                    return true;
                }
            }
        }
    });
}


function dates_control() {
    let start = $("#checkin").val().split("/");
    let ts_start = new Date(start[2], start[1]-1, start[0]);

    let end = $("#checkout").val().split("/");
    let ts_end = new Date(end[2], end[1]-1, end[0]);

    if (ts_start >= ts_end) {
        return false;
    }
    return true;

}

function set_forfait_date(id, value) {
    var input_start_date = $("#" + id);
    var target_wrapper_forfait = input_start_date.closest(".wrapper-forfait");
    var input_end_date = target_wrapper_forfait.find(".forfait_endDate");
    var current_input_date = value.split("/");
    var ts_current_input_date = new Date(current_input_date[2], current_input_date[1] - 1, current_input_date[0]);
    var start_forfait = input_start_date.val().split("/");
    var ts_start_forfait = new Date(start_forfait[2], start_forfait[1] - 1, start_forfait[0]);
    var end_forfait = input_end_date.val().split("/");
    var ts_end_forfait = new Date(end_forfait[2], end_forfait[1] - 1, end_forfait[0]);
    if (Date.parse(ts_start_forfait) <= Date.parse(ts_end_forfait)){
        input_start_date.attr("value", $.datepicker.formatDate("dd/mm/yy", ts_start_forfait));
        input_end_date.attr("value", $.datepicker.formatDate("dd/mm/yy", ts_end_forfait));
        var daily_price_forfait = target_wrapper_forfait.find(".price_per_day_forfait");
        var last_forfait_total_price = $(".forfait-pvp").val();
        recalculate_total_forfait_price(daily_price_forfait, "", verbose = true);
        update_total_price_forfait();
        recalculate_prices_with_forfait(last_forfait_total_price);
    }else{
        input_start_date.attr("value", $.datepicker.formatDate("dd/mm/yy", ts_current_input_date));
        show_alert(T_ALERT_FORFAIT_WRONG_DATE, T_ERROR);
    }
}

function pax_control(){

    var rooms =  $(".rooms .wrapper-rooms");
    var result = true;
    var room_index = "0";
    rooms.each(function(){
        var room = $(this);
        var adults = room.find(".room-adults").val();
        var kids = room.find(".room-kids").val();
        var babies = room.find(".room-babies").val();

        var capacity = `${adults}-${kids}-${babies}`;
        var room_selector = room.find(".room-key");

        var capacities = JSON.parse((room_selector.find(":selected").data("capacities") || "[]").replaceAll("'", '"'));

        if(!capacities.includes(capacity)){
            result = false;
            room_index = room.attr("id").replace("room-entry-", "");
        }
    });

    return [result, room_index];
}

function price_day_control() {
    var roomPriceInputs = $('input.room-price[name*="room_price_day"]');
    var result = true;
    roomPriceInputs.each(function() {
        var value = $(this).val();
        if (value === '' || value === ' '){
            result = false;
        }
        return true;
    });
    return result;
}

function services2_control(){
    if ($('#check_service2_availability').val()){
        var result = true;
        $('.advise-message').each(function(){
            if ($(this).val() === "disabled saving"){
                result = false;
            }
        });
    }else{
        result = true;
    }
    return result;
}

function new_room_control(key){
    let result = true;
    $('select[name^="room"][name$="[' + key + ']"]').each(function() {
        let selected_option = $(this).val();
        if (!selected_option){
            result = false;
        }
        return true;
    });
    return result;
}

function rooms_control(){
    let errorToShow = '';
    if (dates_control() === false) {
        errorToShow = T_ALERT_RESERVATION_WRONG_DATE;
    }
    if (price_day_control() === false) {
        errorToShow = T_ALERT_DAILY_PRICE_MISSING;
    }
    if (new_room_control('board') === false) {
        errorToShow = T_ALERT_BOARD_MISSING;
    }
    if (new_room_control('rate') === false) {
        errorToShow = T_ALERT_RATE_MISSING;
    }
    if (new_room_control('key') === false) {
        errorToShow = T_ALERT_ROOM_MISSING;
    }
    // deactivated due to now they can add services without restrictions
    // if (services2_control() === false) {
    //     errorToShow = T_ALERT_SERVICE_NO_AVAILABLE;
    // }

    if (check_daily_prices_vs_rooms_prices() === false) {
        errorToShow = T_ROOM_PRICE_MISMATCH;
    }


    let [is_valid, room_index] = pax_control();
    if (!is_valid){
        errorToShow = T_ALERT_WRONG_CAPACITY + room_index;
    }

    return errorToShow;
}
function check_daily_prices_vs_rooms_prices() {
    let i, price, day_price, total_price, regex, match, total;
    for (i = 1; i <= 5; i++) {
        price = $('[name="room[' + i + '][price]"]').val();
        if (price === undefined) {
            break;
        } else {
            day_price = $('#day_price_' + i);
            if (day_price) {
                total_price = day_price.find('.precio-final').text().trim().replace(/\s+/g, '');
                regex = /Total:(\d+\.\d+)/;
                match = total_price.match(regex);
                if (match) {
                    total = parseFloat(match[1]);
                    if (Math.abs(total - parseFloat(price)) > 2) {
                        return false;
                    }
                } else {
                    total_price = day_price.find('.precio-final').text().trim().replace(/\s+/g, '');
                    total = parseFloat(total_price);
                    if (Math.abs(total - parseFloat(price)) > 2) {
                        return false;
                    }
                }
            }
        }
    }
    return true;
}


function omnibees_date_control(e, form){
    return new Promise((resolve, reject) => {
    e.preventDefault();
    let session_key = $("input[name='sessionKey']").val();
    const url = new URL('/reservations/check_omnibees_availability?sessionKey='+session_key, window.location.origin);
    const data = {
        hotel_code: $("#real_hotel_code").val(),
        identifier: $("input[name=identifier]").val(),
        form: form,
    };

    const requestOptions = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
    };

    fetch(url, requestOptions)
        .then((res) => res.json())
        .then(response => {
            resolve(response);
        })
        .catch(error => {
            reject(error);
        });
});

}

function save_reservation(e, final_post_dict) {

    var control = rooms_control();
    if (control != ''){
        show_alert(control, T_ERROR);
        e.preventDefault();
        return true;
    }

    formats_control();

    e.preventDefault();

    var chk_hot = $("input[name=chk_email_hotel]").is(":checked");
    toggleDisplay(chk_hot, [
        ".custom_hotel_email_label-save",
        ".custom_hotel_email-save",
        ".custom_hotel_email_label-save-missing-room-availability",
        ".custom_hotel_email-save-missing-room-availability"
    ]);

    var chk_cust = $("input[name=chk_email_customer]").is(":checked");
    toggleDisplay(chk_cust, [
        ".custom_customer_email_label-save",
        ".custom_customer_email-save",
        ".custom_customer_email_label-save-missing-room-availability",
        ".custom_customer_email-save-missing-room-availability"
    ]);

    let confirm_content = "";
    if (final_post_dict['missing_room_availability']){
        confirm_content = $("#popup_save_booking_missing_room_availability").html().replace('@@missing_rooms@@',  final_post_dict['missing_room_availability']);
    }else{
        confirm_content = $("#popup_save_booking").html();
    }
    if (chk_cust || chk_hot){
        confirm_content = confirm_content.replace('@@T_SENDING_EMAILS_INFO@@', T_SENDING_EMAILS_INFO);
    }else{
        confirm_content = confirm_content.replace('@@T_SENDING_EMAILS_INFO@@', '');
    }
    $.confirm({

        title: T_ALERT_RESERVATION_SAVE_TITLE2,
        content: confirm_content.replace(/&lt;br&gt;/g, '<br>'),
        columnClass: 'my_confirm',
        autoClose: false,
        // onContentReady: () => {
        //     document.querySelectorAll("input.custom_hotel_email-save")[1].value = document.querySelector("input[name=hotel_email]").value;
        //     document.querySelectorAll("input.custom_customer_email-save")[1].value = document.querySelector("input[name=email]").value;
        // },
        onContentReady: () => {
            var hotelEmailInputs = document.querySelectorAll("input.custom_hotel_email-save");
            var customerEmailInputs = document.querySelectorAll("input.custom_customer_email-save");
            var hotel_email = document.querySelector("input[name=hotel_email]").value;
            var customer_email = document.querySelector("input[name=email]").value;
            if (hotelEmailInputs.length > 1 && customerEmailInputs.length > 1) {
                hotelEmailInputs[1].value = hotel_email;
                customerEmailInputs[1].value = customer_email;
            } else {
                document.querySelectorAll("input.custom_hotel_email-save-missing-room-availability")[1].value = hotel_email;
                document.querySelectorAll("input.custom_customer_email-save-missing-room-availability")[1].value = customer_email;
            }
        },
        buttons: {
            confirm_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
                btnClass: 'btn btn_small',
                keys: ['enter', 'shift'],
                action: function() {
                    $("#saveReservation").addClass("btn_disable").addClass("btn_loading");
                    $(".spinner_wrapper_faldon").css("display", "block");
                    var extrainfo = $('#extrainfo_hidden').text();
                    final_post_dict['extraInfo'] = extrainfo;
                    var formData = new FormData(document.getElementById("data"));
                    if (chk_cust) {
                        const customerEmail = ["input.custom_customer_email-save", "input.custom_customer_email-save-missing-room-availability"]
                            .map(selector => document.querySelectorAll(selector)[1]?.value)
                            .find(value => value);
                        if (customerEmail) formData.set("email", customerEmail);
                    }

                    if (chk_hot) {
                        const hotelEmail = ["input.custom_hotel_email-save", "input.custom_hotel_email-save-missing-room-availability"]
                            .map(selector => document.querySelectorAll(selector)[1]?.value)
                            .find(value => value);
                        if (hotelEmail) formData.set("hotel_email", hotelEmail);
                    }

                    // var new_internal_comments = $('#new_internal_comments');
                    // final_post_dict['new_internal_comments'] = new_internal_comments;
                    $.ajax({
                        url: "/reservation/save/",
                        data: JSON.stringify(final_post_dict),
                        contentType: "application/json",
                        type: 'POST',
                        success: function(result) {
                            if (result.indexOf("OK") === 0) {
                                var extra_msg_ok = "";
                                var info_ok = result.split("@@");
                                if (info_ok.length > 1){
                                    extra_msg_ok = "<br>"+ T_BOOKING_IDENTIFIER + ": " + info_ok[1];
                                }
                                var error_services2 = result.split("@services2error@");
                                if (error_services2.length > 1){
                                    show_alert(error_services2[1], T_ALERT_RESERVATION_SAVE_TITLE);
                                }else{
                                    show_alert(T_ALERT_RESERVATION_SAVE + extra_msg_ok, T_ALERT_RESERVATION_SAVE_TITLE);
                                }
                            } else {
                                if ($(".rooms").children().length == 0) {
                                    show_alert(T_ALERT_RESERVATION_NOT_ROOMS, T_ERROR);
                                } else if (result == "KO_adapter"){
                                    show_alert(T_ALERT_INTEGRATION_ADAPTER_RESERVATION_ERROR, T_ERROR);
                                } else if (result == "KO") {
                                    show_alert(T_ALERT_RESERVATION_UNKNOWN_ERROR, T_ERROR);
                                } else {
                                    show_alert(T_ALERT_RESERVATION_CANT_MODIFY + " <br><br> <b>" + result + "</b>", T_ERROR);
                                }
                            }
                            $("#saveReservation").removeClass("btn_disable").removeClass("btn_loading");
                            $(".spinner_wrapper_faldon").css("display", "none");
                        }
                    });
                    return true;
                }
            },
            cancel_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
                btnClass: 'btn btn_small btn_link',
                action: function() {
                    $("#saveReservation").removeClass("btn_disable").removeClass("btn_loading");
                    $(".spinner_wrapper_faldon").css("display", "none");
                    return true;

                }
            }

        }
    });

}

function remove_entry_room(control) {

    $.confirm({

        title: T_DELETE_ROOM,
        content: $("#popup_remove_room").html(),
        columnClass: 'my_confirm',
        autoClose: false,
        buttons: {
            confirm_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
                btnClass: 'btn btn_small',
                keys: ['enter', 'shift'],
                action: function() {

                    var entry_index = control.data('room');
                    $("#room-entry-" + entry_index).toggle("fast", function() {
                        $("#room-entry-" + entry_index).remove();
                        delete_room_owner(entry_index);
                        recalculate_counters();
                        recalculate_prices();
                    });

                    return true;
                }
            },
            cancel_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
                btnClass: 'btn btn_small btn_link',
                action: function() {

                    return true;

                }
            }

        }
    });

    return;
}

function remove_entry_service(control) {

    $.confirm({

        title: T_DELETE_SERVICE,
        content: $("#popup_remove_service").html(),
        columnClass: 'my_confirm',
        autoClose: false,
        buttons: {
            confirm_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
                btnClass: 'btn btn_small',
                keys: ['enter', 'shift'],
                action: function() {
                    var entry_index = String(control.data('service'));
                    var service_type = get_service_type(control);
                    if (entry_index === ''){
                        entry_index = control.attr('id');
                    }

                    $("#"+service_type+"-entry-" + entry_index).toggle("fast", function() {
                        $("#"+service_type+"-entry-" + entry_index).remove();
                        recalculate_counters();
                        recalculate_prices();
                    });

                    return true;
                }
            },
            cancel_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
                btnClass: 'btn btn_small btn_link',
                action: function() {

                    return true;

                }
            }

        }
    });

    return;
}

function remove_entry_adult_forfait(control) {

    $.confirm({

        title: T_DELETE_ADULT_FORFAIT,
        content: $("#popup_remove_person_forfait").html(),
        columnClass: 'my_confirm',
        autoClose: false,
        buttons: {
            confirm_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
                btnClass: 'btn btn_small',
                keys: ['enter', 'shift'],
                action: function() {
                    var entry_index = control.data('forfait');
                    $("#forfait-adult-entry-" + entry_index).toggle("fast", function() {
                        var last_forfait_total_price = $(".forfait-pvp").val();
                        $("#forfait-adult-entry-" + entry_index).remove();
                        update_total_price_forfait();
                        recalculate_prices_with_forfait(last_forfait_total_price);
                    });

                    return true;
                }
            },
            cancel_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
                btnClass: 'btn btn_small btn_link',
                action: function() {

                    return true;

                }
            }

        }
    });

    return;
}

function remove_entry_kid_forfait(control) {

    $.confirm({

        title: T_DELETE_KID_FORFAIT,
        content: $("#popup_remove_person_forfait").html(),
        columnClass: 'my_confirm',
        autoClose: false,
        buttons: {
            confirm_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
                btnClass: 'btn btn_small',
                keys: ['enter', 'shift'],
                action: function() {
                    var entry_index = control.data('forfait');
                    $("#forfait-kid-entry-" + entry_index).toggle("fast", function() {
                        var last_forfait_total_price = $(".forfait-pvp").val();
                        $("#forfait-kid-entry-" + entry_index).remove();
                        update_total_price_forfait();
                        recalculate_prices_with_forfait(last_forfait_total_price);
                    });

                    return true;
                }
            },
            cancel_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
                btnClass: 'btn btn_small btn_link',
                action: function() {

                    return true;

                }
            }

        }
    });

    return;
}

function confirm_entry_room(control) {


    if (!num_rooms_control_ok()) {
        return;
    }

    room_key = is_room_valid(true);
    if (room_key == '') {


        return;
    }


    $.confirm({

        title: T_ADD_ROOM,
        content: $("#popup_add_room").html(),
        columnClass: 'my_confirm',
        autoClose: false,
        buttons: {
            confirm_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
                btnClass: 'btn btn_small',
                keys: ['enter', 'shift'],
                action: function() {
                    control_service_restrictions(control);
                    add_entry_room(control);
                    add_entry_service2(control);
                    return true;
                }
            },
            cancel_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
                btnClass: 'btn btn_small btn_link',
                action: function() {

                    return true;

                }
            }

        }
    });

    return;


}

function remove_entry_promo(control){
    var el = $(control[0].closest(".promo"));
    el.toggle("fast", function() {
        el.remove();
    });
}

function add_entry_promo(control, promotion) {
    var next_index = parseInt($("#num_promos").val()) + 1;
    $("#num_promos").val(next_index);
    var template = $('.promo-template').children().first().clone();
    if (!promotion){
        var promotion_key = $(".panel-promotion .promo-injector").val();
    } else{
        var promotion_key = promotion;
    }
    template.find(".promo-key").attr('name', "promotion[" + next_index + "]");
    template.find(".promo-key").val(promotion_key);

    template.find(".promo_button_remove").attr('data-promo', next_index);

    template.find(".promo_button_remove").click(function(e) {
        remove_entry_promo($(this));
    });

    template.css({ display: "none" });
    template.appendTo('.promotions');
    template.show("fast");

}

function add_entry_room(control) {


    var new_index = $("#new_index").val();
    if (!new_index){
        if ($("#next_biggest_index").val()){
            var next_index = parseInt($("#next_biggest_index").val()) + 1;
        }
        else{
             var next_index = parseInt(parseInt($("#num_rooms").val()) + 1)
        }

    }
    else{
        var next_index = parseInt($("#new_index").val())
    }

    $("#num_rooms").val(parseInt($("#num_rooms").val()) + 1);

    var template = $('.room-template #room-entry-').clone();
    template.attr('id', "room-entry-" + next_index);

    template.find(".index-already-exists").attr('id', 'index-already-exists-' + next_index);
    template.find("#selectors-room-").attr('id', 'selectors-room-' + next_index);

    template.find(".room-adults").attr('name', 'room[' + next_index + '][adults]');
    template.find(".room-kids").attr('name', 'room[' + next_index + '][kids]');
    template.find(".room-babies").attr('name', 'room[' + next_index + '][babies]');

    template.find(".room-key").attr('name', 'room[' + next_index + '][key]');
    template.find(".room-rate").attr('name', 'room[' + next_index + '][rate]');
    template.find(".room-board").attr('name', 'room[' + next_index + '][board]');

    template.find(".room-price").attr('name', 'room[' + next_index + '][price]');
    template.find(".room-rate-name").attr('name', 'room[' + next_index + '][rate_name]');
    template.find(".recalculate-price-room").attr('id', 'recalculatePriceRoom' + next_index);

    var room_adults = $(".room-adults-injector").val();
    var room_kids = $(".room-kids-injector").val();
    var room_babies = $(".room-babies-injector").val();
    var room_rate = $(".room-rate-injector").val();
    var room_board = $(".room-board-injector").val();
    var room_price = $(".room-price-injector").val();

    template.find(".room-key").val(room_key);
    template.find(".room-adults").val(room_adults);
    template.find(".room-kids").val(room_kids);
    template.find(".room-babies").val(room_babies);
    template.find(".room-rate").val(room_rate);
    template.find(".room-board").val(room_board);
    template.find(".room-price").val(room_price);
    template.find(".counter-room > .label").html(T_DETAILS_ROOM + next_index);

    template.find(".room_button_remove").attr('data-room', next_index);

    template.find(".room_button_remove").click(function(e) {
        remove_entry_room($(this));
        remove_price_per_day($(this));
    });

    template.find(".room-price").keyup(function(e) {
        recalculate_prices();
    });
    template.find(".recalculate-price-room").click(function() {
        let recalculate_price_room_button = $(this);
        recalculate_price_room(recalculate_price_room_button);
    });
    template.find(".room-price").on("change",function(e) {
        update_price_per_day(e);
    });

    template.css({ display: "none" });
    template.appendTo('.rooms');
    template.show("fast");

    $(".room-adults-injector").val('2').change();
    $(".room-kids-injector").val('0').change();
    $(".room-babies-injector").val('0').change();
    $(".room-rate-injector").val('').change();
    $(".room-board-injector").val('').change();
    $(".room-price-injector").val('');

    recalculate_counters();
    recalculate_prices();
    formats_control();
    add_price_table(next_index);
    add_new_room_owner(new_index);

    template.find("select.room-adults").on("change", function(e){
        update_adults_amount(next_index);
    });
    template.find("select.room-kids").on("change", function(e){
        update_kids_amount(next_index);
    });

    return;

}

function recalculate_counters() {
    $(".count_rooms").html("(" + $('.rooms>.wrapper-rooms').length + ")");
    $(".count_services").html("(" + ($('.services>.wrapper-services').length + $('.wrapper-forfait-inner').length) + ")");
}

function valid_control_number(control) {


    var value = control.val();
    if (value == '' || value == 'NaN' || Number.isNaN(value)) {
        value = '0';
        control.val(value);
    }


}


function format_control_number(control) {


    var value = control.val();
    if (value == '' || isNaN(parseFloat(value))) {
        value = '0';
    }
    value = format_number(value);
    control.val(value);

    return value;

}

function format_number(value) {

    if (Number.isNaN(value)) {
        value = 0;
    }
    var value = parseFloat(value).toFixed(2);
    return value;
}

function fix_price_per_day() {
    var rooms = document.querySelectorAll(".rooms div[id^='room-entry-']");
    for (i of rooms) {
        var id = i.id.replace("room-entry-", "");
        if (!document.querySelector("#day_price_" + id)) {
            add_price_table(id);
        }
    }

}

function update_price_per_day(e) {
    var price = parseFloat(e.target.value);
    var id = (e.target.closest("div[id ^= 'room-entry-']").id).replace("room-entry-", "");
    var divider = $("#day_price_" + id + " .dates").find("tr:not(.precio-final)").length;

    var newprice = Math.floor((price / divider)*100)/100;
    var offset = parseFloat((price - (newprice * divider)).toFixed(2));

    var fix_offset = offset!=0? true:false;

    var first = true
    $("#day_price_" + id + " .dates").find(".room-price").each((i, v) => {
        if( first && fix_offset){
            $(v).val(format_number(newprice + offset));
            first = false;
        }else{
            $(v).val(format_number(newprice));
        }
    });

    $("#day_price_" + id + " .dates").find(".promo-price").each((i, v) => {
        $(v).val("0.00");
    });

    first = true;
    $("#day_price_" + id + " .dates").find(".subtotal-price").each((i, v) => {
        if( first && fix_offset){
            $(v).val(format_number(newprice + offset));
            first = false
        }else{
            $(v).val(format_number(newprice));
        }
    });

    $("#day_price_" + id + " .dates")[0].querySelectorAll(".precio-final td")[3].innerHTML = format_number((newprice * divider)+offset);
    recalculate_prices();



}

function add_price_table(id) {

    var container = document.createElement("div");
    container.setAttribute("class", "input_wrapper col3");
    container.id = "day_price_" + id;

    var label = document.createElement("span");
    label.appendChild(document.createTextNode(T_DETAILS_ROOM + id));
    container.appendChild(label);

    var table = document.createElement("table");
    table.setAttribute("class", "zebra_table");

    var thead = document.createElement("thead");
    var tr = document.createElement("tr");

    var date = document.createElement("th");
    date.appendChild(document.createTextNode("Dia"));
    tr.appendChild(date);

    var room = document.createElement("th");
    room.appendChild(document.createTextNode("Estancia"));
    tr.appendChild(room);

    var promo = document.createElement("th");
    promo.appendChild(document.createTextNode("Promocion"));
    tr.appendChild(promo);

    var subtotal = document.createElement("th");
    subtotal.appendChild(document.createTextNode("Subtotal"));
    tr.appendChild(subtotal);

    thead.appendChild(tr);
    table.appendChild(thead);

    var tbody = document.createElement("tbody");
    tbody.setAttribute("class", "dates");


    var row = document.createElement("tr");
    row.setAttribute("class", "precio-final");
    var total = document.createElement("td");
    total.appendChild(document.createTextNode("Total:"));
    row.appendChild(total)

    var span = document.createElement("td");
    row.appendChild(span);

    var span = document.createElement("td");
    row.appendChild(span);

    var valtotal = document.createElement("td");
    valtotal.appendChild(document.createTextNode("0.0"));

    row.appendChild(valtotal);

    tbody.appendChild(row);
    table.appendChild(tbody);

    container.appendChild(table);

    document.querySelector("#tab-4").appendChild(container);

    add_new_price_per_day();
}

function recalculate_price_per_day(e) {
    var parent = e.target.parentNode.parentNode;

    var roomPrice = parseFloat(parent.querySelector(".room-price").value);
    if (isNaN(roomPrice)) {
        roomPrice = 0.0;
    }
    var promoPrice = parseFloat(parent.querySelector(".promo-price").value);

    if (isNaN(promoPrice)) {
        promoPrice = 0.0;
    }
    parent.querySelector(".subtotal-price").value = (roomPrice - promoPrice).toFixed(2);

    var total = 0.0;
    for (i of parent.parentNode.querySelectorAll(".subtotal-price")) {
        if (!isNaN(i.value) && i.value != "") {
            total += parseFloat(i.value);
        }

    }

    parent
        .parentNode
        .querySelector(".precio-final")
        .querySelectorAll("td")[3]
        .innerHTML = format_number(total);

    var id = parent.closest("div[id ^= 'day_price_']").id;
    id = parseInt(id.replace("day_price_", ""));

    document.querySelector("#room-entry-" + id + " .room-price").value = format_number(total);
    recalculate_prices();
}

function recalculate_total_forfait_price(daily_price_forfait,last_daily_price, verbose = true){
    var regex = /^([0-9])+[\.]{0,1}([0-9])*$/;

    var forfait_wrapper = daily_price_forfait.closest(".wrapper-forfait");
    var startDate = forfait_wrapper.find(".forfait_startDate").val();
    var endDate = forfait_wrapper.find(".forfait_endDate").val();

    let start = startDate.split("/");
    let end = endDate.split("/");

    var firstDate = Date.UTC(start[2],start[1]-1,start[0]);
    var lastDate = Date.UTC(end[2],end[1]-1,end[0]);
    var dif = lastDate - firstDate;
    if(dif >= 0 && daily_price_forfait.val() >= 0){
        var days = Math.floor(dif / (1000 * 60 * 60 * 24)) + 1;
        var new_total_price = parseFloat((((daily_price_forfait.val()) * days) * 100) / 100);

        forfait_wrapper.find(".price_total_per_person_forfait").val(new_total_price.toFixed(2));
        return new_total_price;
    } else {
        if (!regex.test(daily_price_forfait.val())) {
            if (verbose){
                daily_price_forfait.val(last_daily_price);
                return show_alert(T_ALERT_ADD_DAILY_PRICE_FORFAIT, "Datos incorrectos");
            };
        };
    }
}

function update_total_price_forfait(){
    var accumulated_total_price = 0;
    $(".price_total_per_person_forfait").each(function (){
        var current_total_price_person = parseFloat($(this).val());
        if (!Number.isNaN(current_total_price_person)){
            accumulated_total_price += parseFloat($(this).val());
        }

    })
    $(".forfait-pvp").val(format_number(accumulated_total_price));
}

function fix_room_mods() {
    var price_per_day = document.querySelectorAll("div[id ^= 'day_price_']");
    for (i of price_per_day) {
        var id = parseInt(i.id.replace("day_price_", ""));
        if (document.querySelector("#room-entry-" + id) == null) {
            var el = document.querySelector("#" + i.id)
            el.parentNode.removeChild(el)
        }
    }
}

function remove_price_per_day(el) {
    var id = el[0].closest("div[id ^= 'room-entry-']").id;
    id = parseInt(id.replace("room-entry-", ""));

    $("#day_price_" + id).remove();
}

function show_price_per_day(el) {
    if (el[0].value == "all") {
        $(".details div[id ^= 'day_price_']").show();
    } else {
        $(".details div[id ^= 'day_price_']").hide();

        $("div#day_price_" + el[0].value).show();
    }
}

function add_new_price_per_day() {
    var total_price_reservation = parseFloat(0)
    var recalculate_price_method = $("#recalculate_price_method").val();
    $(".dates").each((index, val) => {

        var checkin = $("#checkin").val();
        var dateParts = checkin.split("/");
        checkin = new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0]);

        var checkout = $("#checkout").val();
        var dateParts = checkout.split("/");
        checkout = new Date(new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0]) - 1);


        var newlist = [];
        do {
            var fechaStr = (String(checkin.getDate()).padStart(2, '0') + "/" + String((checkin.getMonth() + 1)).padStart(2, '0') + "/" + checkin.getFullYear())

            newlist.push(fechaStr)
            checkin.setDate(checkin.getDate() + 1);

        } while (Date.parse(checkin) <= Date.parse(checkout));

        var dates = $(val).find("tr:not(.precio-final)");

        oldlist = [];
        for (i of dates) {
            oldlist.push(i.querySelectorAll("td")[0].innerHTML);
        }

        for (i of newlist) {
            if (!oldlist.includes(i)) {

                var id = val.closest("div[id ^= 'day_price_']").id;
                id = id.replace("day_price_", "");

                var row = document.createElement("tr");

                var tddate = document.createElement("td");
                tddate.appendChild(document.createTextNode(i));
                row.appendChild(tddate);

                var tdroom = document.createElement("td");
                var inputroom = document.createElement("input");

                inputroom.setAttribute("class", "room-price");
                inputroom.setAttribute("name", "room_price_day[" + id + "][" + i + "][room]");
                inputroom.addEventListener("change", recalculate_price_per_day, true);
                inputroom.setAttribute("style", "width: 150px; height: 20px;");
                tdroom.appendChild(inputroom);
                row.appendChild(tdroom);

                var tdpromo = document.createElement("td");
                var inputpromo = document.createElement("input");
                inputpromo.setAttribute("class", "promo-price");
                inputpromo.setAttribute("name", "room_price_day[" + id + "][" + i + "][promo]");
                inputpromo.addEventListener("change", recalculate_price_per_day, true);
                inputpromo.setAttribute("style", "width: 150px; height: 20px;");
                tdpromo.appendChild(inputpromo);
                row.appendChild(tdpromo);

                var tdtotal = document.createElement("td");
                var inputtotal = document.createElement("input");
                inputtotal.setAttribute("name", "room_price_day[" + id + "][" + i + "][subtotal]")
                inputtotal.setAttribute("class", "subtotal-price");
                inputtotal.setAttribute("readonly", "true");
                inputtotal.setAttribute("style", "width: 150px; height: 20px;");
                tdtotal.appendChild(inputtotal);
                row.appendChild(tdtotal);

                val.insertBefore(row,
                    val.querySelector("tr.precio-final"));

            }
        }

        for (i of val.querySelectorAll("tr:not(.precio-final)")) {
            if (!newlist.includes(i.querySelector("td").innerHTML)) {
                i.parentNode.removeChild(i);
            }
        }

        var divider = $(val).find("tr:not(.precio-final)").length;
        var id = val.parentNode.parentNode.id;

        if (id != null && id != "") {
            id = parseInt(id.replace("day_price_", ""));
            var price = parseFloat(val.querySelectorAll(".precio-final td")[3].innerHTML);
            if(price == 0){
                var price = parseFloat(document.querySelector("#room-entry-" + id + " .room-price").value);
            }
            if (recalculate_price_method == 'manual' || oldlist.length > newlist.length ){
                var total_price_room = parseFloat(0);
                $(val).find(".subtotal-price").each((i, v) => {
                    if (!isNaN(parseFloat($(v).val()))){
                        total_price_room += parseFloat($(v).val());
                    }
                });
                var old_subtotal = val.querySelectorAll(".precio-final td")[3].innerHTML
                if ((Math.abs(total_price_room - old_subtotal)) < 0.02){
                    total_price_room = old_subtotal
                }
                val.querySelectorAll(".precio-final td")[3].innerHTML = format_number(total_price_room);
                set_new_price_to_rooms(index, total_price_room)
                total_price_reservation += parseFloat(total_price_room)
                $(val).find(".promo-price").each((i, v) => {
                    if ($(v).val() == "0" || $(v).val() == "" ){
                        $(v).val("0.00");
                    }
                });

            }
            else{
                newprice = price / divider;
                $(val).find(".room-price").each((i, v) => {
                    $(v).val(format_number(newprice));
                });

                $(val).find(".promo-price").each((i, v) => {
                    $(v).val("0.00");
                });

                $(val).find(".subtotal-price").each((i, v) => {
                    $(v).val(format_number(newprice));
                });

                val.querySelectorAll(".precio-final td")[3].innerHTML = format_number(newprice * divider);
                total_price_reservation += (newprice * divider)

            }
        }
    });
    $(".priceTotal").val(format_number(total_price_reservation));
}

function set_new_price_to_rooms(index, total_price_room){
    $('input[name="room[' + (index+1) + '][price]"]').val(format_number(total_price_room));
}

function recalculate_inputs_needed(control){

    var entryItem = control.closest('.entry-item');
    var byhours_input = '';
    entryItem.find('.input_wrapper').each(function() {
        if ($(this).attr("class").includes("-input")) {
            $(this).hide();
            if ($(this).attr("class").includes("byHours-input")) {
                byhours_input = $(this).find('select');
                byhours_input.empty();
            }

        }
    });

    var required_inputs = control.find('option:selected').attr('required_inputs');
    if (required_inputs !== undefined) {
        let jsonString = required_inputs.replace(/'/g, '"');
        required_inputs = JSON.parse(jsonString);
        // var new_availability = $('#all_services2').val();
        var parent_ = control.parent().parent().parent();
        for (let i = 0; i < required_inputs.length; i++) {
            var element = parent_.find('.' + required_inputs[i] + '-input');
            if (required_inputs[i] == 'byHours') {
                var byHours = control.find('option:selected').attr('full_service');
                let jsonString_ = byHours.replace(/'/g, '"');
                byHours = JSON.parse(jsonString_);
                $.each(byHours, function (hour, price) {
                    if (price !== 'null'){
                        byhours_input.append(get_option_to_append_hour(hour, price));
                    }
                });
            }
            element.show();
        }
        parent_.find('.price-input').show();
        parent_.find('.with_date_selector-input').show();
    }
}
function get_option_to_append_hour(hour, price){
    return '<option value="'+price+'">'+hour+'</option>';
}

function recalculate_prices() {


    if ($(".chk_rec_supplements").prop('checked')) {

        var pvp_total_sup = parseFloat(0);

        $(".services>.wrapper-services").each(function(index) {
            var pvp = $(this).find(".service-pvp").val();
            var regex = /^([0-9])+[\.,]{0,1}([0-9])*$/;
            if (!regex.test(pvp)) {
                pvp = 0;
            }
            var quantity = $(this).find(".service-quantity").val();
            if (!regex.test(quantity)) {
                quantity = 0;
            }
            var days = $(this).find(".service-days").val();
            if (!regex.test(days)) {
                days = 0;
            }
            var subtotal = parseFloat(pvp);

            pvp_total_sup = pvp_total_sup + parseFloat(subtotal);
        });

        $(".services2>.wrapper-services>.wrapper-services").not('.wrapper-injector').each(function(index){
            var entry_item = $(this).find('.entry-item');
            var pvp = entry_item.find('.service-pvp').find('.input').find('[name*="[pvp]"]').val();
            var regex = /^([0-9])+[\.,]{0,1}([0-9])*$/;
            if (!regex.test(pvp)) {
                pvp = 0;
            }
            var subtotal = parseFloat(pvp);

            pvp_total_sup = pvp_total_sup + parseFloat(subtotal);
        });

        $(".priceSupplements").val(pvp_total_sup);

    }

    if ($(".chk_rec_total").prop('checked')) {

        var pvp_total_estancia = 0;

        $(".rooms").find(".room-price").each(function(index) {

            var pvp = $(this).val();
            if (pvp == "") pvp = 0;
            var forfait_total_price = 0;
            if($(".forfait-pvp").length > 0){
                forfait_total_price = parseFloat($(".forfait-pvp").val());
            }
            pvp_total_estancia = parseFloat(pvp_total_estancia) + parseFloat(pvp) + forfait_total_price;
        });


        if (!$(".priceTotal")[0].classList.contains("trigger")) {
            if (parseFloat($(".priceTotal").val()) != parseFloat(pvp_total_estancia)) {
                $(".priceTotal").val(pvp_total_estancia);

            }
        } else {
            //$(".priceTotal")[0].classList.remove("trigger");
        }

    }

}

function recalculate_price_room(recalculate_price_room_button){
    let session_key = $("input[name='sessionKey']").val();
    let wrapper_room = recalculate_price_room_button.closest($(".wrapper-rooms"));
    let index_room = wrapper_room.attr("id").split("room-entry-")[1]
    let input_price = wrapper_room.find($(".room-price"));
    let startDate = $("#checkin").val();
    let endDate = $("#checkout").val();
    let adults = wrapper_room.find($(".room-adults :selected")).text();
    let kids = wrapper_room.find($(".room-kids :selected")).text();
    let babies = wrapper_room.find($(".room-babies :selected")).text();
    let identifier = $("input[name=identifier]").val();
    let room_key = wrapper_room.find($(".room-key")).val();
    let rate_key = wrapper_room.find($(".room-rate")).val();
    let board_key = wrapper_room.find($(".room-board")).val();
    let current_price = wrapper_room.find($(".room-price")).val();
    let ignore_availability_and_restrictions = wrapper_room.find("#ignore_room_availability_and_restrictions").is(":checked");
    let geolocation = wrapper_room.find(".geolocation-key").val();


    let real_hotel_code = $("#real_hotel_code").val();
    let origin_hotel_code = $("#origin_hotel_code").val();

    let final_data = {
        "startDate": startDate,
        "endDate": endDate,
        "adults": adults,
        "kids": kids,
        "babies": babies,
        "identifier": identifier,
        "room_key": room_key,
        "rate_key": rate_key,
        "board_key": board_key,
        "current_price": current_price,
        "ignore_availability_and_restrictions": ignore_availability_and_restrictions,
        "sessionKey": session_key,
        "real_hotel_code": real_hotel_code,
        "origin_hotel_code": origin_hotel_code,
        "geolocation": geolocation
    }
    $.ajax({
        url: "/reservation/recalculate_price_room/",
        data: final_data,
        contentType: "application/json",
        type: 'GET',
        success: function(result) {
            if (result.status != "error"){
                let price = result.price;
                let price_status = result.status
                input_price = recalculate_price_room_button.closest($(".wrapper-rooms")).find($(".room-price"));
                input_price.val(format_number(price));
                let parent_div = recalculate_price_room_button.parent();
                parent_div.find($(".recalculated_message")).text(price_status);
                parent_div.find($(".recalculated_message")).fadeIn();
                if (result.recalculate_room_check){
                    recalculate_counters();
                    recalculate_prices();
                    input_price.keyup();
                    formats_control();

                    let promotions = result.promotions
                    if (promotions.length > 0){
                        $.each(promotions, function(index, promo) {
                            if(check_incoming_recalculated_promotion(promo) == false){
                                add_entry_promo("", promotion=promo);
                            }

                        });
                    }

                    set_recalculated_prices_per_day(result.final_prices_per_day, result.original_prices_per_day, result.promos_per_day, index_room, price);
                }
            };
        }
    });
}


function recalculate_price_service(recalculate_price_service_button){
    let session_key = $("input[name='sessionKey']").val();
    let wrapper_service = recalculate_price_service_button.closest($(".wrapper-services"));
    if (wrapper_service.length === 0){
        wrapper_service = recalculate_price_service_button.closest($(".wrapper-injector"));
    }
    let service_key = wrapper_service.find("[class*='service-key']").val();
    let service_name = wrapper_service.find($(":selected")).html();
    let input_price = wrapper_service.find("[class*='service-pvp']").val();

    let startDate = $("#checkin").val();
    let quantity = wrapper_service.find("[class*='service-quantity']").val();
    let days = wrapper_service.find("[class*='service-days']").val();
    let endDate = $("#checkout").val();
    let real_hotel_code = $("#real_hotel_code").val();

    let final_data = {
        "startDate": startDate,
        "endDate": endDate,
        "service_key": service_key,
        "service_name": service_name,
        "quantity": quantity,
        "days": days,
        "current_price": input_price,
        "sessionKey": session_key,
        "real_hotel_code": real_hotel_code
    }
    $.ajax({
        url: "/reservation/recalculate_price_service/",
        data: final_data,
        contentType: "application/json",
        type: 'GET',
        success: function(result) {
            if (result.status != "error"){
                let price = result.price;
                let price_status = result.status
                input_price = recalculate_price_service_button.closest($(".wrapper-services")).find("[class*='service-pvp']");
                input_price.val(format_number(price));
                let parent_div = recalculate_price_service_button.parent();
                parent_div.find($(".recalculated_message")).text(price_status);
                parent_div.find($(".recalculated_message")).fadeIn();
                recalculate_counters();
                recalculate_prices();
                input_price.keyup();
                formats_control();

                ;
            };
        }
    });
}



function check_incoming_recalculated_promotion(promotion_key){
    let promo_exist = false
    $(".promo-key").each(function(){
        if ($(this).find(":selected").val() == promotion_key){
            promo_exist = true;
        }
    });
    return promo_exist;
}

function set_recalculated_prices_per_day(final_prices_per_day, original_prices_per_day, promos_per_day, index_room, price){
    let wrapper_price_per_day = $("#day_price_" + index_room);
    let body_table = wrapper_price_per_day.find(".zebra_table").find("tbody");
    body_table.find($(".precio-final")).find($("td:last-child")).text(price);
    $.each(body_table.find("tr"), function(index, tr_day) {
        $(this).find(".room-price").val(original_prices_per_day[index]);
        if (promos_per_day.length > 0) {
            $(this).find(".promo-price").val(promos_per_day[index]);
        }
        $(this).find(".subtotal-price").val(final_prices_per_day[index]);
    });
}
function recalculate_prices_with_forfait(last_forfait_total_price){
    var last_forfait_pvp = parseFloat(last_forfait_total_price);
    var current_forfait_pvp =  parseFloat($(".forfait-pvp").val());
    var price_total_estancia = parseFloat($(".priceTotal").val());
    var new_price_total = 0;
    if (last_forfait_pvp > current_forfait_pvp){
        new_price_total = price_total_estancia - (last_forfait_pvp - current_forfait_pvp);
        $(".priceTotal").val(format_number(new_price_total));
    } else{
        new_price_total = price_total_estancia + (current_forfait_pvp - last_forfait_pvp);
        $(".priceTotal").val(format_number(new_price_total));
    }
}
function formats_control() {

    $(".room-price").each(function(index) {
        format_control_number($(this));
    });
    $(".service-pvp").each(function(index) {
        format_control_number($(this));
    });

    $(".service-pvp").each(function(index) {
        format_control_number($(this));
    });

    $(".forfait-pvp").each(function(index) {
        format_control_number($(this));
    });

}

function add_entry_service(control) {
    var service_type_ = get_service_type(control);
    if (service_type_ == 'service'){
        service_key = is_service_valid(true, control);
    }else{
        service_key = is_service2_valid(true, control);
    }
    if (service_key == '') {
        return;
    }
    var services_type_ = get_services_type(control);
    var next_index = $("#num_"+service_type_).val();
    if(Number.isNaN(next_index) || next_index === undefined){
        next_index = $("#num_services").val();
    }
    next_index = parseInt(next_index) + 1;
    $("#num_"+service_type_).val(next_index);
    // var next_index = $("#num_services").val();
    // if(Number.isNaN(next_index) || next_index === undefined){
    //     next_index = $("#num_services").val();
    // }
    // next_index = parseInt(next_index) + 1;
    $("#num_services").val(next_index);

    var template = $('.'+service_type_+'-template').children().first().clone();
    if (!template){
        template = $('.service2_room_-template').children().first().clone();
    }
    template.attr('id', service_type_+"-entry-" + next_index);

    template.find("."+service_type_+"-guest_type").attr('name', service_type_+'[' + next_index + '][guest_type]');
    template.find("."+service_type_+"-subtype").attr('name', service_type_+'[' + next_index + '][subtype]');
    template.find("."+service_type_+"-quantity").attr('name', service_type_+'[' + next_index + '][quantity]');
    template.find("."+service_type_+"-days").attr('name', service_type_+'[' + next_index + '][days]');
    template.find("."+service_type_+"-date").attr('name', service_type_+'[' + next_index + '][date]');
    template.find("."+service_type_+"-pvp").attr('name', service_type_+'[' + next_index + '][pvp]');
    template.find("."+service_type_+"-template").attr('data-service', next_index);
    template.find("."+service_type_+"-key").attr('name', service_type_+'[' + next_index + '][key]');
    template.find("."+service_type_+"-name").attr('name', service_type_+'[' + next_index + '][name]');
    template.find("."+service_type_+"-amount").attr('name', service_type_+'[' + next_index + '][amount]');
    template.find("."+service_type_+"-customer_name").attr('name', service_type_+'[' + next_index + '][customer_name]');
    template.find("."+service_type_+"-service_key").attr('name', service_type_+'[' + next_index + '][service_key]');
    template.find("."+service_type_+"-ticket_id").attr('name', service_type_+'[' + next_index + '][ticket_id]');
    template.find("."+service_type_+"-ticket_name").attr('name', service_type_+'[' + next_index + '][ticket_name]');
    template.find(".recalculate-price-"+service_type_).attr('id', 'recalculatePriceService' + next_index);



    var ticket_id = $("."+service_type_+"-ticket_id-injector").val();
    var ticket_name = $("."+service_type_+"-ticket_name-injector").val();
    var service_pvp = $("."+service_type_+"-pvp-injector").val();
    var service_amount = $("."+service_type_+"-amount-injector").val();
    var service_guest_type = '';
    if ($("."+service_type_+"-guest_type-injector").is(':visible')){
        service_guest_type = $("."+service_type_+"-guest_type-injector").val();
    }
    var service_subtype = $("."+service_type_+"-subtype-injector").val();
    var service_days = $("."+service_type_+"-days-injector").val();
    var service_name = $("."+service_type_+"-key-injector :selected").text();
    var customer_name = $("."+service_type_+"-customer_name-injector").val();
    var hour_full_select = $("."+service_type_+"-hour-injector").children().clone();
    var date_full_select = $("."+service_type_+"-date-injector").children().clone();
    var service_key_select = $("."+service_type_+"-key-injector").children().clone();
    var hour = $("."+service_type_+"-hour-injector :selected").text();
    var date = '';
    var date_text = '';
    if ($("."+service_type_+"-date-injector").is(':visible')){
        date = $("."+service_type_+"-date-injector").val();
        date_text = $("."+service_type_+"-date-injector :selected").text();
    }

    var service_key = $("."+service_type_+"-key-injector :selected").val();

    var types =  ['to', 'from'];
    for (let i = 0; i < types.length; i++) {
        var flight_number_element = $("."+service_type_+"-transfer_flight_number-"+types[i]+"-injector");
        var airline_element = $("."+service_type_+"-transfer_airline-"+types[i]+"-injector");
        var landing_time_element = $("."+service_type_+"-transfer_landing_time-"+types[i]+"-injector");

        if (flight_number_element.is(":visible") && airline_element.is(":visible") && landing_time_element.is(":visible")) {
            template.find("." + service_type_ + "-transfer_flight_number-" + types[i]).attr('name', service_type_ + '[' + next_index + '][transfer_flight_number-' + types[i] + ']');
            template.find("." + service_type_ + "-transfer_airline-" + types[i]).attr('name', service_type_ + '[' + next_index + '][transfer_airline-' + types[i] + ']');
            template.find("." + service_type_ + "-transfer_landing_time-" + types[i]).attr('name', service_type_ + '[' + next_index + '][transfer_landing_time-' + types[i] + ']');


            var transfer_flight_number = flight_number_element.val();
            var transfer_airline = airline_element.val();
            var transfer_landing_time = landing_time_element.val();

            template.find("." + service_type_ + "-transfer_flight_number-" + types[i]).val(transfer_flight_number);
            template.find("." + service_type_ + "-transfer_airline-" + types[i]).val(transfer_airline);
            template.find("." + service_type_ + "-transfer_landing_time-" + types[i]).val(transfer_landing_time);
        }
    }
    var transfer_comments = $("."+service_type_+"-transfer_comments-injector").val();
    $("."+service_type_+"-transfer_comments-injector").val('');
    template.find("."+service_type_+"-transfer_comments").val(transfer_comments);

    template.find("."+service_type_+"-transfer_comments").attr('name', service_type_+'[' + next_index + '][transfer_comments]');


    template.find("."+service_type_+"-amount").val(service_amount);
    template.find("."+service_type_+"-guest_type").val(service_guest_type);
    if (ticket_id || ticket_name) {
        template.find("."+service_type_+"-guest_type").empty().append('<option value="adult">adult</option>');
    }
    template.find("."+service_type_+"-subtype").val(service_subtype);
    template.find("."+service_type_+"-pvp").val(service_pvp);
    template.find("."+service_type_+"-days").val(service_days);
    template.find("."+service_type_+"-name").val(service_name);
    template.find("."+service_type_+"-customer_name").val(customer_name);
    template.find("."+service_type_+"-service_key").val(service_key);
    var ticket_id_select = $("." + service_type_ + "-ticket_id-injector").children().clone();
    template.find("." + service_type_ + "-ticket_id").empty().append(ticket_id_select);
    template.find("." + service_type_ + "-ticket_id option").filter(function() {
        return $(this).val() === ticket_id;
    }).prop('selected', true);
    template.find("."+service_type_+"-ticket_name").val(ticket_name);
    template.find("."+service_type_+"-hour").empty();
    if (hour){
        template.find("."+service_type_+"-hour").append(hour_full_select);
    }
    template.find("."+service_type_+"-date").empty();
    if (date){
        template.find("."+service_type_+"-date").append(date_full_select);
        template.find("." + service_type_ + "-date option").filter(function() {
            return $(this).text() === date_text;
        }).prop('selected', true);
    }
    template.find("."+service_type_+"-key").empty();
    template.find('.'+service_type_+'-key').append(service_key_select);


    template.find("."+service_type_+"-hour option").filter(function() {
        return $(this).text() === hour;
    }).prop('selected', true);

    template.find("."+service_type_+"-key option").filter(function() {
        return $(this).text() === key;
    }).prop('selected', true);

    template.find("."+service_type_+"-date option").filter(function() {
        return $(this).text() === date;
    }).prop('selected', true);


    var lista = {
        "pvp": service_pvp,
        "amount": service_amount,
        "guest_type": service_guest_type,
        "subtype": service_subtype,
        "customer_name": customer_name,
        "hour": hour,
        "date": date,
        "ticket_id": ticket_id,
        "ticket_name": ticket_name,
    };
    for (var key in lista) {
        if (lista.hasOwnProperty(key)) {
            if (lista[key] !== undefined && lista[key] !== ""){
                template.find("."+service_type_+"-"+key).closest('.input_wrapper').show();
            }
        }
    }
    template.find('[class*="service_"][class*="-transfer_"]').each(function(){
        if ($(this).val() !== undefined && $(this).val() !== "" ){
            $(this).closest(".input_wrapper").show();
        }
    });

    template.find(".counter-"+service_type_+" > .label").html("Suplemento #" + next_index);

    template.find("."+service_type_+"-key").val(service_key).change();

    template.find('[class*="service_"][class*="button_remove"]').click(function(e) {
        remove_entry_service($(this));
    });

    template.find('[class*="service_"][class*="button_remove"]').attr('id', next_index);

    template.find("."+service_type_+"-quantity").change(function(e) {
        recalculate_prices();
    });

    template.find("."+service_type_+"-days").change(function(e) {
        recalculate_prices();
    });

    template.find("."+service_type_+"-pvp").keyup(function(e) {
        recalculate_prices();
    });
    template.find("."+service_type_+"-date").change(function(e) {
        recalculate_prices();
    });
    template.find("."+service_type_+"-hour").change(function(e) {
        recalculate_prices();
    });
    template.find("[class*='_button_calculate_additional_service_price']").click(function(e) {
        calculate_additional_service_price($(this));
        // recalculate_price_service2($(this), e);
    });

    template.find(".recalculate-price-"+service_type_).click(function() {
        let recalculate_price_service_button = $(this);
        recalculate_price_service(recalculate_price_service_button);
    });

    template.css({ display: "none" });
    if (service_type_ == 'service'){
        template.appendTo('.services');
    }else{
        template.appendTo('.'+service_type_);
    }
    template.show("fast");

    $("."+service_type_+"-key-injector").val('').change();
    $("."+service_type_+"-type-injector").val('').change();
    $("."+service_type_+"-subtype-injector").val('').change();
    $("."+service_type_+"-days-injector").val('').change();
    $("."+service_type_+"-quantity-injector").val('').change();
    $("."+service_type_+"-pvp-injector").val('');
    $("."+service_type_+"-customer_name-injector").val('');
    $("."+service_type_+"-amount-injector").val('1');
    // $("."+service_type_+"-pvp-injector").val('');

    recalculate_counters(services_type_);
    recalculate_prices();
    formats_control();

    return;
}

function add_entry_adult_forfait(control) {
    person_key  = is_adult_valid(true);
    if (person_key == "" ) {
        return;
    }

    if (person_key == "adult"){
        var next_index = parseInt($("#num_forfait_adults").val()) + 1;
        $("#num_forfait_adults").val(next_index);
        var template = $('.forfait-adult-template').children().first().clone();
        template.attr('id', "forfait-adult-entry-" + next_index);

        template.find(".forfait_startDate").attr('name', 'forfait_adult[' + next_index + '][startDate]');
        template.find(".forfait_endDate").attr('name', 'forfait_adult[' + next_index + '][endDate]');
        template.find(".price_per_day_forfait").attr('name', 'forfait_adult[' + next_index + '][price_day]');
        template.find(".price_total_per_person_forfait").attr('name', 'forfait_adult[' + next_index + '][price_total]');

        template.find(".forfait_startDate").attr('id', 'forfait_startDate_adult' + next_index);
        template.find(".forfait_endDate").attr('id', 'forfait_endDate_adult' + next_index);

        var forfait_startDate = $(".person-forfait-startDate-injector").val();
        var forfait_endDate = $(".person-forfait-endDate-injector").val();
        var forfait_daily_price = $(".person-forfait-daily-price-injector").val();
        var forfait_total_adult_price = $(".person-forfait-total-price-injector").val();

        var start_forfait = forfait_startDate.split("/");
        var ts_start_forfait = new Date(start_forfait[2], start_forfait[1] - 1, start_forfait[0]);
        template.find(".forfait_startDate").attr("placeholder", $.datepicker.formatDate("dd/mm/yy", ts_start_forfait));
        template.find(".forfait_startDate").attr("value", $.datepicker.formatDate("dd/mm/yy", ts_start_forfait));

        var end_forfait = forfait_endDate.split("/");
        var ts_end_forfait = new Date(end_forfait[2], end_forfait[1] - 1, end_forfait[0]);
        template.find(".forfait_endDate").attr("placeholder", $.datepicker.formatDate("dd/mm/yy", ts_end_forfait));
        template.find(".forfait_endDate").attr("value", $.datepicker.formatDate("dd/mm/yy", ts_end_forfait));

        template.find(".price_per_day_forfait").val(forfait_daily_price);
        template.find(".price_total_per_person_forfait").val(forfait_total_adult_price);

        template.find(".wrapper-injector adult_forfait_wrapper > input hasDatepickerForfait")
        template.find(".counter-forfait > .label").html("Adulto FORFAIT #" + next_index);

        template.find(".forfait_adult_button_remove").attr('data-forfait', next_index);

        template.find(".forfait_adult_button_remove").click(function(e) {
            remove_entry_adult_forfait($(this));
        });

        template.find(".forfait_startDate, .forfait_endDate").change(function(e) {
            set_forfait_date();
        });

        template.find(".price_per_day_forfait").keyup(function(e) {
            var daily_price_forfait = $(this);
            var last_forfait_total_price = $(".forfait-pvp").val();
            recalculate_total_forfait_price(daily_price_forfait);
            update_total_price_forfait();
            recalculate_prices_with_forfait(last_forfait_total_price);
        });

        template.find(".price_per_day_forfait").on("blur", function (){
            if ($(this).val() == 0){
                show_alert(T_ALERT_ADD_DAILY_PRICE_FORFAIT, "Datos incorrectos");
                $(this).val(last_daily_price);
            };
        });

        template.css({ display: "none" });
        template.appendTo('.forfait');
        template.show("fast");

        $(".person-forfait-daily-price-injector").val('').change();
        $(".person-forfait-total-price-injector").val('').change();

        var last_forfait_total_price = $(".forfait-pvp").val();
        update_total_price_forfait();
        recalculate_prices_with_forfait(last_forfait_total_price);
        recalculate_counters();
        formats_control();
    } else{
        var next_index = parseInt($("#num_forfait_kids").val()) + 1;
        $("#num_forfait_kids").val(next_index);
        var template = $('.forfait-kid-template').children().first().clone();

        template.attr('id', "forfait-kid-entry-" + next_index);

        template.find(".forfait_startDate").attr('name', 'forfait_kid[' + next_index + '][startDate]');
        template.find(".forfait_endDate").attr('name', 'forfait_kid[' + next_index + '][endDate]');
        template.find(".price_per_day_forfait").attr('name', 'forfait_kid[' + next_index + '][price_day]');
        template.find(".price_total_per_person_forfait").attr('name', 'forfait_kid[' + next_index + '][price_total]');

        template.find(".forfait_startDate").attr('id', 'forfait_startDate_kid' + next_index);
        template.find(".forfait_endDate").attr('id', 'forfait_endDate_kid' + next_index);

        var forfait_startDate = $(".person-forfait-startDate-injector").val();
        var forfait_endDate = $(".person-forfait-endDate-injector").val();
        var forfait_daily_price = $(".person-forfait-daily-price-injector").val();
        var forfait_total_adult_price = $(".person-forfait-total-price-injector").val();

        var start_forfait = forfait_startDate.split("/");
        var ts_start_forfait = new Date(start_forfait[2], start_forfait[1] - 1, start_forfait[0]);
        template.find(".forfait_startDate").attr("placeholder", $.datepicker.formatDate("dd/mm/yy", ts_start_forfait));
        template.find(".forfait_startDate").attr("value", $.datepicker.formatDate("dd/mm/yy", ts_start_forfait));

        var end_forfait = forfait_endDate.split("/");
        var ts_end_forfait = new Date(end_forfait[2], end_forfait[1] - 1, end_forfait[0]);
        template.find(".forfait_endDate").attr("placeholder", $.datepicker.formatDate("dd/mm/yy", ts_end_forfait));
        template.find(".forfait_endDate").attr("value", $.datepicker.formatDate("dd/mm/yy", ts_end_forfait));

        template.find(".price_per_day_forfait").val(forfait_daily_price);
        template.find(".price_total_per_person_forfait").val(forfait_total_adult_price);

        template.find(".wrapper-injector adult_forfait_wrapper > input hasDatepickerForfait")
        template.find(".counter-forfait > .label").html("Niño FORFAIT #" + next_index);

        template.find(".forfait_kid_button_remove").attr('data-forfait', next_index);

        template.find(".forfait_kid_button_remove").click(function(e) {
            remove_entry_kid_forfait($(this));
        });

        template.find(".forfait_startDate,.forfait_endDate").change(function(e) {
            set_forfait_date();
        });

        template.find(".price_per_day_forfait").keyup(function(e) {
            var daily_price_forfait = $(this);
            var last_forfait_total_price = $(".forfait-pvp").val();
            recalculate_total_forfait_price(daily_price_forfait);
            update_total_price_forfait();
            recalculate_prices_with_forfait(last_forfait_total_price);
        });
        template.find(".price_per_day_forfait").on("blur", function (){
            if ($(this).val() == 0){
                show_alert(T_ALERT_ADD_DAILY_PRICE_FORFAIT, "Datos incorrectos");
                $(this).val(last_daily_price);
            };
        });

        template.css({ display: "none" });
        template.appendTo('.forfait');
        template.show("fast");

        $(".person-forfait-daily-price-injector").val('').change();
        $(".person-forfait-total-price-injector").val('').change();

        var last_forfait_total_price = $(".forfait-pvp").val();
        update_total_price_forfait();
        recalculate_prices_with_forfait(last_forfait_total_price);
        recalculate_counters();
        formats_control();
    }

    var checkin = $("#checkin").val();
    var dateParts = checkin.split("/");
    checkin = new Date(new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0]) + 1);

    var checkout = $("#checkout").val();
    var dateParts = checkout.split("/");
    checkout = new Date(new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0]) + 1);

    template.find(".forfait_startDate, .forfait_endDate")
        .removeClass('hasDatepicker')
        .datepicker({
        changeMonth: true,
        changeYear: true,
        yearRange: "-80:+20",
        minDate: checkin,
        maxDate: checkout,
        showAnim: "clip"
    });
    return;

}
function ini_events_promo_entry(){
    $('.promo_button_add').click(function(e) {
        add_entry_promo();
    });
    $('.promo_button_remove').click(function(e) {
        remove_entry_promo($(this));
    });
}

function ini_events_room_entry() {

    $('.room_button_add').click(function(e) {
        confirm_entry_room($(this));
    });

    $('.room_button_remove').click(function(e) {
        remove_price_per_day($(this));
        remove_entry_room($(this));
    });

    $('.room-key-injector').change(function(e) {
        var room_key = is_room_valid(false);
    });
    $('.room-adults-injector').change(function(e) {
        var room_key = is_room_valid(false);
    });
    $('.room-kids-injector').change(function(e) {
        var room_key = is_room_valid(false);
    });
    $('.room-babies-injector').change(function(e) {
        var room_key = is_room_valid(false);
    });
    $('.room-rate-injector').change(function(e) {
        var room_key = is_room_valid(false);
    });
    $('.room-board-injector').change(function(e) {
        var room_key = is_room_valid(false);
    });
    $('.room-price-injector').keyup(function(e) {
        var room_key = is_room_valid(false);
    });
    $('.room-key-injector').val('').change();

    $(".room-price").keyup(function(e) {
        recalculate_prices();
    });
    $('[name*="service_"][name*="[pvp]"]').keyup(function(e) {
        recalculate_prices(e);
    });


    $(".recalculate-price-room").click(function() {
        let recalculate_price_room_button = $(this);
        recalculate_price_room(recalculate_price_room_button);
    });

    $(".recalculate-price-service").click(function() {
        let recalculate_price_service_button = $(this);
        recalculate_price_service(recalculate_price_service_button);
    });

    $(".room-price").click(function(e) {
        format_control_number($(this));
    });

    $(".price_per_day_forfait").focus(function() {
        var last_daily_price = $(this).val();
        $(this).keyup(function() {
            var daily_price_forfait = $(this);
            var last_forfait_total_price = $(".forfait-pvp").val();
            recalculate_total_forfait_price(daily_price_forfait, last_daily_price);
            update_total_price_forfait();
            recalculate_prices_with_forfait(last_forfait_total_price);
        });
        $(this).on("blur", function (){
            if ($(this).val() == 0){
                show_alert(T_ALERT_ADD_DAILY_PRICE_FORFAIT, "Datos incorrectos");
                $(this).val(last_daily_price);
            };
        });
    });

    $(".person-forfait-daily-price-injector").focus(function() {
        var last_daily_price = $(this).val();
        $(this).keyup(function() {
            var daily_price_forfait = $(this);
            recalculate_total_forfait_price(daily_price_forfait, last_daily_price);
        });
    });
    $("select.room-adults").on("change", function(e){
        var index = e.target.closest(".wrapper-rooms").id.replace("room-entry-", "");
        update_adults_amount(index);
    });
    $(document).on("change", "select.room-kids", function (e) {
        var index = e.target.closest(".wrapper-rooms").id.replace("room-entry-", "");
        update_kids_amount(index);
        var new_num_kids = $(this).val()
        update_ages_inputs(index, 'kids', new_num_kids)
    });
    $(document).on("change", "select.room-babies", function (e) {
        let index = e.target.closest(".wrapper-rooms").id.replace("room-entry-", "");
        let new_num_babies = $(this).val()
        update_ages_inputs(index, 'babies', new_num_babies)
    });


function update_ages_inputs(index, type, new_num_k_b) {
    fix_if_missing_divs(type, index)
    let container = $('#' + type + '-ages\\[' + index + '\\] input')
    let name = (type === 'kids') ? T_KID : T_BABY;
    name += ' '
    let old_num_kids = container.length;

    if (new_num_k_b > old_num_kids) {
        for (let i = old_num_kids; i < new_num_k_b; i++) {
            let new_input = $('<div>').addClass('input').append(
                $('<label>').text(name + (i + 1) + ': ').append(
                    $('<input>').attr({
                        type: 'text',
                        name: 'agesByRoom[kidsAges' + index + ']['+type + (i + 1) + ']',
                        class: '',
                        value: ''
                    })
                )
            );
            $('#' + type + '-ages\\[' + index + '\\]').append(new_input)
        }
    }
    else if (new_num_k_b < old_num_kids) {
        for (let x = old_num_kids; x > new_num_k_b; x--) {
            $('[name=\'agesByRoom[kidsAges'+index+']['+type+x+']\']').parent().remove();
        }
    }
}

function fix_if_missing_divs(type, index){
    let ages_div = $('#ages-input-room\\['+index+'\\]');
    if (ages_div.length == 0){
        let new_ages_div = $('<div>').addClass('entry-item').attr({id: 'ages-input-room['+index+']'});
        $('#selectors-room-'+index).after(new_ages_div);
    }
    let kids_ages_div = $('#kids-ages\\['+index+'\\]');
    if (kids_ages_div.length == 0){
        create_type_div(index, 'kids')
    }
    let babies_ages_div = $('#babies-ages\\['+index+'\\]');
    if (babies_ages_div.length == 0){
        create_type_div(index, 'babies')
    }
}

function create_type_div(index, type){
    let new_div =  $('<div>').addClass('input_wrapper col3').attr('id', type+'-ages[' + index + ']');
    $('#ages-input-room\\['+index+'\\]').append(new_div);
    return new_div
}



    $(".details .room-price").keyup(recalculate_price_per_day);
    $(".promo-price").keyup(recalculate_price_per_day);
    $(".rooms .room-price").keyup((e) => {
        update_price_per_day(e);
        recalculate_prices();
    });

    $(".rooms .room-price").one("click", () => {
        show_alert(T_ALERT_RESERVATION_MODIFY, T_ALERT_RESERVATION_MODIFY_TITLE);
    });

    $("input[id ^= 'check']").on("click", () => {
        const method = $("#recalculate_price_method").val();
        const alertText = method === 'manual' ? T_ALERT_RESERVATION_MODIFY_MANUAL : T_ALERT_RESERVATION_MODIFY_AUTOMATIC;

        if ((method === 'manual' && !recalculate_manual_up) || (method !== 'manual' && !recalculate_automatic_up)) {
            if (method === 'manual') {
                recalculate_manual_up = true;
            } else {
                recalculate_automatic_up = true;
            }
            show_alert(alertText, T_ALERT_RESERVATION_MODIFY_TITLE);
        }
    });

}

function ini_events_service_entry() {

    $('[class*="service_"][class*="button_add"]').click(function(e) {
        control_service_restrictions($(this));
        add_entry_service($(this));
    });

    $('[class*="service_"][class*="button_remove"]').click(function(e) {
        remove_entry_service($(this));
    });

    $('.forfait_person_button_add').click(function(e) {
        add_entry_adult_forfait($(this));
    });

    $('.forfait_adult_button_remove').click(function(e) {
        remove_entry_adult_forfait($(this));
    });

    $('.forfait_kid_button_remove').click(function(e) {
        remove_entry_kid_forfait($(this));
    });

    $('.service-key-injector, .service2-key-injector').change(function(e) {
        var service_key = is_service_valid(false, $(this));
    });
    $(' .service2-key-injector').change(function(e) {
        recalculate_inputs_needed($(this));
        recalculate_dates_selects();
    });

    $('.service-type-injector').change(function(e) {
        var service_key = is_service_valid(false, $(this));
    });
    $('.service-subtype-injector').change(function(e) {
        var service_key = is_service_valid(false, $(this));
    });
    $('.service-quantity-injector, .service2-quantity-injector').change(function(e) {
        var service_key = is_service_valid(false, $(this));
    });
    $('.service-days-injector, .service2-days-injector').change(function(e) {
        var service_key = is_service_valid(false, $(this));
    });
    $('.service-pvp-injector, .service2-pvp-injector').keyup(function(e) {
        var service_key = is_service_valid(false, $(this));
    });
    $('.service-key-injector').val('').change();

    $(".service-quantity").change(function(e) {
        recalculate_prices();
    });

    $(".service-days").change(function(e) {
        recalculate_prices();
    });

    $(".service-pvp").keyup(function(e) {
        recalculate_prices();
    });

    $(".service-pvp").blur(function(e) {
        format_control_number($(this));
    });



}

$(document).ready(function() {
    if ($(".hasDatepicker input").length) {
        let today_date = new Date();
        $(".hasDatepicker input").attr("placeholder", $.datepicker.formatDate("dd/mm/yy", today_date));
        $(".hasDatepicker input").datepicker({
            changeMonth: true,
            changeYear: true,
            yearRange: "-80:+20"
        });
    }
        if ($(".hasDatepickerForfait input")){
        var checkin = $("#checkin").val();
        var dateParts = checkin.split("/");
        checkin = new Date(new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0]) + 1);

        var checkout = $("#checkout").val();
        var dateParts = checkout.split("/");
        checkout = new Date(new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0]) + 1);

        $(".hasDatepickerForfait input").datepicker({
            changeMonth: true,
            changeYear: true,
            yearRange: "-80:+20",
            minDate: checkin,
            maxDate: checkout,
            showAnim: "clip"
        });

        $(".panel-forfait  .hasDatepickerForfait input").attr("value", $.datepicker.formatDate("dd/mm/yy", checkin));
    }

    $(".spinner_wrapper_faldon").css("display", "none");

    try {
        $("#tab-5 .extrainfo").jsonViewer(JSON.parse($("#tab-5 .extrainfo").html()), { collapsed: true });
    } catch {}

    $('ul.tabs li').click(function() {

        var tab_id = $(this).attr('data-tab');

        $('ul.tabs li').removeClass('current');
        $('.tab-content').removeClass('current');

        $(this).addClass('current');
        $("#" + tab_id).addClass('current');
    });
    $('.current').trigger('click');

    if ($('#disable_save_button').length > 0){
        show_alert(T_ALERT_RESERVATION_NOT_SAVE_FLIGHT_HOTEL, "");
    }


    $('#sendConfirmReservation').click(function(e) {
        send_confirmation(e);
    });
    $('#sendConfirmationAvailability').click(function(e) {
        send_confirmation_availability(e);
    });

    $('#sendReservationToChannel').click(function(e){
        send_reservation_to_channel(e);
    });

    $('#saveReservation').click(function(e) {

        $("#saveReservation").addClass("btn_disable").addClass("btn_loading");

        $(".spinner_wrapper_faldon").css("display", "block");

        var formData = new FormData(document.getElementById("data"));

        var final_post_dict = {};
        formData.forEach(function(value, key) {
            if (!final_post_dict[key]){
                final_post_dict[key] = value;
            }
        });

        if ($('#check_pull').val()) {
            var checking_alert = $.alert({
                title: T_TITLE_CHECKING_AVAILABILITY,
                content: T_MESSAGE_CHECKING_AVAILABILITY.replace(/&lt;br&gt;/g, '<br>'),
                closeIcon: false,
                columnClass: 'my_confirm',
                show: false,
                type: 'icon',
                icon: 'fas fa-spinner fa-pulse',
                container: 'body',
                buttons: {
                    confirm: {
                        text: ' ',
                    }
                },
                onOpen: function() {
                    this.$btnc.get(0).style.opacity = '0';
                    this.$btnc.get(0).style.pointerEvents = 'none';
                },
            })
            omnibees_date_control(e, final_post_dict).then(result => {
                checking_alert.close();
                if (result.pull_real_time_mapping){
                    final_post_dict['new_pull_real_time_mapping'] = result.pull_real_time_mapping;
                    if (result.missing_room_availability){
                        final_post_dict['missing_room_availability'] = result.missing_room_availability
                    }
                }
                save_reservation(e, final_post_dict)
            })
        }else if ($('#check_service2_availability').val()){
            verify_services_availability(e, function(unavailable_service) {
                console.log('Unavailable services:', unavailable_service);
                console.log(unavailable_service);
                var all_services = JSON.parse($('#all_services2').val());
                var services_names = '';
                if (!$.isEmptyObject(unavailable_service)){
                    for (var service_key in unavailable_service){
                        all_services.forEach(function(value, key) {
                            if (service_key === value.key){
                                services_names += '\n'+value.name+': '+unavailable_service[service_key];
                            }
                        });
                    }
                    $.confirm({
                        title: T_ALERT_RESERVATION_MODIFY_TITLE,
                        content: T_UNAVAILABLE_SERVICES + services_names,
                        columnClass: 'my_confirm',
                        autoClose: false,
                        buttons: {
                            confirm_button: {
                                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
                                btnClass: 'btn btn_small',
                                keys: ['enter', 'shift'],
                                action: function() {

                                    final_post_dict['skipped_services_restrictions'] = "True";
                                    save_reservation(e, final_post_dict);
                                }
                            },
                            cancel_button: {
                                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
                                btnClass: 'btn btn_small btn_link',
                                action: function() {
                                    return true;
                                }
                            }
                        }
                    });
                }else{
                    save_reservation(e, final_post_dict);
                }

            });
            $("#saveReservation").removeClass("btn_disable").removeClass("btn_loading");
            $(".spinner_wrapper_faldon").css("display", "none");
        }
        else {
            save_reservation(e, final_post_dict);
        }
    });

    $(".chk_rec_supplements").click(function(e) {
        recalculate_prices();
    });

    $(".chk_rec_total").click(function(e) {
        recalculate_prices();
    });

    $("#checkin").on("change", (e) => {
        add_new_price_per_day();
        refresh_services2(e);
        recalculate_dates_selects();
    });

    $("#checkout").on("change", (e) => {
        add_new_price_per_day();
        refresh_services2(e);
        recalculate_dates_selects();
    });

    $(".service-key").on("change", (e)=>{

    e.target
    .closest(".entry-item")
    .querySelector(".service-name").value = e.target.querySelector("option:checked").innerHTML;

    });

    ini_events_service_entry();
    ini_events_room_entry();
    ini_events_promo_entry();

    recalculate_counters();
    //recalculate_prices();
    //fix_price_per_day();
    //add_new_price_per_day();
    //fix_room_mods();

    $("div.rooms").on("change", () => {
        $(".save-notification.room-notification").slideDown();
        $(".recovery_room_info").css({ "display": "None" });
    });

    $("div.services").on("change", () => {
        $(".save-notification.services-notification").slideDown();
    });

    var roomsObserver = new MutationObserver((m) => {
        $(".save-notification.room-notification").slideDown();
        $(".recovery_room_info").css({ "display": "None" });
    });

    roomsObserver.observe($("div.rooms")[0], {
        childList: true,
        subtree: true,
        attributes: true
    });

    var servicesObserver = new MutationObserver((m) => {
        $(".save-notification.services-notification").slideDown();
    });

    servicesObserver.observe($("div.services")[0], {
        childList: true,
        subtree: true,
        attributes: true
    });

    $(".details .gwt-ListBox").on("change", (e) => {
        show_price_per_day($(e.target));
    });

    $(".priceTotal").blur((e) => {
        format_control_number($(e.target));
    });

    $("#payed").blur((e) => {

        format_control_number($(e.target));
    });

    function showSaveAndCancelButtons(){
        $('.save_extrainfo').toggle();
        $('.cancel_extrainfo').toggle();
        $('.discard_extrainfo').toggle();
    }

    function showEditButton(){
        $('.edit_extrainfo').toggle();
    }

    // EDIT CHANGES OF EXTRAINFO //
    $('.edit_extrainfo').on("click", function(e){
        e.preventDefault();
        showSaveAndCancelButtons()
        showEditButton() // Only visual button on/off

        $('#extrainfo_main').hide();
        $('#extrainfo_json').hide();

        var textarea = $("#user_textarea");
        $('#extrainfo_original').val(textarea.val()); //We need update the old value because it is obsolete if we save 1 time
        var obj = JSON.parse(textarea.text());
        textarea.html(JSON.stringify(obj, undefined, 4));
        textarea.css('height', 'auto');

        $("#user_textarea").show();

    })

    // SAVE CHANGES OF EXTRAINFO //
    $('.save_extrainfo').on("click", function(e){
        e.preventDefault();
        var extrainfo = $('.extrainfo_textarea').val();
        $('#extrainfo_hidden').text(extrainfo);
        $('#extrainfo_json').text(extrainfo);

        try{
            $("#extrainfo_json").jsonViewer(JSON.parse($("#extrainfo_json").html()), { collapsed: true });
            $.confirm({
                title: T_ALERT_RESERVATION_SAVE_TITLE2,
                content: $("#popup_save_extrainfo").html(),
                columnClass: 'my_confirm',
                autoClose: false,
                buttons: {
                    confirm_button: {
                        text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
                        btnClass: 'btn btn_small',
                        keys: ['enter', 'shift'],
                        action: function(){
                            showEditButton()
                            showSaveAndCancelButtons()
                            $(".spinner_wrapper_faldon").css("display", "block");
                            var extrainfo = $('#user_textarea').val();
                            $('#extrainfo_json').text(extrainfo);

                            $('#extrainfo_main').text(extrainfo);
                            $("#extrainfo_main").jsonViewer(JSON.parse($("#extrainfo_main").html()), { collapsed: false });

                            $('#extrainfo_hidden').val(JSON.stringify(extrainfo, undefined, 4));
                            let hotel_code = $("#real_hotel_code").val()
                            let identifier = $("input[name=identifier]").val();
                            let extrainfo_original = $("#extrainfo_original").val();
                            var data_to_send = {
                                'json': extrainfo,
                                'json_original': extrainfo_original,
                                'hotel_code': hotel_code,
                                'identifier': identifier
                            }
                            let session_key = $("input[name='sessionKey']").val();

                            $.ajax({
                                url: "/reservation/save_extrainfo_booking?sessionKey=" + session_key,
                                data: JSON.stringify(data_to_send),
                                contentType: "application/json",
                                type: 'POST',
                                xhrFields: {
                                    withCredentials: true
                                },
                                crossDomain: true,
                                statusCode: {
                                    308: function () {
                                        console.log('Error 308 detected...')
                                        window.location.href = `https://hotel-manager-2-264405814672.europe-west1.run.app/reservation/save_extrainfo_booking?sessionKey=${session_key}`;
                                    }
                                },
                                success: function(response) {

                                    if (response.status == "OK"){
                                        // Now we update the history
                                        var table = $('#history_table');
                                        var newRow = $('<tr>').append(
                                            $('<td>').html(response.timestamp),
                                            $('<td>').html(response.user),
                                            $('<td>').html(response.change)
                                        );
                                        table.prepend(newRow);
                                        save_message = T_ALERT_RESERVATION_SAVE_TITLE
                                    } else {
                                        save_message = T_ALERT_NO_CHANGES
                                    }

                                    show_alert(save_message, save_message);
                                },
                                error: function(xhr, status, error) {
                                    // Handle errors here
                                    save_message = "No se puede guardar el extrainfo"; // Or a more specific error message
                                    show_alert(save_message, T_ALERT_NO_CHANGES);
                                    console.error("Error saving extra info:", xhr, status, error);
                                }
                            });

                            $("#user_textarea").hide();
                            $('#extrainfo_main').show();

                            return true;
                        }
                    },
                    cancel_button: {
                        text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
                        btnClass: 'btn btn_small btn_link',
                        action: function() {
                            $("#saveReservation").removeClass("btn_disable").removeClass("btn_loading");
                            $(".spinner_wrapper_faldon").css("display", "none");
                            return true;
                        }
                    }
                }
            });
        }catch(error){

            // We check if the browser supports the 'name' and 'message' fields in the error
            if ('message' in error && 'name' in error) {
                var error_text = 'El JSON está mal formado: '+ '<br><br><b>Error:</b> ' + error.name + '<br><b>Detalle</b>: ' + error.message + '<br><br>Revísalo e intentalo de nuevo.';
            } else {
                var error_text = 'El JSON está mal formado. Revísalo e intentalo de nuevo.';
            }

            $.confirm({
                title: 'JSON mal formado',
                content: error_text,
                columnClass: 'my_confirm',
                autoClose: false,
                buttons: {
                    confirm_button: {
                        text: 'OK',
                        btnClass: 'btn btn_small',
                        keys: ['enter', 'shift'],
                    }
                }
            });
            }
        })

    // CANCEL EDIT EXTRAINFO //
    $('.cancel_extrainfo').on("click", function(e){
        e.preventDefault();
        showSaveAndCancelButtons()
        showEditButton()
        $("#user_textarea").hide();
        $('#extrainfo_main').show();
    })

    // DISCARD CHANGES OF EXTRAINFO //
    $('.discard_extrainfo').on("click", function(e){
        e.preventDefault();
        $.confirm({
            title: 'Descartar los cambio',
            content: '¿Deseas descartar los cambios?',
            columnClass: 'my_confirm',
            autoClose: false,
            buttons: {
                confirm_button: {
                    text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
                    btnClass: 'btn btn_small',
                    keys: ['enter', 'shift'],
                    action: function() {
                        $(".spinner_wrapper_faldon").css("display", "block");

                        var extrainfo_original = $('#extrainfo_original').val();
                        $('.extrainfo_textarea').val(extrainfo_original);

                        $('#extrainfo_json').text(extrainfo_original);
                        $("#extrainfo_json").jsonViewer(JSON.parse($("#extrainfo_json").html()), { collapsed: true });

                        var element = $(".extrainfo_textarea");
                        var jsonString = element.val();

                        var obj = JSON.parse(jsonString);
                        var formattedJson = JSON.stringify(obj, undefined, 4);
                        element.val(formattedJson);
                        return true;
                    }
                },
                cancel_button: {
                    text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
                    btnClass: 'btn btn_small btn_link',
                    action: function() {
                        $("#saveReservation").removeClass("btn_disable").removeClass("btn_loading");
                        $(".spinner_wrapper_faldon").css("display", "none");
                        return true;
                    }
                }
            }
        });
    })
    $('#save_internal_comments').on("click", function(e){
        e.preventDefault();
        var internal_comments = $('#new_internal_comments').val();
        var session_key = $('#session_key').val();
        var identifier = $('.identifier').val();
        var hotel_code = $('.origin_hotel_code').val();
        var data_to_send = {
            'internal_comments': internal_comments,
            'identifier': identifier,
            'hotel_code': hotel_code
        }
        save_internal_comments(data_to_send, session_key);
    })
});
function save_internal_comments(data_to_send, session_key){
    $.ajax({
        url: "/reservation/save_internal_comments?sessionKey=" + session_key,
        data: JSON.stringify(data_to_send),
        contentType: "application/json",
        type: 'POST',
        success: function(response) {
            var table = $('#internal_comments_table');
            $('#new_internal_comments').val('');

            var newRow = $('<tr>').append(
                $('<td>').html(response.user),
                $('<td>').html(response.comment),
                $('<td>').html(response.timestamp)
            );
            table.response.changend(newRow);
        },
    });
}

function is_service_valid(verbose, control) {
    var service_type = get_service_type(control);

    var service_key = $("."+service_type+"-key-injector").val();
    var service_pvp = $("."+service_type+"-pvp-injector").val();
    var service_amount = $("."+service_type+"-amount-injector").val();
    var service_days = $("."+service_type+"-days-injector").val();

    $("."+service_type+"_button_add").prop("disabled", true);

    if (service_key == "") {
        if (verbose) show_alert(T_ALERT_RESERVATION_NOT_SERVICES, "Datos incorrectos");
        return '';
    }
    var regex = /^([0-9])+[\.,]{0,1}([0-9])*$/;
    if (!regex.test(service_pvp)) {
        if (verbose) show_alert(T_ALERT_RESERVATION_WRONG_SERV_PRICE, "Datos incorrectos");
        return '';
    }

    if (service_days == '') {
        if (verbose) show_alert(T_ALERT_SEVICE_DAYS, "Datos incorrectos");
        return '';
    }

    $("."+service_type+"_button_add").prop("disabled", false);

    return service_key;

}
function is_adult_valid(verbose) {
    var person_key = $(".person-key-injector").val();
    var adult_forfait_daily_price = $(".person-forfait-daily-price-injector").val();
    $('.forfait_person_button_add').prop("disabled", true);

    if (person_key == "") {
        if (verbose) show_alert(T_ALERT_FORFAIT_NOT_PERSON, "Datos incorrectos");
        return '';
    }

    var regex = /^([0-9])+[\.,]{0,1}([0-9])*$/;
    if (!regex.test(adult_forfait_daily_price) || adult_forfait_daily_price == "" || adult_forfait_daily_price == 0) {
        if (verbose) show_alert(T_ALERT_ADD_DAILY_PRICE_FORFAIT, "Datos incorrectos");
        return '';
    }
    $('.forfait_person_button_add').prop("disabled", false);

    return person_key;
}

function show_alert(msg, title) {
    $.alert({
        title: title,
        closeIcon: true,
        content: msg.replace(/&lt;br&gt;/g, '<br>'),
        columnClass: 'my_confirm',
        buttons: {
            confirm: {
                text: 'OK',
                btnClass: 'btn btn_small',
                action: function() {
                    return true;
                }
            }
        }
    });
    return;

}




function is_room_valid(verbose) {

    var room_key = $(".room-key-injector").val();
    var room_price = $(".room-price-injector").val();
    var room_rate = $(".room-rate-injector").val();
    var room_board = $(".room-board-injector").val();
    var room_adults = $(".room-adults-injector").val();

    var next_index = $("#new_index").val();
    if (next_index && $("#index-already-exists-"+next_index).val()){
        if (verbose) show_alert(T_ALERT_ROOM_INDEX, "Datos incorrectos");
        return '';
    }

    $('.room_button_add').prop("disabled", true);

    if (room_adults <= 0) {
        if (verbose) show_alert(T_ALERT_OCUPANCY, "Datos incorrectos");
        return '';
    }
    if (room_key == "") {
        if (verbose) show_alert(T_ALERT_ONE_ROOM, "Datos incorrectos");
        return '';
    }
    var regex = /^([0-9])+[\.,]{0,1}([0-9])*$/;
    if (!regex.test(room_price)) {
        if (verbose) show_alert(T_ALERT_ROOM_PRICE, "Datos incorrectos");
        return '';
    }
    if (room_rate == "") {
        if (verbose) show_alert(T_ALERT_ROOM_RATE, "Datos incorrectos");
        return '';
    }
    if (room_board == "") {
        if (verbose) show_alert(T_ALERT_ROOM_BOARD, "Datos incorrectos");
        return '';
    }

    $('.room_button_add').prop("disabled", false);

    return room_key;
}



function num_rooms_control_ok() {


    if (!$("#shopping_cart_enabled").val() ||  $("#shopping_cart_enabled").val().toLowerCase() != "true") {
        //MAX 3 rooms
        var num_rooms = $(".counter-room").length;
        if (num_rooms >= 4) {
            show_alert(T_ALERT_THREE_ROOMS, T_ACTION_NOT_ALLOWED);
            return false;
        }
    }

    return true
}


function open_new_room_form() {

    if (num_rooms_control_ok()) {
        $(".panel-rooms").slideToggle("fast");
    }

}

function open_new_promotion_form() {

    $(".panel-promotion").slideToggle("fast");
    
}

function price_change(e) {
    //$('.chk_rec_total').prop('checked', false);
    $(".priceTotal")[0].classList.remove("trigger");
    // $(".priceTotal")[0].classList.add("trigger");
    //valid_control_number($(".priceTotal"));
    var num_rooms = $(".counter-room").length - 1;
    var average = parseFloat($(".priceTotal").val()) / parseFloat(num_rooms);

    $(".rooms").find(".room-price").each(function(index) {
        $(this).val(average);
        if (e.target.name != "price") {
            this.dispatchEvent(new Event('keyup'));
        }
        format_control_number($(this));

    });

    $(".room-price").each((index, v) => {
        v.dispatchEvent(new Event('keyup'));
    });

}
var price_message_up = false;
var price_sups_message_up = false;
var payed_message_up = false;
var recalculate_manual_up = false;
var recalculate_automatic_up = false;


$('#recalculate_price_method').on("change", function () {
    recalculate_automatic_up = recalculate_manual_up = false;
});


function recovery_deleted_room(id_input){


   if ($("#index_room_"+id_input).val()){
        $("#new_index").val($("#index_room_"+id_input).val()).change();
    }
   if ($("#room_key_"+id_input).val()){
        $('.room-key-injector').val($("#room_key_"+id_input).val()).change();
    }
    if ($("#rate_key_"+id_input).val()){
        $('.room-rate-injector').val($("#rate_key_"+id_input).val()).change();
    }
    if ($("#regimen_key_"+id_input).val()){
        $('.room-board-injector').val($("#regimen_key_"+id_input).val()).change();
    }
    if ($("#price_"+id_input).val()){
        $('.room-price-injector').val($("#price_"+id_input).val()).change();
    }
    if ($("#occupancy_"+id_input).val()){

        var occupancy = $("#occupancy_"+id_input).val().split("-");

        $('.room-adults-injector').val(occupancy[0]).change();
        $('.room-kids-injector').val(occupancy[1]).change();
        $('.room-babies-injector').val(occupancy[2]).change();
    }
    $(".recovery_room_info").css({ "display": "block" });
    open_new_room_form();
    $("#tab-rooms").click()


}


$(window).on("load", function() {

    $('#destiny_hotel').change(function(e) {
            change_reservation_to_other_hotel();

    });

    $('#select_agent').change(function(e){
        change_agent_call_center();
    });

    // se ha cambiado de sitio de ejecucion debido a cambio en el renderizado del modificar
    if ($(".hasDatepicker input").length) {
        let today_date = new Date();
        $(".hasDatepicker input").attr("placeholder", $.datepicker.formatDate("dd/mm/yy", today_date));
        $(".hasDatepicker input").datepicker({
            changeMonth: true,
            changeYear: true,
            yearRange: "-80:+20"
        });
    }

    if ($(".hasDatepickerForfait input")){
        var checkin = $("#checkin").val();
        var dateParts = checkin.split("/");
        checkin = new Date(new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0]) + 1);

        var checkout = $("#checkout").val();
        var dateParts = checkout.split("/");
        checkout = new Date(new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0]) + 1);

        $(".hasDatepickerForfait input").datepicker({
            changeMonth: true,
            changeYear: true,
            yearRange: "-80:+20",
            minDate: checkin,
            maxDate: checkout,
            showAnim: "clip"
        });

        $(".panel-forfait  .hasDatepickerForfait input").attr("value", $.datepicker.formatDate("dd/mm/yy", checkin));
    }

    $(".priceSupplements").click(function() {
        //$('.chk_rec_supplements').prop('checked', false);
        if (!price_sups_message_up) {
            //more than one room
            price_sups_message_up = true;
            show_alert(T_ALERT_RESERVATION_MOD_PRICE, T_IMPORTANT_ALERT);
            $("#tab-services").click();
            return false;
        }


    });

    $(".priceTotal").keyup(price_change);

    $(".priceTotal").click(function() {
        var num_rooms = $(".counter-room").length - 1;
        if (num_rooms > 1 && !price_message_up) {
            //more than one room
            price_message_up = true;
            show_alert(T_ALERT_CHANGE_RESERVATION_PRICE_A + num_rooms + T_ALERT_CHANGE_RESERVATION_PRICE_B);

            $("#tab-rooms").click();

            return false;
        }else if (!price_message_up){
            price_message_up = true;
            show_alert(T_ALERT_RESERVATION_MODIFY, T_ALERT_RESERVATION_MODIFY_TITLE);
        }
    });
    //$(".payed").keyup(price_change);

    $(".payed").click(function() {
        if (!payed_message_up){
            payed_message_up = true;
            show_alert(T_TOTAL_PRICE_CHANGE, T_ALERT_RESERVATION_MODIFY_TITLE);
        }
        $("#tab-rooms").click();
        return false;
    });

    $(".room-contact").tabs({
          classes: {
            "ui-tabs-active": "active"
          }
    });


    $("select[name=method_payment]").on("change", function (){
        let selected_payment_method = $('select[name=method_payment] option:selected').val();
        if (selected_payment_method == "web_payment"){
            let total_price = $(".priceTotal").val();
            $("#payed").val(total_price);
        }
        if (selected_payment_method == "transfer_bank"){
            $("#payed").val("");
        }
    })



});


function change_reservation_to_other_hotel(){

     $.confirm({
        title: T_RESERVATIONS_HOTEL_CHANGE,
        content: T_RESERVATIONS_HOTEL_CHANGE_ADVISE +$('#destiny_hotel option:selected').text(),
        columnClass: 'my_confirm',
        autoClose: false,
        buttons: {
            confirm_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_YES,
                btnClass: 'btn btn_small',
                keys: ['enter', 'shift'],
                action: function() {
                    let save_button = $('#saveReservation');
                    let destinity_hotel_code =  $('#destiny_hotel').val();
                    let destiny_hotel_name = $('#destiny_hotel option:selected').text();
                    let identifier = $('.identifier').val();
                    let session_key = $('#session_key').val();
                    let url = '/reservations/modify/check_moved_reservation?sessionKey=' + session_key + '&identifier=' + identifier + '&destiny_hotel_code=' + destinity_hotel_code;
                    save_button.prop('disabled', true);

                    $.ajax({
                        type: 'GET',
                        url: url,
                        contentType: 'application/json',
                        success: function (result) {

                            save_button.prop('disabled', true);
                            if (result === 'ready_to_move') {
                                $('.spinner_wrapper_move_hotel').css("display", "block");
                                window.location.replace(window.location.href + "&destiny_hotel_code="+destinity_hotel_code + "&destiny_hotel_name=" + destiny_hotel_name);
                                return true;
                            }
                            else {
                                show_alert(T_RESERVATION_ALREADY_MOVED, "")
                            }
                        }
                    });
                }
            },
            cancel_button: {
                text: T_RESERVATIONS_POPUP_RESEND_HOTEL_NO,
                btnClass: 'btn btn_small btn_link',
                action: function() {

                    return true;

                }
            }

        }
    });
}

function change_agent_call_center(){
    let select_agent =  $('#select_agent').val();
    if (select_agent !== ""){
        $("#agentId").val(select_agent);
    }

}

function error_info(){
    $.alert({
        title: 'Error',
        closeIcon: true,
        content: $(".popup_error_info").html(),
        columnClass: 'my_confirm',
        buttons: {
            confirm: {
                text: 'OK',
                btnClass: 'btn btn_small',
                action: function () {
                   return true;
                }
            }
        }
    });
}

function toggleDisplay(isChecked, selectors) {
    const displayValue = isChecked ? "block" : "none";
    selectors.forEach(selector => {
        $(selector).css({ "display": displayValue });
    });
}