import React, { Component } from 'react';
import BigCalendar from './components/custo-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import './App.css';



//array de eventos
const myEventsList= [{
  title: "today",
  start: new Date('2020-09-01 10:22:00'),
  end: new Date('2020-09-01 10:42:00')
},
{
  title: "string",
   start: new Date('2020-09-05 12:22:00'),
  end: new Date('2020-09-05 13:42:00')
}]

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <BigCalendar eventList="myEventsList" />
      </header>
    </div>
  );
}

export default App;
