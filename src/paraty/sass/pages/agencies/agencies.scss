@import "commons/colors";
@import "libs/mixins";

@import "commons/modals";
@import "commons/forms";
@import "commons/zebra_table";

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.modal .modal_content {
  width: 600px;
}
.default_swicth {
  input.swicth {
    display: none;
  }
  &:before {
    content: '\f0f3';
    font-family: "Font Awesome 5 Pro", sans-serif;
    font-weight: 900;
    color: $yellow;
  }
}

.see_subagencies_button {
  font-size: 12px;
  padding: 8px 12px;
  margin-left: 5px;
}

.modal_subagencies_wrapper {
  .modal_content {
    .modal_subagencies_body {
      .subagency_list {
        width: 100%;
        margin-top: 5px;
        border-collapse: collapse;

        .subagency_row.hide {
          display: none;
        }

        th {
          text-align: left;
        }

        td, th {
          padding: 8px;
        }

        tr:nth-child(even) {
          background-color: rgba(gray, .1);
        }

        .buttons_actions {
          text-align: right;
        }
      }

      .sub_agency_to_add_list_wrapper {
        display: flex;
        width: 100%;
        align-items: center;
        flex-wrap: nowrap;
        margin-top: 8px;

        select {
          width: 300px;
          height: 32px;
          font-size: 12px;
        }

        .add_subagency, .refresh_subagency {
          font-size: 14px;
          padding: 5px 15px;
          margin-left: 5px;
        }
      }
    }
  }
}

.input.select.hidden_shown_select:after{
    transform: translate(0%, 50%);
}
#hidden_shown_select{
  margin: 3px 0;
  width: 100%;
}
#rate_words{
  margin-top: 13%;
}

.warning_add_subagency{
  font-size: 10px;
  margin: 3px 0 0 3px;
}

.search_filter_text{
  color:$orange;
  margin: 5px 0;
}

.modal .modal_content .modal_title {
  top: -10px;
}

.info_agencies_label{
  color: #446ca9;
  text-decoration: underline #446ca9;
}

.fa-recycle{
  font-size: 1.5em;
}

.show_total_external_reservations{
  color: white;
  text-transform: uppercase;
  z-index: 3;
  position: absolute;
  animation: fadeIn 0.5s ease-in forwards;
  background: $garkgrey;
  font-size: 0.7rem;
  padding: 0 5px;
  border-radius: 10px;
}

.hide_total_external_reservations{
  display: none;
}
