body {
  box-sizing: border-box;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  font-family: "Roboto", sans-serif;
  letter-spacing: 0.4px;
    align-items: center;
  .full_wraper{
    border: 2px #446ca9 solid;
    border-radius: 5px;
    width: 40%;
    margin: 20px auto;
    padding: 7px 14px;
  }
  .wraper {
    text-align: center;
    display: flex;
    justify-content: center;
    margin: 20px 20px 0px;

    .form {
      .c1 {
        color: #446ca9;
      }
      .config_search {
        border: 1px solid #ededed;
        appearance: none;
        border-radius: 3px;
        padding: 7px 14px;
      }
      .search {
        background-color: #446ca9;
        color: white;
        appearance: none;
        padding: 7px 14px;
        text-transform: uppercase;
        border-radius: 3px;
        font-weight: bold;
        letter-spacing: 1px;
        cursor: pointer;
        display: inline-block;
        margin:5px;
      }
      }
    }
  }

  div{
    text-align: center;
    color: #446ca9;

    .nhotels{

    }
  }



select{
      border: 1px solid #ededed;
      appearance: none;
      border-radius: 3px;
      padding: 7px 14px;
}
.search {
        background-color: #446ca9;
        color: white;
        appearance: none;
        padding: 7px 14px;
        text-transform: uppercase;
        border-radius: 3px;
        font-weight: bold;
        letter-spacing: 1px;
        cursor: pointer;
        display: inline-block;
        margin:10px;
      }
#title{
}
.modifies_info{

}

.loader_container {
  text-align: center;
  .loader {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #446ca9;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 2s linear infinite;
    display: none;
    text-align: center;
    margin: 10px auto 10px;

  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}