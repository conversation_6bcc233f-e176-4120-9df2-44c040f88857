# -*- coding: utf-8 -*-
import base64
import hashlib
import hmac
import json
import zlib

import securetrading
from Crypto.Cipher import DES3
# from Cryptodome.Cipher import DES3
from requests.auth import HTTP<PERSON>asicAuth

from paraty.communications.datastore_utils import legacy_key_to_id
from paraty.pages.agencies.agencies_utils import get_integration_configuration
from paraty.pages.reservations.create import get_decrypted, decrypted, get_real_hotel_reservation
from paraty.pages.reservations.modify import get_rooms_reservation, send_emails_confirmation
from paraty.pages.reservations.send_reservations_to_adapter import get_all_applicationId
from paraty.pages.reservations.show import _get_total_payed_amount_from_all_sources
from paraty.security_utils.decrypt_utils import build_encrypted_url
from paraty.utilities.hotel_utils import get_config_property_value
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel
import os
import time
from datetime import datetime, timedelta
import logging

import securetrading
from flask import request as r, make_response
import requests
from paraty import app
from paraty.authentication.login_utils import login_from_session<PERSON>ey, login_using_referrer
from paraty.pages.integrations.IntegrationUserPanelHandler import get_integrations
from paraty.utilities import session_utils
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.datastore.datastore_utils import get_inner_url
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels, get_hotel_by_application_id, \
    get_all_hotels

import requests

from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels, get_hotel_by_application_id
from paraty_commons_3.email_utils import sendEmail, sendEmail_backup


def gateways_format_price(amount):
    stconfig = securetrading.Config()
    stconfig.username = "NachoParaty"
    stconfig.password = "TrustPayment123!"
    st = securetrading.Api(stconfig)
    transaction_reference = "58-9-3703405"

    request = {
        "sitereference": "test_paratytech126489",
        "requesttypedescriptions": ["AUTH"],
        "accounttypedescription": "ECOM",
        "currencyiso3a": "EUR",
        "baseamount": "1050",
        "orderreference": "My_Order_123",
        "parenttransactionreference": "58-9-3703405"
    }
    logging.info(f"Request sent to trust: {str(request)}")
    strequest = securetrading.Request()
    strequest.update(request)
    stresponse = st.process(strequest)
    responses = stresponse.get('responses')[0]
    pass

def send_emails():
    hoteles = {
        'port-benidorm': '2024-07-05 09:00:00',
        "port-alicante": '2024-07-05 09:00:00',
        "port-jardin": '2024-07-05 09:00:00',
        "q10-caspe": '2024-07-05 09:00:00',
        "el-duque": '2024-07-05 09:00:00',
        "oasishoteles-tulum": '2024-07-04 19:00:00',
        "oasishoteles-senstulum": '2024-07-05 07:00:00',
        "oasishoteles-grandcancun": '2024-07-04 19:00:00',
        "el-balsamo": '2024-07-05 01:00:00',
        "oasishoteles-grandpalm": '2024-07-05 01:00:00',
        "oasishoteles-pyramid": '2024-07-05 01:00:00',
        "htop-caleta-palace": '2024-07-04 01:00:00',
        "htop-olympic": '2024-07-05 00:00:00',
        "htop-amaika": '2024-07-04 23:00:00',
        "htop-royal-beach": '2024-07-04 17:00:00',
        "htop-calella-palace": '2024-07-04 22:00:00',
        "htop-platja-park": '2024-07-04 21:00:00'
    }

    d = {}
    identifiers_to_avoid = []

    for hotel_code, time in hoteles.items():
        hotel_info = get_hotel_by_application_id(hotel_code)
        url = get_inner_url(hotel_info)
        reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code, search_params=[('timestamp', '>=', time)]))
        for r in reservations:
            if r.get('identifier') not in identifiers_to_avoid and not r.get('modificationTimestamp') and r.get('additionalServices'):
                email = r.get('email')
                email_sender = ""
                if email:
                    email_sender = "&email=" + str(email).replace(";", "%3B")

                send_confirmation_post = build_encrypted_url("%s/send-confirmation/?id=%s&modification=%s&type=%s%s&from_manager2=True" % (
                url, r.get('identifier'), False, 'customer', email_sender))
                result = requests.post(send_confirmation_post, json={})
                if d.get(hotel_code):
                    d[hotel_code].append(r)
                else:
                    d[hotel_code] = [r]
                identifiers_to_avoid.append(r.get('identifier'))
                logging.info(f"{hotel_code}: {identifiers_to_avoid}")
            pass
    pass

def test_affirm():
    import requests

    url = "https://sandbox.affirm.com/api/v1/transactions"

    payload = {
        "expand": "checkout",
        "order_id": "100095646",
        "transaction_id": "0NC7SCH57NNYV6Q2"
    }
    headers = {
        "accept": "*/*",
        "content-type": "application/json",
        "authorization": "Basic S09QNjVUR1Y5T0ozMjhXTjpVMnF6Q0xXTFdSRlVRVVhYOTVSVHJKTWtNUlNZRWQ2aA=="
    }

    response = requests.post(url, json=payload, headers=headers)
    import requests

    url = "https://sandbox.affirm.com/api/v2/charges"

    payload = {
        "checkout_token": "0KYZ5FN5JLPYG6IV"
    }
    headers = {
        "accept": "*/*",
        "content-type": "application/json",
    }

    response = requests.post(url, auth=HTTPBasicAuth("KOP65TGV9OJ328WN", "U2qzCLWLWRFUQUXX95RTrJMkMRSYEd6h"),json=payload, headers=headers)

    print(response.text)


def room_with_occupants():
    has_occupants = []
    reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code='oasishoteles-grandcancun',
                                                                           search_params=[
                                                                               ('timestamp', '>=', '2024-01-18'),
                                                                               ('timestamp', '<=', '2024-02-18')]))

    for r in reservations:
        ei = json.loads(r.get('extraInfo'))
        roi = ei.get('room_owner_information')
        if roi and roi.get('1', {}).get('adults', [''])[0].get('name', '') != "":
            has_occupants.append(r.get('identifier'))
            pass
        pass


def test_agregar_token_sermepa_en_extrainfo_y_enviar_email():
    id = "57542970"
    hotel_code = 'nura-ponsa'
    payment_info = {'CODE': 'OK', 'GATEWAY_EXTRA_INFO': {'ds_ExpiryDate': '2248a5f812387564060a5ea4c477f6bf4a15d1c1', 'ds_MerchantIdentifier': '2248a5f812387564060a5ea4c477f6bf4a15d1c1', 'no_redirect': True, 'sermepa_cof_txnid': 'MNWZ2AKIN0910  '}, 'GATEWAY_ORDER_ID': '57542970', 'GATEWAY_PAID_AMOUNT': '382.64', 'GATEWAY_TOKEN_ID': '2248a5f812387564060a5ea4c477f6bf4a15d1c1', 'PAYMENT_GATEWAY_NAME': 'REDSYS', 'sermepa_cof_txnid': 'MNWZ2AKIN0910  '}
    reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,search_params=[('identifier', '=', id)]))

    hotel_info = get_hotel_by_application_id(hotel_code)
    url = get_inner_url(hotel_info)
    for r in reservations:
        ei = json.loads(r.get('extraInfo'))
        for x, k in payment_info.get("GATEWAY_EXTRA_INFO", {}).items():
            ei[x] = k
        r["extraInfo"] = json.dumps(ei)
        datastore_communicator.save_to_datastore("Reservation", int(r.key.id), r, hotel_code=hotel_code)

        # email = r.get('email')
        # email_sender = ""
        # if email:
        #     email_sender = "&email=" + str(email).replace(";", "%3B")

        # send_confirmation_post = build_encrypted_url(
        #     "%s/send-confirmation/?id=%s&modification=%s&type=%s%s&from_manager2=True" % (
        #         url, r.get('identifier'), False, 'customer', email_sender))
        # requests.post(send_confirmation_post, json={})
        #
        # send_confirmation_post = build_encrypted_url(
        #     "%s/send-confirmation/?id=%s&modification=%s&type=%s%s&from_manager2=True" % (
        #         url, r.get('identifier'), False, 'manager', email_sender))
        # requests.post(send_confirmation_post, json={})

def test_booking_request_log():
    properties = {
        "hotelCode": 'test',
        "identifier": 'test',
        "integrationPushTimestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "requestTimestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

    datastore_communicator.save_to_datastore("BookingRequestLog", None, properties, hotel_code="admin-hotel")
    pass

def test_corregidor():
    reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code="hotel-corregidor", search_params=[('timestamp', '>', "2024-09-19 20:50:34")]))
    pass

# agregar_token_sermepa_en_extrainfo_y_enviar_email(id, hotel_code)


def show_form():
    # x = {"selected_options": [["ahBlfmRhZ3Vpc2EtaG90ZWxzchELEgRSYXRlGICAgJCyqJQKDKIBD2RhZ3Vpc2EtY2FuaWxsbw", "ahBlfmRhZ3Vpc2EtaG90ZWxzchULEghSb29tVHlwZRiAgICA_KmfCwyiAQ9kYWd1aXNhLWNhbmlsbG8", "ahBlfmRhZ3Vpc2EtaG90ZWxzchQLEgdSZWdpbWVuGICAgKCF5NoKDKIBD2RhZ3Vpc2EtY2FuaWxsbw", "730.23", "7% DESCOMPTE WEB", "FAKE_KEY_FROM_PRESTIGE", "7% DESCOMPTE WEB"]], "prices_per_day": [[261.73, 261.73, 261.73]], "promotions": [{"promotion": "FAKE_KEY_FROM_PRESTIGE", "valuesPerDay": [18.32, 18.32, 18.32], "forced_promotion_name": "7% DESCOMPTE WEB", "multiple_promotions": [{"price": [18.32, 18.32, 18.32], "key": "FAKE_KEY_FROM_PRESTIGE"}], "value": 54.96, "lateBookingDays": None, "ficticious": None, "promotionName": "7% DESCOMPTE WEB", "promotionNameList": [{"name": "7% DESCOMPTE WEB"}]}], "currency": "EUR", "additional_service": [{"key": "ahBlfmRhZ3Vpc2EtaG90ZWxzchcLEgpTdXBwbGVtZW50GICAgOCkv9AIDKIBD2RhZ3Vpc2EtY2FuaWxsbw", "name": "Parking para coches ", "description": "&iexcl;Reserva ya tu aparcamiento en el Hotel!", "amount": 1, "days": 3, "daysVisible": None, "price": 45.0, "nights_custom": None, "confirmation_text": None, "calendar_selection": False, "all_stay_message": True}], "search": {"startDate": "2024-12-06", "endDate": "2024-12-09", "countryCode": "es", "acceptIncompleteRates": None, "rooms": [{"numKids": 1, "numBabies": 0, "numAdults": 2, "kidAges": "8", "total_persons": 3, "agesKid": ["8"]}], "agentId": None, "numDays": 3, "language": "ca", "promoCode": "", "source": "Web", "perfil": "", "ofertas": "", "package_preselection": None, "package_order": None, "hidden_name": None, "only_prices": None, "hidden_promocode": None, "roomFilter": ".*", "advanced_filters": None, "min_stay_endDate": None}, "payed": 0, "geo_location_info": {"ip_address": "*************", "X-AppEngine-Country": "ES", "X-AppEngine-City": "madrid", "X-AppEngine-Region": "md", "X-AppEngine-CityLatLong": "40.416775,-3.703790", "longitud": "40.416775", "latitud": "-3.703790"}, "original_referer": "https://www.hotelfontdargentcanillo.com/ca/", "language": "CATALAN", "geolocation": "es", "identifier": "71567970", "shopping_cart_info": None, "agesByRoom": [{"kids": [8], "babies": []}]}
    pass
# gateways_format_price(2)
# send_emails()
# room_with_occupants()

# lista_gigante = [{'CODE': 'OK', 'GATEWAY_EXTRA_INFO': {'ds_ExpiryDate': '82446948332da0be8115ef94c73184111a029014', 'ds_MerchantIdentifier': '82446948332da0be8115ef94c73184111a029014', 'no_redirect': True, 'sermepa_cof_txnid': '232024217217462'}, 'GATEWAY_ORDER_ID': '24793576', 'GATEWAY_PAID_AMOUNT': '0.0', 'GATEWAY_TOKEN_ID': '82446948332da0be8115ef94c73184111a029014', 'PAYMENT_GATEWAY_NAME': 'REDSYS', 'sermepa_cof_txnid': '232024217217462'}]
lista_gigante = [{'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=f1accbe2-236a-4864-9adb-c3e225db29d5&lang=PORTUGUESE&identifier=88971853&namespace=vidamar-algarve&id=C91F4DD6019EDAE9E7C2D78D3D4A669B.prod02-vm-tx04&resourcePath=%2Fv1%2Fcheckouts%2FC91F4DD6019EDAE9E7C2D78D3D4A669B.prod02-vm-tx04%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=113fe45b-b7de-4b74-9a2e-4fd6e84bac43&lang=PORTUGUESE&identifier=28922951&namespace=vidamar-algarve&id=4EBCE9F08D0B9294A7703E36C3CDA50C.prod02-vm-tx12&resourcePath=%2Fv1%2Fcheckouts%2F4EBCE9F08D0B9294A7703E36C3CDA50C.prod02-vm-tx12%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=75784361-f251-4cf0-a399-99734513925a&lang=PORTUGUESE&identifier=53494789&namespace=vidamar-algarve&id=4C64D97075F8EC5FCEFB4DBE84AB8FF7.prod02-vm-tx07&resourcePath=%2Fv1%2Fcheckouts%2F4C64D97075F8EC5FCEFB4DBE84AB8FF7.prod02-vm-tx07%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=c8e0634b-cf13-4506-8d63-d94163979f01&lang=PORTUGUESE&identifier=87489156&namespace=vidamar-algarve&id=D33A37A7ED2005774986C4F67F48CD87.prod02-vm-tx16&resourcePath=%2Fv1%2Fcheckouts%2FD33A37A7ED2005774986C4F67F48CD87.prod02-vm-tx16%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=e4427913-8ea2-48ae-984f-aa0c6a5a2753&lang=PORTUGUESE&identifier=70916543&namespace=vidamar-algarve&id=D1D083FEE884A2AFFED74D213A2637C2.prod02-vm-tx06&resourcePath=%2Fv1%2Fcheckouts%2FD1D083FEE884A2AFFED74D213A2637C2.prod02-vm-tx06%2Fpayment'}, {'hotel_code': 'wine-books-porto', 'url': '/sibs/merchant_url?sid=a7c1f43f-ae8a-4643-a5f5-d9cdf50015aa&lang=PORTUGUESE&identifier=67664223&namespace=wine-books-porto&id=AA42E57A10AA5474F095C78CD63C30D7.prod02-vm-tx15&resourcePath=%2Fv1%2Fcheckouts%2FAA42E57A10AA5474F095C78CD63C30D7.prod02-vm-tx15%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=c5f51fab-45ba-4629-a30f-5141bea391e0&lang=PORTUGUESE&identifier=64273598&namespace=vidamar-algarve&id=B786CCF31C684617B332DEB51FEF856F.prod02-vm-tx01&resourcePath=%2Fv1%2Fcheckouts%2FB786CCF31C684617B332DEB51FEF856F.prod02-vm-tx01%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=241da332-60d1-4745-89f7-9f0394131ea4&lang=ENGLISH&identifier=33159877&namespace=vidamar-algarve&id=45AD883ACD1E47BEF38100D831862A42.prod02-vm-tx08&resourcePath=%2Fv1%2Fcheckouts%2F45AD883ACD1E47BEF38100D831862A42.prod02-vm-tx08%2Fpayment'}, {'hotel_code': 'vidamar-madeira', 'url': '/sibs/merchant_url?sid=839ad131-f69c-4f4d-9624-f1afb3b0cd92&lang=ENGLISH&identifier=72190386&namespace=vidamar-madeira&id=AE7D48DAEC8CF7B0D5E37F36D7FD7034.prod02-vm-tx10&resourcePath=%2Fv1%2Fcheckouts%2FAE7D48DAEC8CF7B0D5E37F36D7FD7034.prod02-vm-tx10%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=7a57184f-d0c7-4eb2-8e9f-d61f4f10afdc&lang=PORTUGUESE&identifier=51455509&namespace=vidamar-algarve&id=A431117CB54D2BF2959C39F04F9DE9D0.prod02-vm-tx06&resourcePath=%2Fv1%2Fcheckouts%2FA431117CB54D2BF2959C39F04F9DE9D0.prod02-vm-tx06%2Fpayment'}, {'hotel_code': 'vidamar-madeira', 'url': '/sibs/merchant_url?sid=219f0dbf-2815-40bb-b365-3f6a9858f75f&lang=ENGLISH&identifier=62852800&namespace=vidamar-madeira&id=5D45B170F0452B011E88CC0FC850054C.prod02-vm-tx09&resourcePath=%2Fv1%2Fcheckouts%2F5D45B170F0452B011E88CC0FC850054C.prod02-vm-tx09%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=3863951b-c74b-457c-ac10-028d7fc4c64b&lang=ENGLISH&identifier=42497728&namespace=vidamar-algarve&id=1B77726545F4F9B68CE848A996992DDC.prod02-vm-tx02&resourcePath=%2Fv1%2Fcheckouts%2F1B77726545F4F9B68CE848A996992DDC.prod02-vm-tx02%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=35c2eb3a-e0e6-452a-9910-c607c1c052a1&lang=PORTUGUESE&identifier=26867905&namespace=vidamar-algarve&id=C4050EED96B9C58C9C250169E97756E2.prod02-vm-tx05&resourcePath=%2Fv1%2Fcheckouts%2FC4050EED96B9C58C9C250169E97756E2.prod02-vm-tx05%2Fpayment'}, {'hotel_code': 'wine-books-porto', 'url': '/sibs/merchant_url?sid=2b1f98c7-c9cc-48c1-960b-ded34a9937b4&lang=ENGLISH&identifier=14865872&namespace=wine-books-porto&id=FDAE9D6DC0ABCC4CEA83B37BD377E680.prod02-vm-tx17&resourcePath=%2Fv1%2Fcheckouts%2FFDAE9D6DC0ABCC4CEA83B37BD377E680.prod02-vm-tx17%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=3fc39fbc-3497-464d-8000-cf5b21d29bcb&lang=PORTUGUESE&identifier=93962911&namespace=vidamar-algarve&id=97276A3A51C7CABB994AA1ABE757858F.prod02-vm-tx02&resourcePath=%2Fv1%2Fcheckouts%2F97276A3A51C7CABB994AA1ABE757858F.prod02-vm-tx02%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=3d453329-d3e6-4215-864f-a77ac7b20d95&lang=ENGLISH&identifier=11208049&namespace=vidamar-algarve&id=D30673C85A6FC7F8064263598F1E626C.prod02-vm-tx17&resourcePath=%2Fv1%2Fcheckouts%2FD30673C85A6FC7F8064263598F1E626C.prod02-vm-tx17%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=a1a814d8-4e94-45d7-b2de-49bb7730ebf5&lang=ENGLISH&identifier=12221891&namespace=vidamar-algarve&id=76B580364CD716435B37639990B2714E.prod02-vm-tx13&resourcePath=%2Fv1%2Fcheckouts%2F76B580364CD716435B37639990B2714E.prod02-vm-tx13%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=db1e5a8b-9fec-41a0-ae14-cdb2caee98da&lang=ENGLISH&identifier=30840174&namespace=vidamar-algarve&id=3E0EF07F5649F815C9CE970E223D7DF2.prod02-vm-tx14&resourcePath=%2Fv1%2Fcheckouts%2F3E0EF07F5649F815C9CE970E223D7DF2.prod02-vm-tx14%2Fpayment'}, {'hotel_code': 'vidamar-algarve', 'url': '/sibs/merchant_url?sid=c0c50dd7-1cbf-4a4e-bf0c-f3845b29fe76&lang=ENGLISH&identifier=34561914&namespace=vidamar-algarve&id=3B4F24B2689AC53F2160FF4C4B7EDF39.prod02-vm-tx13&resourcePath=%2Fv1%2Fcheckouts%2F3B4F24B2689AC53F2160FF4C4B7EDF39.prod02-vm-tx13%2Fpayment'}]
def test_comprobarrespuestapepycrearreservacontokenencasodequenoexista():

    failed = []
    reservations_made = {}
    total = 0
    for r in lista_gigante:
        hotel_code = r.get('hotel_code')
        _json = {'hotel_code': hotel_code, 'data_session_form': {'hour_flight': '', 'flight_number_from': '', 'accept_conditions_and_policies': '', 'billing_name': '', 'telephone': u'', 'prefix': '', 'numfactu': u'', 'date_main': '', 'id': '', 'share_info_group_chain': '', 'city': '', 'lastName1': u'Jomppanen', 'billing_cif': '', 'extra_fields': {}, 'comments': u'', 'agency_agent_name': '', 'pickup_hour_from': '', 'success_indicator_evo': '', 'postalCode': '', 'agency_email': '', 'email': u'<EMAIL>', 'province': '', 'allow_notifications': False, 'billing_address': '', 'transfer_disabled_to': '', 'transfer_disabled_from': '', 'agency_identifier': '', 'agency_name': '', 'birthday': '', 'personalId': u'', 'address': '', 'lastName2': '', 'password': '', 'chk_hour_flight': '', 'flight_hour_to': '', 'passengers': [], 'name': u'Ola', 'user_club_allow_register': False, 'country': u'FI', 'passengers_total': 0, 'flight_number_to': '', 'numflight': '', 'agency_telephone': ''}, 'response': ''}
        url = f"https://payment-seeker.ew.r.appspot.com/forms/cobrador/proceses_gateway_response?{r.get('url').split('?')[1]}"
        headers = {'Content-Type': 'application/json'}
        try:
            response = requests.post(url, json=_json, timeout=20, headers=headers)
            response_cobrador = json.loads(response.text)
            # response_cobrador = response.json()
        except:
            # response_cobrador = response.json()
            response_cobrador = {}
            print('failed')
            failed.append(r)
        if response_cobrador.get('CODE') == "OK":
            total += 1

            identifier = response_cobrador.get('GATEWAY_ORDER_ID')


            reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,
                                                                                   search_params=[('identifier', '=', identifier)]))
            if len(reservations) >= 1:
                continue

            if len(reservations) < 1:
                pending_reservation = list(datastore_communicator.get_using_entity_and_params('PendingReservation', hotel_code=hotel_code,
                                                                        search_params=[
                                                                            ('identifier', '=', identifier)]))
                if len(pending_reservation) > 0:
                    reservation_data = get_decrypted(pending_reservation[0], 'reservation_data')
                    personal_details = get_decrypted(pending_reservation[0], 'personal_details')
                    data = {
                        "namespace": hotel_code,
                        "reservation_data": json.loads(reservation_data),
                        "personal_details": json.loads(personal_details)
                    }

                    url = "http://localhost:8070/reservations/create/send_reservation?sessionKey=%s" % '9213cecf-b399-4e53-b5af-fe442606048d'
                    response = requests.post(url, json.dumps(data), headers={'Content-type': 'application/json'})
                    message = "KO"

                    if response.status_code == 200:
                        message = "OK"
                        identifier = json.loads(reservation_data).get("identifier")
                        url = "http://localhost:8070/reservations/create/update_entity?namespace=%s&identifier=%s" % (hotel_code, identifier)
                        response = requests.post(url, {})



            hotel_info = get_hotel_by_application_id(hotel_code)
            url = get_inner_url(hotel_info)
            time.sleep(5)
            reservation_to_send = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,
                                                                                   search_params=[('identifier', '=', identifier)]))[0]
            ei = json.loads(reservation_to_send.get('extraInfo'))
            for x, k in response_cobrador.get("GATEWAY_EXTRA_INFO", {}).items():
                ei[x] = k
            r["extraInfo"] = json.dumps(ei)
            datastore_communicator.save_to_datastore("Reservation", int(reservation_to_send.key.id), reservation_to_send, hotel_code=hotel_code)

            email = reservation_to_send.get('email')
            email_sender = ""
            if email:
                email_sender = "&email=" + str(email).replace(";", "%3B")

            send_confirmation_post = build_encrypted_url(
                "%s/send-confirmation/?id=%s&modification=%s&type=%s%s&from_manager2=True" % (
                    url, reservation_to_send.get('identifier'), False, 'customer', email_sender))
            requests.post(send_confirmation_post, json={})

            send_confirmation_post = build_encrypted_url(
                "%s/send-confirmation/?id=%s&modification=%s&type=%s%s&from_manager2=True" % (
                    url, reservation_to_send.get('identifier'), False, 'manager', email_sender))
            requests.post(send_confirmation_post, json={})


def test_get_hotel_codes():
    port_hotels = []
    all_hotels = get_all_hotels()
    for hotel in all_hotels:
        if 'nau-' in hotel:
            port_hotels.append(hotel)
    pass

def test_1():
    reservations = list(
        datastore_communicator.get_using_entity_and_params('Reservation', hotel_code='pure-formosa',
                                                           search_params=[
                                                               ('cancellationTimestamp', '>=', '2024-10-07'),
                                                               ('cancellationTimestamp', '<=', '2024-10-11')]))

    return

def test_get_hotels_with_this_adapter_active(adapter_name='siteminder'):
    all_hotels = get_all_hotels()
    hotels = []

    for hotel_code in all_hotels:
        # if 'estival' not in hotel_code and 'blau' not in hotel_code and 'sella' not in hotel_code and 'ramblas' not in hotel_code:
        #     continue
        try:
            xml = get_integration_configuration_of_hotel(get_hotel_by_application_id(hotel_code), adapter_name)[0].get('downloadBooking')
        except:
            xml = None
        if xml:
            hotels.append(hotel_code)

    pass

def test_get_possibple_duplicated_external_identifiers_siteminder():
    reservationdict = {}
    all_hotels_with_siteminder = ['hotel-pradillo', 'hotel-zen', 'hotel-bahiasur', 'flatotelcostadelsol', 'hotel-parasol-garden', 'hotel-costa-caleta', 'hotel-playa-real', 'hotel-nerja-club', 'hotel-royal-costa', 'hotel-royal-al-andalus', 'hotel-antequera-golf', 'hotel-la-magdalena', 'estrellademar', 'arenacenter', 'golfcenter', 'zerbinetta', 'gavimarcalagranhotel', 'gavimarlamirada', 'gavimararielchico', 'sevillaalmeria', 'hotel-don-pancho', 'mc-sandiego', 'son-perdiu', 'hotel-neptuno', 'casual-ilbira-granada', 'summum-joan-miro', 'summum-rosellon', 'nuriasol', 'mint-casabonita', 'atenea-park', 'essence-donpaquito', 'rincon-granvia', 'on-residence', 'ona-hacienda-alamo', 'pyr-fuengirola', 'ar-parquesur', 'villa-sallent', 'casual-letras-sevilla', 'ona-alanda-club', 'urbansense-oboemadrid', 'juderia-cordoba', 'mc-soul', 'sno-pallars', 'habitus-leyre', 'hotel-myramar', 'demo9', 'adaria-vera', 'villa-nazules', 'raiz-boutique', 'hemd-gloria', 'derby-sevilla', 'singular-ferrero', 'u-sense-granada-gran-via', 'hotel-sevilla', 'noa-boutique', 'fuerte-estepona', 'nexus-club-tenerife', 'atlantico', 'casual-jazz-sansebastian', 'princesa-solar', 'luxor-hotel', 'dreamland-heights', 'alamos-benidorm', 'santamaria15', 'ona-valle-romano', 'q10-caspe', 'landmar', 'axis-porto', 'hemd-zaida', 'colony-hotel', 'suites-tarifa', 'casual-vintage-valencia', 'sant-pere-bosc', 'puerto-sherry', 'albegaria-pedra', 'rali-viana', 'ona-sueno-azul', 'gran-proa', '4us-manga-vip', 'aranzazu-carlton', 'som-dona', 'cortijo-zahara', 'hotel-pintor', 'ona-palmira-paguera', 'seayou-valencia', 'axis-vermar', 'port-benidorm', 'ona-dorada-tarter', 'amister', 'landmar-arena', 'reino-nevado', 'guitart-gold', 'prinsotel-caleta', 'ap-eva', 'azoris-faial', 'port-jardin', 'nura-ponsa', 'zahara-sol', 'ar-farnesio', 'orly-5casitas', 'ona-claveles', 'daguisa-goldenfenix', 'sandandsea-cardones', 'reino-nevasur', 'prinsotel-malpas', 'acebuchal', 'ocean-view-apartments', 'orly-arena', 'terro-braga', 'serenity-boutique', 'axis-ponte-lima', 'ona-brisas', 'golf-resort-royalgarden', 'casual-civilizaciones', 'azoris-angra', 'hotel-dynastic', 'elaya-hotel-wolfenbuettel', 'ona-jardines-paraisol', 'singular-fonte', 'aptos-cruces', 'habitus-lorenzo', 'vista-real', 'urbansense-oboegranada', 'prinsotel-dorada', 'charming-estreito', 'nexus-hacienda4', 'magaluf-playa', 'feetup-purple-nest-hostel', 'xq-palacete', 'mc-faro', 'port-elche', 'greenland-bubble', 'smy-civico-zero', 'barlovento', 'ona-acaunada', 'brisamar-canteras', 'nexus-ocean-vistas', 'stein-chateau-eza', 'jimesol', 'casa-de-la-vieja', 'ms-tropicana', 'palacio-dorobe', 'cartuja', 'simon-sevilla', 'hacienda-cusco', 'finca-almeji', 'zeus-malaga', 'zione-cartagena', 'ap-adriana', 'ona-living-barcelona', 'el-balsamo', 'casual-raizes', 'almeji-palmera', 'casual-belle-epoque', 'valise-cdmx', 'luxor-villas', 'azoris-royal', 'summum-zurbaran', 'demo-siteminder', 'palacio-maria-luisa', 'sno-escarrilla', 'hotel-perla-marina', 'yoy-edelweiss-aptos', 'urbansense-bellasevilla', 'mint-ecodelmar', 'elaya-hotel-vienna-city-center', 'ap-cabanaspark', 'fhb-casa-liza', '4us-ocean-boulevard', 'smy-carlosv-alghero', 'ona-diana-park', 'zuana', 'sno-bielsa', 'urbansense-bellagranada', 'demo12', 'smy-bologna-centrale', 'jardin-reina', 'ap-sinerama', 'singular-la-marina', 'agaro', 'psantiago-iii', 'ona-marbella', 'ona-alborada', 'smy-aran-blu', 'smy-koflerhof-dolomiti', 'olivia-river1872', 'ms-alay', 'elaya-hotel-oberhausen', 'playa-victoria', 'fhb-maria-isabela', 'feetup-red-nest', 'elaya-hotel-steinplatte', 'suitesonsouthbeach', 'elaya-hotel-augsburg', 'xq-vistamar', '4us-rioja-wine', 'mio-vallarta', 'fhb-milkaella', 'summum-ratxo', 'regency-salgados', 'mint-saman', 'alanda-marbella', 'fhb-pila-seca', 'smy-louise-bruxelles', 'port-vista', 'smy-eulalia-algarve', 'elaya-hotel-leipzig-city-center', 'smy-orizontes-santorini', 'cool-tisalaya', 'q10-cuevas', 'hostal-beatriz', 'palacio-corredera', 'fhb-santa-ana', 'smy-lisboa', 'sno-villasallent3', 'q10-etxarri', 'elaya-hotel-hannover-city', 'ap-victoria', 'belver-principereal', 'sno-colomers', 'super8-freiburg', 'elaya-hotel-regensburg-city-center', 'hotel-ninays', 'molino-enmedio', 'peso-village', 'q10-ampolla', 'fhb-casa88', 'super8-koblenz', 'fhb-casa-florida', 'palmeramar', 'psantiago-v', 'casual-rinascimiento-florencia', 'elaya-hotel-villach', 'sno-cerler', 'elaya-hotel-vienna-city-west', 'reino-anamaria', 'psantiago-iv', 'ona-club-bena-vista', 'fhb-naramu', 'smy-mediterranean-white', 'singular-rb-luxury', 'casa-marisa', 'elaya-hotel-stuttgart-boeblingen', 'summum-virrey-finca', 'casa-dorada', 'elaya-hotel-ludwigsburg', 'ona-mar-menor-aptos', 'smy-santorini-suites', 'flipflop-cala-mandia', 'elaya-hotel-munich-schwabing', 'fhb-casaluna-boutique', 'flipflop-cala-romantica', 'elaya-hotel-munich-city', 'fhb-mansion-bosque', 'smy-mykonos', 'mesaluna-short-long', 'ur-avenida', 'fhb-hacienda-arcangeles', 'super8-munich-city-north', 'fhb-maria-bonita', 'hemd-angeles', 'super8-hamburg-mitte', 'fhb-villa-helena', 'elaya-hotel-kleve', 'guitart-aptohotel-central', 'suites-pintor', 'ona-lomas-manga-club', 'barcarola', 'nexus-club-tarahal', 'villa-sallent2', 'elaya-hotel-goeppingen', 'elaya-hotel-frankfurt-oberursel', 'toboso-plaza', 'miramar-cantabria', 'vik-coralbeach', 'navegadores', 'ona-casa-lit', 'riversun-monteparaiso', 'fariones-apartamentos', 'sandandsea-lagos', 'nexus-adelfas', 'casa-merced', 'greco-pintor', 'don-paco-malaga', 'hotel-leyre', 'ona-palmira-paradise', 'som-llaut', 'ar-golf-almerimar', 'sao-miguel-de-arcos', 'marinas-de-nerja', 'ona-princesa', 'casual-olas-sansebastian', 'singular-garza', 'sherry-suites', 'singular-diego', 'ona-marques', 'corona-atarfe', 'tarifa-lances', 'ms-aguamarina', 'ona-palamos', 'ona-rampas', 'ona-mojacar', 'casual-don-juan-tenorio', 'marsol-hotel', 'juderia-sanfrancisco', 'primavera-park', 'mirador-fuerteventura', 'malvasia', 'riversun-veleros', 'tanat', 'demo-qa2', 'port-azafata', 'toboso-aparturis', 'charming-vistas', 'elaya-hotel-rostock', 'ona-drach', 'sno-candanchu', 'ms-fuente', 'ar-almerimar', 'fariones-avenida', 'ms-amaragua', 'america-sevilla', 'parkhouse-101', 'ona-las-zarzas', 'super8-augsburg', 'casual-pop-art', 'yoy-isaba', 'joy-plaza-de-armas', 'ona-bahia-blanca', 'mc-trebol', 'test3-copia1', 'casual-duende', 'sweet-cullera', 'aranzazu-sansebastian', 'feetup-nest-style', 'ona-garden-lago', 'mimosas-banus', 'la-mirage', 'hemd-puertareal', 'mesaluna', 'singular-mundo-verde', 'indurain-toro', 'ourabay', 'cantabria-puerta', 'singular-tropical', 'axis-viana', 'sno-pazo', 'lb-lebrija', 'estival-vendrell', 'free-roma', 'casa-romana-boutique', 'port-denia', 'gala-placidia', 'casual-natura-valencia', 'lesbrases', 'som-suretcool', 'sno-aragon', 'greco-antidoto', 'valise-san-miguel', 'som-far', 'fenals-garden', 'benalmadena-beach', 'club-maspalomas', 'palace-albir', 'onhotel', 'som-central', 'riversun-jacarandas', 'prinsotel-alba', 'ateneo', 'tarik', 'ms-maestranza', 'checkin-antequera', 'primavera-loix', 'bahiasur-apartamentos', 'convento-seixo', 'valderrabanos', 'ona-aquamarina', 'mint-caracol', 'nura-condor', 'fariones-suites', 'summum-ventas', 'singular-montico', 'moremar', 'vilagaros', 'lisbon-best-choice', 'nura-can-beia', 'casual-apartments-malaga', 'marsol-encantada', 'pato-amarillo', 'hotel-iris', 'nura-boreal', 'charming-angra', 'creixell', 'belver-donamaria', 'test3', 'yoy-edelweiss', 'port-huerto', 'brisa-sol', 'port-europa', 'casual-cine-valencia', 'singular-finca-fabiola', 'som-ars-magna', 'mc-maryciel', 'vincci-aleysa', 'playagolf-islantilla', 'infanta-antequera', 'toctoc-olof', 'singular-toloriu', 'test5-copia3', 'prinsotel-pineda', 'maestranza-ronda', 'estacada', 'cool-marina2', 'hotel-mariner', 'almeji-benadalid', 'la-barracuda', 'kent-hotel', 'dreamland-mirage', 'marsol-blau', 'plazuela-carbon', 'sevilla-este', 'dom-jose-beach', 'casual-deportes', 'landmar-gigantes', 'prinsotel-villas', 'ona-benidorm', 'ona-ogisaka', 'las-palmeras', 'pio-apartments', 'bbou-vinuela', 'nura-rosa', 'hacienda-cusco-centro', 'casa-de-colon', 'elaya-hotel-hamburg-finkenwerder', 'puerta-sevilla', 'belver-aldeia', 'cortijo-mimbrales', 'elaya-hotel-kevelaer', 'riversun-eco-nos', 'yellow-praia', 'cervantes-sevilla', 'ona-campanario', 'dreamland-hills', 'ur-m-house', 'gran-playa', 'guitart-lamolina', 'bahia-cadiz', 'casual-colours', 'q10-portus', 'feetup-youth-hostel', 'b3-virrey', 'yoy-tredos', 'ona-hacienda-alamo-hotel', 'ap-marianova', 'fhb-agaves', 'sirena-aptos', 'ona-internacional', 'free-barcelona', 'estival-vilamari', 'q10-teacampa', 'smy-tahona-fuerteventura', 'reino-granada', 'hotel-sevilla-cadiz', 'adh-montesol', 'loule-jardim', 'apartamentos-vall-boi', 'landmar-test', 'port-fiesta', 'taiga-valdevaqueros', 'guitart-passage', 'meridional-arcos', 'puerto-palace', 'bahiablanca', 'axis-ofir', 'casual-bianco-firenze', 'clube-maria-luisa', 'anoreta-golf', 'nexus-hacienda-village', 'fay-victoria', 'ap-lago-montargil', 'aranzazu-abando', 'singular-nansa', 'checkin-magdalena', 'sensation-authentic', 'ona-cala-pi', 'dreamland-palmbeach', 'singular-casagrande', 'poza', 'sirena-4', 'sensation-sagrada', 'cartuja-suites', 'casual-incas', 'ap-dona-aninhas', 'ona-mosaic', 'ona-marina-arpon', 'mint-naturacabana', 'casual-mar-malaga', 'super8-munich-city-west', 'hacienda-pruno', 'hostal-micaela', 'hostal-marissal', 'palm-playa', 'yoy-vall-boi', 'yoy-unhola', 'q10-conil', 'dolmenes-antequera', 'ona-mar-menor', 'super8-mainz-zollhafen', 'belver-boavista', 'toboso-almunecar', 'ona-benalmar', 'selu', 'vostra-llar', 'ona-suites-salou', 'hospital-benasque', 'pato-rojo', 'fhb-quetzal', 'mc-amalia', 'summum-poblado-suites', 'yoy-plaza', 'mint-bannister', 'baia-grande', 'super8-chemnitz', 'singular-balsamo', 'toctoc-canteras', 'zero-drach', 'ona-village-cala-dor', 'hemd-sanandres', 'smy-kos', 'super8-oberhausen', 'ona-rosas', 'playa-catedrales', 'aljarafe-suites', 'som-rosella', 'sno-beret', 'fhb-portal-miguel-allende', 'golf-resort-river', 'matalascanas', 'valise-tulum', 'palacete', 'arova-tulum', 'puertobahia-spa', 'greco-delfin', 'pasarela-sevilla', 'flipflop-surfing', 'singular-la-escondida', 'casual-artes-valencia', 'sno-formigal', 'boho-marbella', 'test3-copia2', 'ba-sohotel', 'urbansense-oboesevillaii', 'olivia-exmo', 'lima-marbella', 'singular-jardines-mata', 'vale-dazenha', 'ap-oriental', 'overland-suites', 'riversun-riosol', 'sarga', 'maritim-galatzo', 'som-llevant', 'ar-arcos', 'palau-apartments', 'hacienda-cusco-plaza', 'vik-villavik', 'event-tarifa', 'entremares-hotel', 'riversun-cocoteros', 'contemplacion', 'marsol-condado', 'hotel-first-class', 'sao-felix', 'namaste-beach', 'guitart-rosa', 'joy-setas-coworking', 'casa-carolina', 'hotel-alay', 'dynastic-apartamentos-rooms', 'sintra-marmoris', 'hn-negresco', 'urbansense-oboesevillai', 'el-guarapo', 'ona-aldea-mar', 'hotel-clarin', 'sanroque-garden', 'taiga-dunas', 'casual-teatro-madrid', 'sol-y-miel', 'balneario-vichy-catalan', 'port-alicante', 'montepiedra', 'on-aleta-room', 'diamar-lanzarote', 'puerta-ribera', 'hotel-born', 'seayou-saplaya', 'som-espanya', 'santuario-spa', 'benalmadena-palace', 'baia-cristal', 'yoy-mont-romies', 'estival-torre-mora', 'sant-roc', 'sangil', 'mc-buenosaires', 'basic-manuela', 'hotel-spa-congreso', 'casas-arenal', 'xq-finca-salamanca', 'prazer-natureza', 'ecorkhotel', 'smy-vistamar', 'torre-nunez', 'v-vejer', 'casual-socarrat', 'ecuadorpark', 'axis-basic-braga', 'apartamentos-neptuno', 'em-ande', 'isla-mallorca', 'test-backend5', 'singular-tempus', 'charming-monte', 'hotel-vall-boi', 'juderia-trinidad', 'elimar', 'casa-relator', 'bbou-cortijo', 'aptos-bahia-cadiz', 'fuengirola-beach', 'playamaro', 'basic-sevilla', 'vasco-gama', 'sno-villasallent4', 'baltum', 'don-ramon', 'axis-club', 'port-feria', 'alito-tulum', 'urbansense-hostelsevilla', 'olivia-marques', 'president-alcudia', 'el-patio', 'ona-casitas', 'natura-algarve', 'axis-casa-anquiao', 'indomito', 'belle-marivent', 'em-cartagena', 'quinta-pedra']
    for hotel_code in all_hotels_with_siteminder:
        reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,
                                                               search_params=[('startDate', '>', '2024-10-24')]))
        for reservation in reservations:
            extra_info = json.loads(reservation.get('extraInfo'))
            identifier = reservation.get('identifier')
            external_identifier_for_manager = extra_info.get('external_identifier_for_manager', '')
            if f"0{identifier}" in external_identifier_for_manager:
                if reservationdict.get(hotel_code):
                    reservationdict[hotel_code].append(identifier)
                else:
                    reservationdict[hotel_code] = [identifier]

    pass

def test_filter_test_get_possibple_duplicated_external_identifiers_siteminder():
    duplicada_seguro={}
    all_reservations = {'hotel-pradillo': ['475219489'], 'hotel-don-pancho': ['30198845'], 'casual-ilbira-granada': ['A46021680', '6BBFBACAB'], 'summum-rosellon': ['76316800', '24537380'], 'nuriasol': ['61F753117'], 'rincon-granvia': ['E689509B3'], 'casual-letras-sevilla': ['BE-63591969'], 'ona-alanda-club': ['76498530-1'], 'juderia-cordoba': ['4D7CAC4EF'], 'derby-sevilla': ['7978D662F'], 'dreamland-heights': ['49498010'], 'alamos-benidorm': ['14033384'], 'q10-caspe': ['93111530'], 'aranzazu-carlton': ['26411030'], 'seayou-valencia': ['76414050'], 'port-benidorm': ['54352508'], 'ona-dorada-tarter': ['62314320'], 'landmar-arena': ['BE-39882648', 'OF-52025296', 'BE-42281815-DT', 'BE-79835419-DT', 'OF-4BDB11DC3', 'OF-03A0273C8', 'OF-C0013AB2C', 'OF-33E4615F7', 'OF-88960366', 'OF-A95CE2732', 'OF-D0F303CAE', 'OF-3E6C668A3', 'OF-BE143AC75'], 'prinsotel-malpas': ['50292740'], 'hotel-dynastic': ['13218326', '23862065'], 'port-elche': ['45183876'], 'casual-raizes': ['BE-66211989'], 'zuana': ['R59870405C'], 'sno-bielsa': ['32058771'], 'agaro': ['D98FBAE52', '73C833109'], 'psantiago-iii': ['BE-40064045', 'BE-95881902'], 'elaya-hotel-leipzig-city-center': ['438D8683A', 'A77E4E7EE'], 'q10-cuevas': ['97365170'], 'peso-village': ['51165030'], 'psantiago-iv': ['BE-66371463', '87614025', 'BE-81771108'], 'ona-casa-lit': ['39454882'], 'fariones-apartamentos': ['65868950'], 'marinas-de-nerja': ['41139070', '23026710'], 'ona-marques': ['64889260'], 'primavera-park': ['81726940', '87601370'], 'toboso-aparturis': ['68186470'], 'fariones-avenida': ['39877070'], 'mc-trebol': ['34448F36D'], 'club-maspalomas': ['18970000', '69860270'], 'ateneo': ['BB590C9B3'], 'fariones-suites': ['12999420', '86849790'], 'marsol-encantada': ['91053300'], 'port-huerto': ['50780928', '51217819', '64597759'], 'port-europa': ['24615264', '64263291', 'R7AE2A2EC9'], 'dreamland-mirage': ['52696910', '71999610'], 'dom-jose-beach': ['67078908'], 'landmar-gigantes': ['OF-25111128', 'OF-25847912', 'OF-AFFBAAA3D', 'BE-2CD9243BA', 'OF-3DB569574', 'BE-E33155682', 'OF-139EC0F2E', 'OF-87722408-DT', 'OF-36360829-DT', 'BE-E3762E1AB', 'BE-46891314', 'OF-3642120BD', 'BE-3622B6174'], 'elaya-hotel-hamburg-finkenwerder': ['8EF79F32E'], 'bahia-cadiz': ['62088560', '91171150', '84298250', '13009560'], 'q10-teacampa': ['42074080'], 'puerto-palace': ['85475050'], 'nexus-hacienda-village': ['36587340'], 'ona-cala-pi': ['33541190'], 'ap-dona-aninhas': ['C9A2348F4'], 'casual-mar-malaga': ['BE-96652208'], 'mint-bannister': ['A2364ED55'], 'ona-village-cala-dor': ['76235530-1'], 'golf-resort-river': ['D9D00411E', '3195F8979'], 'hotel-alay': ['49606750'], 'port-alicante': ['78044583'], 'basic-manuela': ['A6376A62D'], 'casas-arenal': ['37350070'], 'em-ande': ['56819161'], 'isla-mallorca': ['52511801'], 'ona-casitas': ['29218540']}
    for hotel_code, list_identifiers in all_reservations.items():
        for identifier in list_identifiers:
            reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,search_params=[('identifier', '=',identifier)]))
            real_identifier_log = list(datastore_communicator.get_using_entity_and_params('BookingRequestLog',[('identifier', '=', identifier)],hotel_code="admin-hotel"))
            if reservations:
                reservation = reservations[0]
            else:
                reservation = {}
            if real_identifier_log:
                identifier_log = real_identifier_log[0]
            else:
                identifier_log = {}
            if reservation.get('cancelled'):
                continue

            reservation_ts = reservation.get('timestamp')
            brl_ts = identifier_log.get('requestTimestamp')
            if not diferencia_mayor_a_2_horas(reservation_ts, brl_ts):

                if duplicada_seguro.get(hotel_code):
                    duplicada_seguro[hotel_code].append(identifier)
                else:
                    duplicada_seguro[hotel_code] = [identifier]
    pass


def diferencia_mayor_a_2_horas(fecha1_str, fecha2_str):
    # Convertir las cadenas de texto en objetos de tipo datetime
    formato = "%Y-%m-%d %H:%M:%S"
    fecha1 = datetime.strptime(fecha1_str, formato)
    fecha2 = datetime.strptime(fecha2_str, formato)

    # Calcular la diferencia en horas
    diferencia_horas = abs((fecha2 - fecha1).total_seconds() / 3600)

    # Verificar si la diferencia es mayor a 2 horas
    return diferencia_horas > 2


def test_set_new_external_identifier():
    hotel_code = 'isla-mallorca'
    identifier = "12342BBD0"
    new_external_identifier = "PTH-"+"12342BBD0"
    reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,
                                                                           search_params=[('identifier', '=', identifier)]))
    logging.info(identifier)
    print(identifier)

    if len(reservations) > 0:
        r = reservations[0]
        extrainfo = json.loads(r.get('extraInfo'))
        extrainfo["external_identifier_for_manager"] = new_external_identifier

        r["extraInfo"] = json.dumps(extrainfo)
        datastore_communicator.save_to_datastore("Reservation", int(r.key.id), r, hotel_code=hotel_code)


def test_get_reservations_with_different_paymentorderid():
    hotel_code = 'estival-vendrell'


    # reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,
    #                                                                        search_params=[('startDate', '>', '2024-11-11')]))

    reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code))
    identifiers = []
    for r in reservations:
        # if r.get('cancelled'):
        #     continue
        ei = json.loads(r.get('extraInfo'))
        paymentOrderId = ei.get('paymentOrderId')
        if paymentOrderId and paymentOrderId not in r.get('identifier'):
            identifiers.append(r.get('identifier'))

        pass
    pass

def test_template():
    # app.py - Backend in Python using Flask
    from flask import Flask, render_template, request, jsonify
    import json

    app = Flask(__name__)

    # Sample JSON data
    data = {
        "name": "John Doe",
        "age": 30,
        "address": {
            "street": "123 Main St",
            "city": "Anytown",
            "zipcode": "12345"
        },
        "hobbies": ["reading", "traveling", "swimming"]
    }



    context = {
        'data': data
    }
    return build_template("src/paraty/pages/reservations/test.html", context)


def test_encontrar_reservas_loopcontent():
    broken_list = []
    oasis = []
    all_hotels = get_all_hotels()
    for hotel in all_hotels:
        if 'oasishoteles-' in hotel:
            oasis.append(hotel)

    for hotel in oasis:

        reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel,
                                                                                search_params=[('timestamp', '>', '2024-11-21')]))
        for reservation in reservations:
            ei = json.loads(reservation.get('extraInfo'))
            schr = ei.get("shopping_cart_human_read", {})
            if not schr:
                continue
            rooms = schr.get("rooms", [])
            broken = False
            for room in rooms:
                index = room.get("step_index", "")
                if isinstance(index, int):
                    continue
                if isinstance(index, str) and "LoopContext" in index:
                    broken = True
                    if reservation.get("identifier") not in broken_list:
                        broken_list.append(reservation.get("identifier"))
                    room["step_index"] = int(index.split("/")[0].split(" ")[1])
                else:
                    continue
            if not broken:
                continue
            schr["rooms"] = rooms
            ei["shopping_cart_human_read"] = schr
            reservation["extraInfo"] = json.dumps(ei)
            datastore_communicator.save_to_datastore("Reservation", int(reservation.key.id), reservation, hotel_code=hotel)

            pass
    print(broken_list)
    pass

def test_encontrar_reservas_con_currency_distinta_a_la_tarifa():
    broken_list = []
    dict_ = {}
    cont = 0
    oasis = []
    all_hotels = get_all_hotels()
    for hotel in all_hotels:
        if 'parkroyal-' in hotel and not 'cozumel' in hotel:
            oasis.append(hotel)

    for hotel in oasis:
        dict_[hotel] = {}
        search_params = [('timestamp', '>', '2024-11-01')]
        # search_params = [('identifier', '=', '33862409')]
        reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel,
                                                                                search_params=search_params))
        rate_dict = {x.id: x for x in get_using_entity_and_params('Rate', hotel_code=hotel)}
        for reservation in reservations:
            ei = json.loads(reservation.get('extraInfo'))
            currency = ei.get('currency', '')
            try:
                rate = ei.get('RateConditionsOrigin', '').get('name', '')
            except:
                rate = ''
            if currency not in rate:
                dict_[hotel][reservation.get('identifier')] = {
                    "paid": ei.get("payed", 0),
                    "currency": currency,
                    "rate": rate
                }
                cont += 1
            # datastore_communicator.save_to_datastore("Reservation", int(reservation.key.id), reservation, hotel_code=hotel)

            pass
    print(broken_list)


def test_cambiar_url_del_hotel_manager_2_push():
    hotel_codes = ["soho-tiburon","soho-museo","soho-malaga","soho-vistahermosa-apartamentos","soho-cadiz","itaca-colon","soho-catedral","soho-congreso","soho-puerto-apartamentos","itaca-moon-dreams-fuengirola","soho-palillero","soho-bahia","soho-oviedo","soho-naranjos","itaca-jerez","itaca-sevilla","soho-moon-dreams","soho-corpo","soho-canalejas","itaca-artemisa","soho-noja","soho-sevilla","soho-pombo","soho-moon-dreams-cortijo","soho-capuchinos","soho-jerez","soho-almerimar","soho-turia","soho-colon","soho-puerto","soho-hoy","soho-tetuan","landmar-gigantes","soho-salamanca","soho-columela","soho-atalia","soho-vegas","soho-fernando","soho-moon-dreams-roquetas","itaca-barcelona","soho-vistahermosa-hotel","soho-cordoba","soho-caceres","soho-urban","soho-catalina","soho-gabriel","soho-equitativa","soho-opera"]
    for hotel_code in hotel_codes:
        configurations = get_using_entity_and_params('IntegrationConfiguration', hotel_code=hotel_code,
                                                     return_cursor=True)
        for configuration in configurations:
            if configuration.get('name', '') == "BookingValidation":
                for config in configuration.get('configurations'):
                    value = config.split(" @@ ")
                    configuration_key = value[0]
                    if "url" == configuration_key:
                        new_value = value[1].replace('https://hotel-manager-2-dot-admin-hotel.appspot.com', 'https://hotel-manager-2-264405814672.europe-west1.run.app')
                        configuration.get('configurations').remove(config)
                        configuration.get('configurations').append(f'{configuration_key} @@ {new_value}')
                        configuration.get('configurations').append(f'_{configuration_key} @@ {value[1]}')

                        datastore_communicator.save_to_datastore("IntegrationConfiguration", configuration.id, configuration,
                                                                 hotel_code=hotel_code)
                        break
                break
        pass



def test_w2m():
    url = "http://o7hotels.com/api/v1/checkout"

    headers = {
        "Accept-Language": "es_ES",
        "commerce": "VIAJESEROSKI",
        "Content-Type": "application/json",
        "market": "3RXaCJCb42bpVYDcyHpU2SVf5wGDNk87"
    }

    data = {
        "booking": {
            "bookingId": "FW-JAR-1686239055677",
            "backURL": "https://booking.viajeseroski.com/detail/",
            "description": "information product booking",
            "startDate": "2023-07-15",
            "endDate": "2023-07-30",
            "amount": {
                "value": 1.00,
                "currency": "EUR"
            }
        }
    }

    response = requests.post(url, headers=headers, json=data)
    pass



def test_resortcom_key():

    secret_key_sha256_b64 = base64.b64decode('sq7HjrUOBfKmC576ILgskD5srU870gJ7')
    key_for_signature = encrypt_message_DES3('47239870', secret_key_sha256_b64)
    cobrador_parameters = {"payment_order_id": '47239870',
                           "amount": '789.86',
                           "response_code": "OK",
                           "payment_by_api_info": {}
                           # maybe you want to encrypt extra_data to get it in process_response
                           }

    cobrador_parameters_SHA256 = json.dumps(cobrador_parameters)
    cobrador_parameters_SHA256 = cobrador_parameters_SHA256.encode("utf-8")
    cobrador_parameters_SHA256 = base64.b64encode(cobrador_parameters_SHA256)

    encrypted_parameters = hmac.new(key_for_signature, cobrador_parameters_SHA256, digestmod=hashlib.sha256).digest()

def encrypt_message_DES3(message, password):
    iv = b'\0\0\0\0\0\0\0\0'
    des3 = DES3.new(password, DES3.MODE_CBC, iv)
    return des3.encrypt(message.encode())


def test_crear_reservas_por_identificador_hotel_code():
    diccionario = {'parkroyal-vallarta': ['75760928']}
    reservation = ''

    for hotel_code, identifiers in diccionario.items():
        for identifier in identifiers:
            reservation_list = get_using_entity_and_params('PendingReservation', hotel_code=hotel_code,
                                                           order_by="-timestamp")
            reservation_list = [item for item in reservation_list if item["identifier"] == identifier]

            if reservation_list:
                reservation = decrypted(reservation_list[0])

def test_reservas_canceladas_con_email_de_t2t():
    reservas = {}
    for hotel_code in get_all_hotels():
        try:
            reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,search_params=[('timestamp', '>=', "2024-12-10")]))
        except:
            logging.info(f"{hotel_code} failed")
            continue
        for reservation in reservations:
            if reservation.get('cancelled') and "ring2travel." in reservation.get('email'):
                if reservas.get(hotel_code):
                    reservas[hotel_code].append(reservation.get('identifier'))
                else:
                    reservas[hotel_code] = [reservation.get('identifier')]


    pass
    print(reservas)


def test_reservas_con_pago_duplicado_falso():
    reservas = {'contador': 0}
    hotel_code_filter = 'oasis'
    for hotel_code in get_all_hotels():
        if hotel_code_filter not in hotel_code:
            continue
        try:
            reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,search_params=[('timestamp', '>=', "2025-01-13")]))
        except:
            logging.info(f"{hotel_code} failed")
            continue
        for reservation in reservations:
            extra_info = json.loads(reservation.get('extraInfo'))
            if float(extra_info.get('payed', 0)) and abs(float(reservation.get('price')) - (float(extra_info.get('payed'))/2)) <= 1:
                if reservas.get(hotel_code):
                    reservas[hotel_code].append({reservation.get('identifier'): f"{reservation.get('timestamp')} - {float(reservation.get('price'))} - {float(extra_info.get('payed'))}"})
                else:
                    reservas[hotel_code] = [{reservation.get('identifier'): f"{reservation.get('timestamp')} - {float(reservation.get('price'))} - {float(extra_info.get('payed'))}"}]

                extra_info["payed"] = float(reservation.get('price'))
                reservation["extraInfo"] = json.dumps(extra_info)
                datastore_communicator.save_to_datastore("Reservation", int(reservation.key.id), reservation, hotel_code=hotel_code)

                reservas['contador'] += 1
        pass
    pass


def test_descomprimir_zlib():
    descomprimido = zlib.decompress(base64.b64decode("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"))
    pass

def test_save_to_metadata():
    payload = {
        "checkout_id": "checkout_id"
    }
    datastore_communicator.get_using_entity_and_params("ReservationMetadata",search_params=[("identifier", "=", str(31425611))], hotel_code="santiago-de-arma")
    properties = {
        "sid": "test",
        "identifier": "test",
        "hotel_code": "test-backend10",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "extraInfo": json.dumps(payload)
    }

    # datastore_communicator.save_to_datastore("ReservationMetadata", None, properties,
    #                                             hotel_code="payment-seeker")

    pass

def test_():
    reservas = {}

    for hotel_code in get_all_hotels():
        try:
            reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,
                                                                                   search_params=[
                                                                                       ('timestamp', '>=', "2025-02-11")]))
        except:
            reservations = []
        for reservation in reservations:
            if "hotmail" in reservation.get('email', '') or "outlook" in reservation.get('email', ''):
                pass
                if reservas.get(hotel_code):
                    reservas[hotel_code].append([reservation.get('identifier'), reservation.get('email')])
                else:
                    reservas[hotel_code] = [[reservation.get('identifier'), reservation.get('email')]]
        pass
    pass


def test_get_nrf_not_paid():
    reservas = {}

    for hotel_code in get_all_hotels():
        try:
            if not get_configuration_property_value(hotel_code, "Gateway rules by cobrador"):
                continue
            reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,
                                                                                   search_params=[
                                                                                       ('timestamp', '>=',
                                                                                        "2025-02-19 17:30:00")]))
        except:
            reservations = []
        for reservation in reservations:
            if not get_nothing_paid(json.loads(reservation.get('extraInfo'))):
                continue
            if avoid(reservation.get('identifier')):
                continue
            reservation_rate_key = reservation.get('rate', '')
            reservation_rate_id = legacy_key_to_id(reservation_rate_key)
            rates = {x.id: x for x in get_using_entity_and_params('Rate', hotel_code=hotel_code)}
            if rates.get(reservation_rate_id, {}).get('cancellationPolicy', '') == "No cancelable":
                if reservas.get(hotel_code):
                    reservas[hotel_code].append(reservation.get('identifier'))
                else:
                    reservas[hotel_code] = [reservation.get('identifier')]
            pass

        pass
    pass

def avoid(identifier):
    if identifier in "{'flashhotelbenidorm': ['60277953'], 'cortijo-zahara': ['84054406'], 'port-benidorm': ['38577321'], 'hotansa-grand-pas': ['90947389'], 'port-jardin': ['68329703'], 'oasishoteles-grandpalm': ['R65892210', 'R32325940'], 'ibersol-torremolinosbeach': ['64191525'], 'port-elche': ['67215492', '64411301'], 'ap-sinerama': ['47E214A4E', '54CBE3C62'], 'oasishoteles-tulum': ['R42216030'], 'oasishoteles-smart': ['R76415520'], 'port-vista': ['46974754'], 'ibersol-almunecar': ['26438199'], 'q10-cuevas': ['31893821', '17211387', '26735425', '71591840', '53747669'], 'q10-etxarri': ['20707812'], 'q10-ampolla': ['95881629', '57734833', '36174984', '92901780', '79722961'], 'oasishoteles-pyramid': ['R96592630'], 'oasishoteles-grandcancun': ['R70270830', 'R86165950', 'R44429490', 'R24709840', 'R11565760'], 'oasishoteles-senstulum': ['R21742490'], 'port-azafata': ['64331019'], 'port-denia': ['78955955', '13892187', '89483277'], 'port-huerto': ['65453942', '59834315', '50161466', '52215502'], 'port-europa': ['16175428'], 'villa-flamenca': ['R76346297'], 'solvasa-valencia': ['66753338'], 'port-fiesta': ['15711265', '21653774', '30987060', '94904580'], 'taiga-valdevaqueros': ['80815275', '22039843', '87198981'], 'q10-conil': ['49635101', '21806577', '38832696'], 'taiga-dunas': ['40620356', '92096033', '75469553', '89294221', '58950016', '71804067', '80568146', '61723423'], 'port-alicante': ['41565401', '45300299', '40859562', '28997668'], 'puntazo-2': ['52544748']}":
        return True

def get_nothing_paid(extra_info):
    return not float(extra_info.get("payed", 0) or 0) and not extra_info.get("payed_by_tpv_link") and not extra_info.get('payed_by_cobrador')



def test_get_reservations_paid():
    hotel_code_filter = 'oasis'
    for hotel_code in get_all_hotels():
        if hotel_code_filter not in hotel_code:
            continue
        reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,
                                                                               search_params=[('timestamp', '>=', "2024-11-13"), ('timestamp', '<=', "2024-11-15")]))
        for r in reservations:
            ei = json.loads(r.get('extraInfo'))
            if ei.get('payed') and ei.get('payed') != '0' and float(ei.get('payed')) < 101:
                pass

def test_marcar_como_modificadas_reservas_con_link_de_pago():
    hotel_code_filter = 'blaumar-'
    resultado = {}
    blaumar = ['blaumar-magnolia', 'blaumar-dalies', 'blaumar-plaza', 'blaumar-acacias', 'blaumar-boella', 'blaumar-blaumar']
    for hotel_code in blaumar:
        if hotel_code_filter not in hotel_code:
            continue
        blaumar.append(hotel_code)
        reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code, search_params=[('timestamp', '>=', "2024-11-13")]))
        for r in reservations:
            modify = False
            if (not r.get('cancelled') and not r.get('modificationTimestamp')) and (r.get('startDate') > '2025-03-05'):
                payed_by_tpv_link = json.loads(r.get('extraInfo')).get('payed_by_tpv_link', {})
                if payed_by_tpv_link:
                    for p in payed_by_tpv_link:
                        if float(p.get('amount')):
                            modify = True
                            continue

            if modify:
                # modificar reserva
                r['modificationTimestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                datastore_communicator.save_to_datastore("Reservation", int(r.key.id), r, hotel_code=hotel_code)
                if resultado.get(hotel_code):
                    resultado[hotel_code].append(r.get('identifier'))
                else:
                    resultado[hotel_code] = [r.get('identifier')]
                pass

                pass
    pass

def test_reservas_que_en_desglose_de_suplementos_no_tienen_impuestos():
    hotel_code_filter = 'best-'
    resultado = {}
    hoteles = ['best-lloret-splash']
    for hotel_code in get_all_hotels():
        if hotel_code_filter not in hotel_code and 'serenade' not in hotel_code:
        # if hotel_code_filter not in hotel_code and 'serenade' not in hotel_code:
            continue

        reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code, search_params=[('timestamp', '>=', "2025-01-01")]))
        # hoteles.append(hotel_code)
        for r in reservations:
            if r.get('cancelled'):
                continue
            precio_total_suplementos_calculado = 0
            # if r.get('startDate') > "2025-03-07" and not r.get('cancelled'):
            if float(r.get('priceSupplements', 0)):
                precios = [(x.split('precio: ')[-1]) for x in r.get('additionalServices').strip().split(';')]
                for p in precios:
                    if p != '':
                        precio_total_suplementos_calculado += float(p)
            ei = json.loads(r.get('extraInfo'))
            precio_total = "%.2f" %float(float(r.get('price')) + precio_total_suplementos_calculado)
            payed = "%.2f" % float(ei.get('payed', 0))
            if float(ei.get('payed', 0)) and precio_total != payed:
                if "Price modified from" in r.get('extraInfo'):
                    continue
                if float(precio_total) * 0.95 == float():
                    pass

                if resultado.get(hotel_code):
                    resultado[hotel_code][r.get('identifier')] = f"{precio_total} - {payed}"
                else:
                    resultado[hotel_code] = {r.get('identifier'): f"{precio_total} - {payed}"}
                pass

                # if float(r.get('priceSupplements', 0)) != precio_total_suplementos_calculado:
                #     r['priceSupplements'] = str(precio_total_suplementos_calculado)
                #     r['modificationTimestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                #
                #     # datastore_communicator.save_to_datastore("Reservation", int(r.key.id), r, hotel_code=hotel_code)
                #
                #     if resultado.get(hotel_code):
                #         resultado[hotel_code].append(r.get('identifier'))
                #     else:
                #         resultado[hotel_code] = [r.get('identifier')]
                #     pass
        pass
    pass

def test_for_omni():
    resultado = {}
    for hotel_code in ['test-backend10']:

        # hoteles.append(hotel_code)
        reservations = datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,

                                                                               search_params=[('timestamp', '>=',
                                                                                               "2025-01-01")])
        for r in reservations:
            format = "%Y-%m-%d"
            start = datetime.strptime(r.get('startDate'), format)
            today = datetime.strptime(datetime.now().strftime("%Y-%m-%d"), format)
            days_to_start = (start - today).days
            text_to_add = ''
            if days_to_start > 7:
                text_to_add = f'[CHECKIN IN {days_to_start} DAYS]'

        pass
    pass

def test_reservas_afectadas_por_error_pago_programado():
    hotel_code_filter = 'portofino-'
    resultado = {}
    # hoteles = ['hotansa-marco-polo', 'hotansa-cervol', 'hotansa-grand-pas', 'hotansa-piolet', 'hotansa-pas', 'hotansa-encamp', 'hotansa-himalaia', 'hotansa-corpo', 'hotansa-font', 'hotansa-gothard', 'hotansa-camelot', 'hotansa-pui', 'hotansa-cat-ski', 'hotansa-magic-and', 'hotansa-ski', 'hotansa-shusski', 'hotansa-demo', 'hotansa-massana', 'hotansa-comptes']
    # hoteles = ['estival-torrequebrada']
    all_hotels = {}
    for hotel_code in get_all_hotels():
        x = {}
        eliminar = []
        if hotel_code_filter not in hotel_code:
        # if hotel_code_filter not in hotel_code and 'serenade' not in hotel_code:
            continue
        try:
            payments_reservations = datastore_communicator.get_using_entity_and_params('PaymentsReservation', hotel_code=hotel_code,
                                                                                   search_params=[('timestamp', '>=',
                                                                                                   "2025-03-23")])
        except:
            continue
        for payments_reservation in payments_reservations:
            identifier = payments_reservation.get('reservation_identifier')
            if payments_reservation.get('type') != 'programado':
                continue
            if x.get(identifier):
                x[identifier].append(payments_reservation.get('error') or 'OK')
            else:
                x[identifier] = [payments_reservation.get('error') or 'OK']
            pass
        for k, v in x.items():
            if v[-1] == 'OK':
                eliminar.append(k)
        for e in eliminar:
            x.pop(e)
        if x:
            all_hotels[hotel_code] = list(x.keys())
    pass

def test_reservas_dble_cobro_programado():
    # hotel_code_filter = 'prinsotel-'
    resultado = {}
    eliminar = []
    # hoteles = ['hotansa-marco-polo', 'hotansa-cervol', 'hotansa-grand-pas', 'hotansa-piolet', 'hotansa-pas', 'hotansa-encamp', 'hotansa-himalaia', 'hotansa-corpo', 'hotansa-font', 'hotansa-gothard', 'hotansa-camelot', 'hotansa-pui', 'hotansa-cat-ski', 'hotansa-magic-and', 'hotansa-ski', 'hotansa-shusski', 'hotansa-demo', 'hotansa-massana', 'hotansa-comptes']
    # hoteles = ['estival-torrequebrada']
    all_hotels = {}
    for hotel_code in get_all_hotels():
    # for hotel_code in ['prinsotel-dorada']:
        eliminar = []
        x = {}
        reservas_2_pagos = []
        # if hotel_code_filter not in hotel_code:
        # # if hotel_code_filter not in hotel_code and 'serenade' not in hotel_code:
        #     continue
        try:

            payments_reservations = datastore_communicator.get_using_entity_and_params('PaymentsReservation', hotel_code=hotel_code,
                                                                                   search_params=[('timestamp', '>=',
                                                                                                   "2025-03-23")])
        except:
            continue
        for payments_reservation in payments_reservations:
            identifier = payments_reservation.get('reservation_identifier')
            if payments_reservation.get('type') != 'programado' and payments_reservation.get('type') != "Envío de link al cliente":
                continue
            if x.get(identifier):
                x[identifier].append(payments_reservation)
            else:
                x[identifier] = [payments_reservation]
            pass
        for identifier, payments in x.items():
            pagos_hechos = 0
            timestamps = ' -- '
            for p in payments:
                if not p.get('error') and p.get('type') == 'programado':
                    pagos_hechos += 1
                    timestamps += ' - ' +p.get('timestamp')

            if pagos_hechos >= 1:
                reservation = datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,
                                                                   search_params=[('identifier', '=',
                                                                                   identifier)])[0]

                if json.loads(reservation.get("extraInfo")).get('payed_by_tpv_link'):
                    reservas_2_pagos.append(str(identifier))
        if reservas_2_pagos:
            all_hotels[hotel_code] = reservas_2_pagos
    pass

def test_reservas_sin_token():
    final = []
    identifiers = [ "31893821", "36016882", "75899094", "49635101", "75469553", "89294221", "80568146", "61723423" ]
    for identifier in identifiers:

        hotel_code, _, _ = get_real_hotel_reservation(identifier, 'test-backend10')
        reservation = datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,
                                                                         search_params=[('identifier', '=',
                                                                                         identifier)])[0]
        ei  =json.loads(reservation.get('extraInfo'))
        if ei.get('datatransdata'):
            # final.append(identifier)
            pass
        else:
            reservation['extraInfo'] =ei
            final.append(reservation)
        # if _get_total_payed_amount_from_all_sources(ei):
        #     final.append(identifier)

    pass


def test_():
    hotels = get_all_valid_hotels()
    hotels = [x.get('applicationId') for x in hotels]
    start_date = '2025-04-02'
    hotel_reservations = {}


    for hotel in hotels:
        integration_config = get_integration_configuration_of_hotel(hotel, 'siteminder')
        if integration_config and integration_config[0].get('downloadBooking') is True:

            payments_reservation = datastore_communicator.get_using_entity_and_params('PaymentsReservation', hotel_code=hotel,
                search_params=[('timestamp', '>=',
                                start_date)])

            for payment in payments_reservation:
                if payment.get('type') != 'Envío de link al cliente' or payment.get('error'):
                    continue
                reservation_identifier = payment.get('reservation_identifier')
                reservation = datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel,
                    search_params=[('identifier', '=',
                                    reservation_identifier)])[0]
                ei = json.loads(reservation.get('extraInfo'))
                if ei.get('payed_by_tpv_link') and not ei.get('payed_by_tpv_link')[0].get('datatransdata'):
                    if hotel_reservations.get(hotel):
                        hotel_reservations[hotel].append(reservation_identifier)
                    else:
                        hotel_reservations[hotel] = [reservation_identifier]
    return hotel_reservations


def test_reservations_with_room_added_posteriori():
    # Obtener todos los hoteles válidos
    hotels = get_all_valid_hotels()
    # Filtrar solo hoteles Park Royal
    parkroyal_hotels = [x.get('applicationId') for x in hotels if 'parkroyal' in x.get('applicationId', '').lower()]
    
    start_date = '2024-07-01'
    all_reservations = {}
    
    for hotel_code in parkroyal_hotels:
        reservations = datastore_communicator.get_using_entity_and_params('Reservation', 
            hotel_code=hotel_code,
            search_params=[
                ('timestamp', '>=', start_date)
            ])
        
        reservations_with_room_added = []
        i = 0
        for reservation in reservations:
            ei = json.loads(reservation.get('extraInfo', '{}'))
            if 'ROOM ADDED' in str(ei):
                reservations_with_room_added.append(reservation.get('identifier'))
            i += 1
        
        if reservations_with_room_added:
            all_reservations[hotel_code] = reservations_with_room_added
    
    return all_reservations

def test_arreglar_reservas_con_None_en_extraInfo():
    hotels = get_all_valid_hotels()
    hotels = [x.get('applicationId') for x in hotels]
    modified_reservations_by_hotel = {}
    for hotel in hotels:
        reservations = datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel)
        modified_reservations = []
        for r in reservations:
            if not r.get('extraInfo'):
                continue
            ei = json.loads(r.get('extraInfo', '{}'))
            payed_by_tpv_link = ei.get('payed_by_tpv_link', [])
            modified = False
            if payed_by_tpv_link:
                for p in payed_by_tpv_link:
                    if p.get('datatransdata') and not (p.get('amount') or p.get('amount') == 0.0):
                        p['amount'] = 0
                        modified = True
            if modified:
                r['extraInfo'] = json.dumps(ei)
                datastore_communicator.save_to_datastore('Reservation', r.id, r, hotel_code=hotel)
                modified_reservations.append(r.get('identifier'))
        if modified_reservations:
            modified_reservations_by_hotel[hotel] = modified_reservations
    return modified_reservations_by_hotel


def test_reservas_con_SIBS_MULTIBANCO_en_extraInfo():
    hotels = get_all_valid_hotels()
    hotels = [x.get('applicationId') for x in hotels if 'azoris' in x.get('applicationId', '').lower()]
    current_month = datetime.now().strftime("%Y-%m")
    for hotel in hotels:
        reservations = datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel,
            search_params=[
                ('timestamp', '>=', f"{current_month}-01")
            ])
        for r in reservations:
            ei = json.loads(r.get('extraInfo', '{}'))
            if ei.get('SIBS_MULTIBANCO'):
                pass


def reservas_de_prinsotel_de_los_ultimos_30_dias_que_tengan_payed_por_tpv_link():
    hotels = get_all_valid_hotels()
    hotels = [x.get('applicationId') for x in hotels if 'prinsotel' in x.get('applicationId', '').lower()]
    reservas_con_payed_por_tpv_link = {}
    for hotel in hotels:
        reservations = datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel)
        identifiers = []
        for r in reservations:
            ei = json.loads(r.get('extraInfo', '{}'))
            if ei.get('payed_by_tpv_link'):
                identifiers.append(f"{r.get('identifier')} - {r.get('startDate')}")
        if identifiers:
            reservas_con_payed_por_tpv_link[hotel] = identifiers
    return reservas_con_payed_por_tpv_link


def reservas_de_hotel_code_ap_con_SIBS_MULTIBANCO_en_extraInfo_y_estado_pending():
    hotels = get_all_valid_hotels()
    hotels = [x.get('applicationId') for x in hotels if 'ap-' in x.get('applicationId', '').lower()]
    today = datetime.now().date()
    reservas_por_hotel = {}
    total_reservas = 0
    
    for hotel in hotels:
        reservations = datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel)
        identifiers = []
        for r in reservations:
            if r.get('cancelled'):
                continue
            start_date = datetime.strptime(r.get('startDate'), '%Y-%m-%d').date()
            if start_date > today:
                continue
            extra_info = r.get('extraInfo')
            if not extra_info:
                continue
            try:
                ei = json.loads(extra_info)
                # if _get_total_payed_amount_from_all_sources(ei):
                #     continue
                if ei.get('SIBS_MULTIBANCO') and ei.get('SIBS_MULTIBANCO').get('status') == 'pending':
                    identifiers.append(f"{r.get('identifier')} - {r.get('startDate')} - {r.get('timestamp')}")
            except (json.JSONDecodeError, TypeError):
                continue
        
        if identifiers:
            reservas_por_hotel[hotel] = identifiers
            total_reservas += len(identifiers)
    pass

ERROR_OMNIBEES_DATE = "OTA_CancelRQ.Verification.ReservationTimeSpan.EffectiveDate"
SYSTEM_ERROR = "System error: Err_SystemError"
    
def test_omni():
    hotel_code = "parkroyal-beachcancun"
    identifier = "61532087"
    error = ''
    response = None
    request_body = {}
    response = {'result': [{'Language': 1, 'ShortText': 'Start date is invalid: OTA_CancelRQ.Verification.ReservationTimeSpan.EffectiveDate = 2025/04/18 is not a valid date', 'Code': 136, 'RPH': None, 'Type': None}], 'status': 500}
    send_error_email = not response or response.get('status') == 500
    if send_error_email:
        email_error_subject = f"RESERVATION WITH PROBLEM IN OMNIBEES ADAPTER: {hotel_code} - {identifier}"
        email_error_message = error if error else 'Unexpected error'
        if response:
            logging.info(f'response: {response}')
            warning_text = response.get('result', {}).get('ShortText') if isinstance(response.get('result', {}), dict) else response.get('result',{})
            error_text = response.get('ErrorsType', {}).get('Errors', [{}])[0].get('ShortText')
            if SYSTEM_ERROR in (error_text or warning_text):
                return
            email_error_message = warning_text or email_error_message
       
        if request_body:
            email_error_message += "\n\n" + str(request_body)
        if ERROR_OMNIBEES_DATE in email_error_message:
            return
        logging.error(email_error_message)
        logging.error(email_error_subject)

        emails_to_add = ";<EMAIL>;<EMAIL>"
        notify_error_by_email(email_error_subject, email_error_message, emails_to_add=emails_to_add)


def notify_error_by_email(subject, message, emails_to_add=None):
    emails = emails_to_add
    # if emails_to_add:
    #     emails += emails_to_add
    try:
        sendEmail(emails, subject, "", message)
    except:
        sendEmail_backup(emails, "DEFAULT_TITLE", "", message)


import zlib
import json

def compress_data(data: str) -> bytes:
    """
    Compress string data using zlib with maximum window bits.
    
    Args:
        data (str): The string data to compress
        
    Returns:
        bytes: The compressed data
    """
    return zlib.compress(data.encode(), level=zlib.Z_BEST_COMPRESSION)

def compress_json(data: dict) -> bytes:
    """
    Compress JSON data similar to ndb.JsonProperty(compressed=True).
    First converts the dict to JSON string, then compresses it.
    
    Args:
        data (dict): The dictionary/JSON data to compress
        
    Returns:
        bytes: The compressed JSON data
    """
    json_str = json.dumps(data)
    x=  compress_data(json_str)
    return compress_data(json_str)


def decompress_json(compressed_data: bytes) -> dict:
    """
    Decompress JSON data that was compressed with compress_json.
    
    Args:
        compressed_data (bytes): The compressed JSON data
        
    Returns:
        dict: The decompressed JSON data
    """
    decompressed_str = zlib.decompress(compressed_data, zlib.MAX_WBITS|16).decode()
    return json.loads(decompressed_str)

def test_encontrar_pagos_en_prinsotel_que_no_sean_numericos():
    """
    Busca pagos en todos los hoteles de Prinsotel donde el identificador no sea numérico.
    Ignora pagos con errores (amount = None).
    """
    # Diccionario para almacenar resultados por hotel
    resultados_por_hotel = {}

    hotels = get_all_valid_hotels()
    hotels = [x.get('applicationId') for x in hotels if 'prinsotel-' in x.get('applicationId', '').lower()]
    
    for hotel_code in hotels:
        print(f"\nRevisando hotel: {hotel_code}")
        payments = datastore_communicator.get_using_entity_and_params('PaymentsReservation', hotel_code=hotel_code)
        
        # Lista para almacenar los pagos problemáticos de este hotel
        pagos_problematicos = []
        
        for payment in payments:
            # Ignorar pagos con errores
            if payment.get('error'):
                continue
                
            identifier = payment.get('order')
            if identifier:
                # Verificar si el identificador no es numérico
                if not str(identifier).isdigit() and not "CC" in str(identifier) and not payment.get('type') == 'Envío de link al cliente' and payment.get('timestamp') > '2025-01-01':
                    pagos_problematicos.append({
                        'payment_id': payment.get('id'),
                        'identifier': identifier,
                        'amount': payment.get('amount'),
                        'date': payment.get('date'),
                        'reservation_identifier': payment.get('reservation_identifier'),
                        'type': payment.get('type'),
                        'user_id': payment.get('user'),
                    })
        
        # Si encontramos pagos problemáticos en este hotel, los agregamos al diccionario
        if pagos_problematicos:
            resultados_por_hotel[hotel_code] = pagos_problematicos
    
    # Imprimir resultados organizados

    
    return resultados_por_hotel


def test_brl():
    payments = datastore_communicator.get_using_entity_and_params('BookingRequestLog', hotel_code="admin-hotel", search_params=[('identifier', '=', 'R96116772')])

    pass


def compare_date_ranges(date_string):
    from datetime import datetime, timedelta
    
    # Extract dates using string manipulation
    old_dates = date_string.split('Old dates: ')[1].split(' <br>')[0]
    new_dates = date_string.split('New dates: ')[1]
    
    # Parse the dates
    old_start = datetime.strptime(old_dates.split(' - ')[0], '%d/%m/%Y')
    old_end = datetime.strptime(old_dates.split(' - ')[1], '%d/%m/%Y')
    new_start = datetime.strptime(new_dates.split(' - ')[0], '%d/%m/%Y')
    new_end = datetime.strptime(new_dates.split(' - ')[1], '%d/%m/%Y')
    
    # Generate lists of dates
    old_dates_list = []
    new_dates_list = []
    
    current = old_start
    while current <= old_end:
        old_dates_list.append(current.date())
        current += timedelta(days=1)
        
    current = new_start
    while current <= new_end:
        new_dates_list.append(current.date())
        current += timedelta(days=1)
    
    # Check if all new dates are in old dates
    return all(date in old_dates_list for date in new_dates_list)


def test_3():
    string = 'Old dates'
    hotels = get_all_valid_hotels()
    hotels = [x.get('applicationId') for x in hotels if 'parkroya' in x.get('applicationId', '').lower()]
    
    matching_reservations = {}
    for hotel in hotels:
        matching_reservations[hotel] = []
        reservations_mt = datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel,
            search_params=[('modificationTimestamp', '>=', '2025-05-06')])
        for reservation in reservations_mt:
            ei = json.loads(reservation.get('extraInfo'))
            if ei.get('history'):
                for h in ei.get('history'):
                    if h.get('timestamp') > '2025-05-06' and string in h.get('change'):
                        if compare_date_ranges(h.get('change')):
                            matching_reservations[hotel].append(reservation.get('identifier'))
                            break
            pass
    
    print(matching_reservations)
    return matching_reservations

def reservas_de_prinsotel_que_tienen_envio_de_link_en_PaymentsReservation_y_no_tienen_payed_by_tpv_link_en_extraInfo():
    hotels = get_all_valid_hotels()
    hotels = [x.get('applicationId') for x in hotels if 'prinsotel-' in x.get('applicationId', '').lower() and 'demo' not in x.get('applicationId', '').lower()]

    results_before = {}
    results_after = {}
    total_before = 0
    total_faltante = 0
    total_after = 0
    today = datetime.now().date()
    
    for hotel in hotels:
        results_before[hotel] = []
        results_after[hotel] = []
        payments = datastore_communicator.get_using_entity_and_params('PaymentsReservation', hotel_code=hotel)
        for payment in payments:
            if payment.get('type') == 'Envío de link al cliente':
                # Check if link was sent more than 24 hours ago
                link_timestamp = datetime.strptime(payment.get('timestamp'), '%Y-%m-%d %H:%M:%S')
                # if (datetime.now() - link_timestamp).total_seconds() < 86400:  # 86400 seconds = 24 hours
                #     continue

                # Get the associated reservation
                reservation = datastore_communicator.get_using_entity_and_params('Reservation', 
                    hotel_code=hotel,
                    search_params=[('identifier', '=', payment.get('reservation_identifier'))])
                
                if reservation and reservation[0].get('extraInfo'):
                    ei = json.loads(reservation[0].get('extraInfo'))
                    if not ei.get("status_reservation") == "pending" or reservation[0].get('cancelled'):
                        continue
                    if not ei.get('payed_by_tpv_link'):
                        pagado = _get_total_payed_amount_from_all_sources(ei)
                        precio_total = float(reservation[0].get('price')) + float(reservation[0].get('priceSupplements'))
                        start_date = datetime.strptime(reservation[0].get('startDate'), '%Y-%m-%d').date()
                        end_date = datetime.strptime(reservation[0].get('endDate'), '%Y-%m-%d').date()
                        reservation_data = {
                            'identifier': payment.get('reservation_identifier'),
                            'reservation_timestamp': reservation[0].get('timestamp'),
                            'link_timestamp': payment.get('timestamp'),
                            'payed': ei.get('payed'),
                            'payed_by_cobrador': ei.get('payed_by_cobrador'),
                            'start_date': reservation[0].get('startDate'),
                            'end_date': reservation[0].get('endDate'),
                            'customer_name': f"{reservation[0].get('name', '')} {reservation[0].get('surname', '')}".strip(),
                            'pagado': pagado,
                            'precio_total': precio_total,
                            'faltante': precio_total - pagado,
                        }
                        
                        if end_date < today:
                            results_before[hotel].append(reservation_data)
                            total_before += 1
                            total_faltante += pagado - precio_total
                        else:
                            results_after[hotel].append(reservation_data)
                            total_after += 1
                            
    
    print(f"Total de reservas con fecha anterior a hoy: {total_before}")
    print("Reservas con fecha anterior a hoy:")
    print(results_before)
    print(f"\nTotal de reservas con fecha posterior a hoy: {total_after}")
    print("Reservas con fecha posterior a hoy:")
    print(results_after)
    print(f"\nTotal de faltante: {total_faltante}")
    
    return {
        'before_today': results_before,
        'after_today': results_after
    }

# reservas_de_prinsotel_que_tienen_envio_de_link_en_PaymentsReservation_y_no_tienen_payed_by_tpv_link_en_extraInfo()

def check_hotels_with_siteminder():
    import csv
    from paraty.pages.agencies.agencies_utils import get_integration_configuration
    
    # Read unique hotel codes from CSV
    hotel_codes = set()
    with open('src/paraty/pages/reservations/bquxjob_44710475_196af902d94.csv', 'r') as file:
        csv_reader = csv.DictReader(file)
        for row in csv_reader:
            if row['hotel_code'] and row['hotel_code'] != 'KO':
                hotel_codes.add(row['hotel_code'])
    
    # Check which hotels have siteminder configured
    hotels_with_siteminder = {}
    for hotel_code in hotel_codes:
        config = get_integration_configuration(hotel_code, 'siteminder')
        if config:
            hotels_with_siteminder[hotel_code] = config
    
    print(f"Total de hoteles únicos en el CSV: {len(hotel_codes)}")
    print(f"Hoteles con siteminder configurado: {len(hotels_with_siteminder)}")
    print("\nHoteles con siteminder:")
    for hotel, config in hotels_with_siteminder.items():
        print(f"- {hotel}: {config}")
    
    return hotels_with_siteminder

# check_hotels_with_siteminder()

def test_obtener():
    from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels
    from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel
    import xml.etree.ElementTree as ET
    
    hotels = get_all_valid_hotels()
    hotels_with_config = []
    
    for hotel in hotels:
        hotel_code = hotel.get('applicationId')
        if not hotel_code:
            continue
        # if 'prinsotel' not in hotel_code:
        #     continue
            
        configs = get_integration_configuration_of_hotel(hotel, "PEP_PAYLINKS COBRADOR")
        if not configs:
            continue
            
        for config in configs:
            for config_xml in config.get('configurations', ''):
                config_xml = config_xml.split(" @@ ")[0].strip()
                if config_xml == "allow automatic cancellation with pending payment":
                    hotels_with_config.append(hotel_code)
                    break

    
    print(f"Total de hoteles válidos: {len(hotels)}")
    print(f"Hoteles con configuración de cancelación automática: {len(hotels_with_config)}")
    print("\nHoteles con la configuración:")
    for hotel in hotels_with_config:
        print(f"- {hotel}")
    
    return hotels_with_config
def test_get_hc():
    r = ''
    hotels = get_all_valid_hotels()
    hotels_with_config =[]

    for hotel in hotels:
        hotel_code = hotel.get('applicationId')
        if not hotel_code:
            continue
        if 'ms-' in hotel_code and hotel_code not in r and not 'soho' in hotel_code:
            hotels_with_config.append(hotel_code)

    pass

def test_reservations_paypal_v2():
pass

def test_logs():
    from google.cloud import logging
    from datetime import datetime, timedelta

    # Inicializar el cliente
    client = logging.Client('integration-test-hotel')

    # Definir el filtro (por ejemplo, últimos 24h para una app GAE)
    project_id = "integration-test-hotel"
    time_limit = datetime.utcnow() - timedelta(days=1)

    filter_str = f'''
    resource.type="gae_app"
    timestamp>="{time_limit.isoformat()}Z"
    '''

    # Consultar logs
    entries = client.list_entries(filter_=filter_str)

    # Mostrar resultados
    for entry in entries:
        print(f"{entry.timestamp}: {entry.payload}")

# obtener()