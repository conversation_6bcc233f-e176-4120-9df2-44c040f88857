import logging

from paraty.utilities.countries.configs.country_configs_utils import _get_country_info
from paraty.utilities.manager_utils import get_configuration_property_value, integration_configuration_to_dict
from paraty.utilities.taxes.taxes_constant import HOTEL_COUNTRY_LOCATION, HOTEL_COUNTRY_LOCATION_TAX_INCLUDED, \
    COUNTRY_LOCATION_CUSTOM_TAX
from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel


def tax_is_included_in_price(extra_info):
    """
    Check whether the VAT should be included in final price. Possible cases:
    1. Default: included
    2. HCL + PTAH - not included
    """
    price_info = extra_info.get('price_info', {})
    taxes = price_info.get('taxes', {})
    country = taxes.get('country', {})

    return country.get('included', '') == True


def tax_is_included_in_base_price(hotel_code):
    """
    Check whether the VAT is already included in price that comes from manager. Possible cases:
    1. Default: included
    2. HCL only: according to country config
    3. HCL + HCLTI: included
    """

    hotel_country_location = get_configuration_property_value(hotel_code, HOTEL_COUNTRY_LOCATION)

    if hotel_country_location:
        country_info = _get_country_info(hotel_country_location)
        tax_not_included_json = country_info.get('general_dict', {}).get('tax_not_included')

        hotel_country_location_tax_included = get_configuration_property_value(hotel_code, HOTEL_COUNTRY_LOCATION_TAX_INCLUDED)
        if tax_not_included_json == 'True' and not hotel_country_location_tax_included:
            return False

    return True


def get_tax_percentage(hotel_code):
    tax_percentage = 21

    hotel_country_location = get_configuration_property_value(hotel_code, HOTEL_COUNTRY_LOCATION)
    if hotel_country_location:
        # country_location_config = _get_country_info(hotel_country_location)
        custom_tax = get_configuration_property_value(hotel_code, COUNTRY_LOCATION_CUSTOM_TAX)
        if custom_tax:
            tax_percentage = float(custom_tax.replace("%", ""))

    return tax_percentage


def get_price_with_taxes(price, price_info):
    taxes = price_info.get('taxes', {})
    country = taxes.get('country', {})
    rooms = country.get('rooms', {})
    if rooms:
        price += rooms.get('value', 0)


def get_supplement_price_with_taxes(price_info):
    taxes = price_info.get('taxes', {})
    country = taxes.get('country', {})
    supplements = country.get('supplements', {})
    supplement_price = 0
    if supplements:
        for supplement_key, supplement in supplements.items():
            percentage = supplement.get('percentage', 0)
            value = supplement.get('value', 0)

            if percentage > 0:
                supplement_net_price = value / (percentage / 100)
                supplement_price += (supplement_net_price + value)

    return supplement_price


def get_special_tax_for_suplements(hotel_code):
    result = {}

    try:
        integration_config = get_integration_configuration_of_hotel({'applicationId': hotel_code}, 'SPECIAL TAXES')
        if integration_config:
            integration_config = integration_configuration_to_dict(integration_config[0])
            for supplement_key, supplement_tax in integration_config.get('extraMap', {}).items():
                result[supplement_key] = float(supplement_tax.replace("%", ""))
    except Exception as e:
        logging.error("Error in SPECIAL TAXES xml config")
        logging.error(e)

    return result


def remove_tax_from_price(hotel_code, price, tax_percentage=None):
    if not tax_percentage:
        tax_percentage = get_tax_percentage(hotel_code)

    tax_percentage = float(tax_percentage) / 100
    tax_percentage = tax_percentage + 1

    return float(price) / tax_percentage


def add_tax_to_price(hotel_code, price, tax_percentage=None):
    if not tax_percentage:
        tax_percentage = get_tax_percentage(hotel_code)

    tax_percentage = float(tax_percentage) / 100
    tax_percentage = tax_percentage + 1

    return float(price) * tax_percentage