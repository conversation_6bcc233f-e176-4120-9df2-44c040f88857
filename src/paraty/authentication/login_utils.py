import json
import logging
from functools import wraps
from urllib.parse import urlparse, parse_qs

import flask
from flask import request, Response

from werkzeug.utils import redirect

from paraty import app, Config
from paraty.communications import datastore_communicator

from paraty.utilities import session_utils, manager_utils, hotel_utils, encryption_utils, date_utils
from paraty.utilities.languages import language_utils
from paraty.utilities.languages.language_utils import SPANISH, PORTUGUESE
from paraty.utilities.session_utils import UserSession
from paraty.utilities.templates.templates_processor import build_template




def extract_params_from_request(my_request):
    full_body = {}
    try:
        body = my_request.get_json()
    except:
        body = None
    if body:
        full_body.update(body)

    # try get params by POST
    try:
        body = dict(my_request.form)
    except:
        body = None

    if body:
        full_body.update(body)
   # try get params by GET
    try:
        body = dict(my_request.args)
    except:
        body = None

    if body:
        full_body.update(body)


    # new try:
    try:
        body = dict(my_request.values)
    except:
        body = None

    if body:
        full_body.update(body)



    return full_body

def _dev_load_session():
    session_utils.set_current_session(UserSession({}, session_id='TEST_SESSION'))
    session_utils.get_current_session().set_value("hotel_code", 'parkroyal-loscabos')
    session_utils.get_current_session().set_value("language", SPANISH)
    session_utils.get_current_session().set_value('user_name', 'nhaunstetter')


def login_from_sessionKey(func):

    @wraps(func)
    def decorated_view(*args, **kwargs):

        request_params = extract_params_from_request(request)

        session_key = request_params.get('sessionKey')
        language = request_params.get("language")

        not_authorized = _login_using_url_params(session_key, language)
        if not_authorized:
            logging.info("Not authorized login for sessionKey %s" % session_key)

            if _login_from_webseeker():
                return func(*args, **kwargs)

            if _login_from_hotelwebs():
                return func(*args, **kwargs)
            
            return not_authorized

        return func(*args, **kwargs)

    return decorated_view


def _login_using_url_params(session_key, language):

    if app.config['DEV']:
        _dev_load_session()
        return

    if not session_key:
        return unauthorized()

    my_session = session_utils.get_session_from_datastore(session_key)

    if not my_session:
        return unauthorized()

    hotel_application = datastore_communicator.get_entity("HotelApplication", my_session.get_value('currentAppId'))
    hotel_code = hotel_application.get('applicationId')

    session_utils.get_current_session().set_value("hotel_code", hotel_code)
    session_utils.get_current_session().set_value("user_name", my_session.content['userName'])
    session_utils.get_current_session().set_value("language", language_utils.get_language_in_manager_based_on_locale(language))


def _login_from_webseeker():
    authorization_header = request.headers.get('Authorization')
    if authorization_header:
        if authorization_header == Config.WEBSEEKER_AUTHORIZATION_KEY:
            return True
    return False


def _login_from_hotelwebs():
    session_key = request.values.get('sessionKey')
    if session_key and session_key == Config.HOTELWEBS_SESSION_KEY:
        return True
    return False


def login_using_referrer(func):
    @wraps(func)
    def decorated_view(*args, **kwargs):

        referer = request.headers.get("Referer")

        #Sometimes it is easier to set it
        if not referer:
            referer = request.values.get('referer')


        logging.info(referer)
        logging.info(request.url)

        query_params = parse_qs(urlparse(referer).query)
        session_key = query_params.get('sessionKey', [''])[0]
        language = query_params.get('language', [''])[0]

        if (not session_key and not language) and hasattr(session_utils.get_current_session(),
                                                          "is_authenticated") and session_utils.get_current_session().is_authenticated:
            return func(*args, **kwargs)


        _login_using_url_params(session_key, language)

        return func(*args, **kwargs)

    return decorated_view



# @logged_call This logs the request and the status code returned
@app.route("/has_access_to_cc", methods=['POST'])
def user_has_access_to_cc():
    try:
        logging.info("Entering user_has_access_to_cc")
        body = request.get_json()

        username = body['user']
        password_cc = body['password_cc']
        hotel_code = body['hotel_code']

        current_user = list(datastore_communicator.get_using_entity_and_params('ParatyUser', [('name', '=', username)]))
        if not current_user or not current_user[0]['enabled']:
            logging.info("Access denied, user not found: %s", username)
            return flask.Response('Access not allowed', status=401)

        if not hotel_code in manager_utils.get_hotel_codes_from_ids(current_user[0]['accesibleApplications']) and not 'admin' in current_user[0]['permission']:
            logging.info("Access denied, hotel code not accesible for the given user: %s, %s", username, hotel_code)
            return flask.Response('Access not allowed', status=401)

        hotel_cc_password = hotel_utils.get_config_property_value(hotel_code, 'Password Tarjetas')

        if hotel_cc_password != password_cc:
            logging.info("Access denied, wrong password_cc provided")
            return flask.Response('Access not allowed', status=401)

        return 'OK'
    except Exception as e:
        logging.error("Fatal error in main loop", exc_info=True)

@app.route("/user/change_password", methods=['GET'])
@login_from_sessionKey
def show_password_change_window():
    return build_template('login/index.html', {'session_key': request.args.get('sessionKey')})

@app.route("/user/change_password", methods=['POST'])
@login_using_referrer
def change_user_password():

    username = session_utils.get_current_session().get_value("user_name")
    old_password = request.values['old_password']
    new_password_1 = request.values['new_password_1']
    new_password_2 = request.values['new_password_2']

    if new_password_1 != new_password_2:
        return flask.Response("New passwords don't match", status=400)

    if len(new_password_1) < 8:
        return flask.Response("Min length 8", status=400)

    num_digits = len([x for x in new_password_1 if x.isdigit()])
    if num_digits == 0 or num_digits == 8:
        return flask.Response("Password must contain numbers and letters", status=400)

    affected_user = list(datastore_communicator.get_using_entity_and_params('ParatyUser', [('name', '=', username)]))

    if not affected_user:
        return flask.Response('User/Password incorrect', status=404)

    expected_password = affected_user[0]['password']

    # if encryption_utils.hash_to_md5(old_password) != expected_password:
    #     return flask.Response('User/Password incorrect', status=404)

    affected_user[0]['password'] = encryption_utils.hash_to_md5(new_password_1)
    affected_user[0]['lastPasswordChange'] = date_utils.get_timestamp()

    datastore_communicator.save_entity(affected_user[0])

    return 'OK'


def unauthorized():
    """Redirect unauthorized users to Login page."""

    return Response('Unauthorized access', 404)

